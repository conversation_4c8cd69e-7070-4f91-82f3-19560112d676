﻿using Microsoft.AspNetCore.Http;

namespace Subhlagan.Application.Authentication
{
    public static partial class KsAuthenticationDefaults
    {
        public static string AuthenticationScheme => "Authentication";

        public static string ExternalAuthenticationScheme => "ExternalAuthentication";

        public static string ClaimsIssuer => "kiranaStore";

        public static PathString LoginPath => new("/login");
        
        public static PathString AccessDeniedPath => new("/page-not-found");

    }
}