﻿using System.Collections.Generic;

namespace Subhlagan.Application.Messages
{
    public static partial class SubhMessageTemplateSystemNames
    {
        public const string CustomerPasswordRecoveryMessage = "Customer.PasswordRecovery";
        public const string ProfileRegistrationFormMessage = "SubhLagan.Profile.RegistrationForm";
        public const string ProfileMessageNotification = "SubhLagan.Profile.MessageNotification";
        public const string ProfileWelcomeMessage = "SubhLagan.Profile.WelcomeMessage";
        public const string ProfileMeetingScheduleMessage = "SubhLagan.Profile.MeetingScheduleMessage";
        public const string ProfileRelationshipManagerMessage = "SubhLagan.Profile.RelationshipManagerMessage";
        public const string MatchKundaliDetailsMessage = "SubhLagan.MatchKundali.Details";
        public const string ProfileActivationMessage = "SubhLagan.Profile.ActivationMessage";
        public const string ProfileContactExchangeMessage = "SubhLagan.Profile.ContactExchange";
        public const string MatchProfilesMessage = "SubhLagan.Profile.MatchProfilesMessage";
        public const string MatchAcceptedMessage = "SubhLagan.Profile.MatchAccepted";
        public const string ProfileIntimationRegistrationMessage = "SubhLagan.Profile.IntimatioRegistration";
        public const string ProfileRegistrationReceiptMessage = "SubhLagan.Profile.RegistrationReceipt";
        public const string ProfileCongratulationsMessage = "SubhLagan.Profile.CongratulationsMessage";
        public const string ProfileLoginDetailsMessage = "SubhLagan.Profile.LoginDetails";
        public const string ProfileSelfBioDataMessage = "SubhLagan.Profile.BioData";
        public const string ProfileBirthdayMessage = "SubhLagan.Profile.BirthdayGreeting";
        public const string ClientMatchAcceptedNotificationRM = "SubhLagan.Profile.ClientMatchAcceptedNotificationRM";
        public const string ClientMatchAcceptedNotificationToRM = "SubhLagan.Profile.ClientMatchAcceptedNotificationToRM";
        public const string ProfileDailyReportMessage = "SubhLagan.Profile.DailyReport";
        public const string FinancialPdfMessage = "SubhLagan.Profile.FinancialPdf";

        public static List<string> GetAllMessageTemplates()
        {
            return new List<string>
            {
                ProfileRegistrationFormMessage,
                ProfileMessageNotification,
                ProfileWelcomeMessage,
                ProfileMeetingScheduleMessage,
                ProfileRelationshipManagerMessage,
                MatchKundaliDetailsMessage,
                ProfileActivationMessage,
                ProfileContactExchangeMessage,
                MatchProfilesMessage,
                MatchAcceptedMessage,
                ProfileIntimationRegistrationMessage,
                ProfileRegistrationReceiptMessage,
                ProfileCongratulationsMessage,
                ProfileLoginDetailsMessage,
                ProfileSelfBioDataMessage,
                ProfileBirthdayMessage,
                ClientMatchAcceptedNotificationRM,
                ProfileDailyReportMessage,
                FinancialPdfMessage
            };
        }

        public static List<string> GetAllMatchingMessageTemplates()
        {
            return new List<string>
            {
                ProfileMeetingScheduleMessage,
                MatchKundaliDetailsMessage,
                ProfileContactExchangeMessage,
                MatchProfilesMessage,
                MatchAcceptedMessage,
                ProfileCongratulationsMessage,
                ClientMatchAcceptedNotificationRM,
                FinancialPdfMessage,
            };
        }
    }
}
