# =====================================================================
# Install SQL Server PowerShell Module
# Purpose: Install required dependencies for database analysis
# =====================================================================

Write-Host "Installing SQL Server PowerShell Module..." -ForegroundColor Green

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  Warning: Not running as administrator. Installing for current user only." -ForegroundColor Yellow
    Write-Host "   For system-wide installation, run PowerShell as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

try {
    # Set execution policy for current user if needed
    Write-Host "1. Setting execution policy..." -ForegroundColor Cyan
    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
    
    # Install NuGet provider if needed
    Write-Host "2. Installing NuGet provider..." -ForegroundColor Cyan
    Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force -Scope CurrentUser
    
    # Set PSGallery as trusted repository
    Write-Host "3. Setting PSGallery as trusted repository..." -ForegroundColor Cyan
    Set-PSRepository -Name PSGallery -InstallationPolicy Trusted
    
    # Install SQL Server module
    Write-Host "4. Installing SqlServer module (this may take a few minutes)..." -ForegroundColor Cyan
    if ($isAdmin) {
        Install-Module -Name SqlServer -Force -AllowClobber
    } else {
        Install-Module -Name SqlServer -Force -AllowClobber -Scope CurrentUser
    }
    
    # Import the module
    Write-Host "5. Importing SqlServer module..." -ForegroundColor Cyan
    Import-Module SqlServer -Force
    
    # Verify installation
    Write-Host "6. Verifying installation..." -ForegroundColor Cyan
    $module = Get-Module -Name SqlServer
    if ($module) {
        Write-Host "✅ SqlServer module installed successfully!" -ForegroundColor Green
        Write-Host "   Version: $($module.Version)" -ForegroundColor Gray
        Write-Host "   Path: $($module.ModuleBase)" -ForegroundColor Gray
    } else {
        throw "Module verification failed"
    }
    
    Write-Host ""
    Write-Host "🎉 Installation completed successfully!" -ForegroundColor Green
    Write-Host "You can now run the database analysis script." -ForegroundColor White
    Write-Host ""
    Write-Host "Next step: Run the analysis script" -ForegroundColor Cyan
    Write-Host ".\RunDatabaseAnalysis.ps1 -ServerName 'Localhost\SQLEXPRESS' -DatabaseName 'SubhLaganNew_v1'" -ForegroundColor Yellow

} catch {
    Write-Host "❌ Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Alternative solutions:" -ForegroundColor Yellow
    Write-Host "1. Try running PowerShell as Administrator" -ForegroundColor White
    Write-Host "2. Use the manual SQL script approach (see AlternativeAnalysisGuide.md)" -ForegroundColor White
    Write-Host "3. Use SQL Server Management Studio to run scripts manually" -ForegroundColor White
    
    exit 1
}