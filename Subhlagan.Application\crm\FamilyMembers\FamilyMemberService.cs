﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.FamilyMembers
{
    public partial class FamilyMemberService : IFamilyMemberService
    {
        #region Fields

        private readonly EnhancedEntityRepository<FamilyMember> _familyMemberRepository;

        #endregion

        #region Ctor

        public FamilyMemberService(EnhancedEntityRepository<FamilyMember> familyMemberRepository)
        {
            _familyMemberRepository = familyMemberRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertFamilyMemberAsync(FamilyMember familyMember)
        {
            if (familyMember == null)
                throw new ArgumentNullException(nameof(familyMember));

            familyMember.Name = CommonHelper.EnsureNotNull(familyMember.Name)?.Trim();
            familyMember.Name = CommonHelper.EnsureMaximumLength(familyMember.Name, 255);

            await _familyMemberRepository.InsertAsync(familyMember);
        }

        public virtual async Task UpdateFamilyMemberAsync(FamilyMember familyMember)
        {
            if (familyMember == null)
                throw new ArgumentNullException(nameof(familyMember));

            familyMember.Name = CommonHelper.EnsureNotNull(familyMember.Name)?.Trim();
            familyMember.Name = CommonHelper.EnsureMaximumLength(familyMember.Name, 255);

            await _familyMemberRepository.UpdateAsync(familyMember);
        }

        public virtual async Task DeleteFamilyMemberAsync(FamilyMember familyMember)
        {
            if (familyMember == null)
                throw new ArgumentNullException(nameof(familyMember));

            await _familyMemberRepository.DeleteAsync(familyMember);
        }

        public virtual async Task<FamilyMember> GetFamilyMemberByIdAsync(int familyMemberId)
        {
            return await _familyMemberRepository.GetByIdAsync(familyMemberId, cache => default);
        }

        public virtual async Task<IPagedList<FamilyMember>> GetAllFamilyMembersAsync(
            string name = null,
            int? relationId = null,
            bool? isDeceased = null,
            bool? isPrimaryContact = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            int profileId = 0)
        {
            var query = _familyMemberRepository.Table;

            if (!string.IsNullOrWhiteSpace(name))
                query = query.Where(f => f.Name.Contains(name));

            if (relationId.HasValue)
                query = query.Where(f => f.RelationId == relationId.Value);

            if (isDeceased.HasValue)
                query = query.Where(f => f.IsDeceased == isDeceased.Value);

            if (isPrimaryContact.HasValue)
                query = query.Where(f => f.IsPrimaryContact == isPrimaryContact.Value);

            if (profileId > 0)
                query = query.Where(f => f.ProfileId == profileId);

            //query = query.OrderBy(f => f.Name);

            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);
        }

        #endregion
    }

}
