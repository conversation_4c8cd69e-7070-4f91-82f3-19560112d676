﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Subhlagan.Application.EducationAreas
{
    public partial interface IEducationAreaService
    {
        Task InsertEducationAreaAsync(EducationArea educationArea);
        Task UpdateEducationAreaAsync(EducationArea educationArea);
        Task DeleteEducationAreaAsync(EducationArea educationArea);
        Task<EducationArea> GetEducationAreaByIdAsync(int educationAreaId);
        Task<IList<EducationArea>> GetAllEducationAreasAsync(string name = null, bool showHidden = false);
    }

}
