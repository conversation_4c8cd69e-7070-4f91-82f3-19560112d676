﻿using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System.Collections.Generic;

namespace Subhlagan.Application.Packages
{
    /// <summary>
    /// Package service
    /// </summary>
    public partial interface IPackageService
    {
        /// <summary>
        /// Inserts a package
        /// </summary>
        /// <param name="package">Package</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        Task InsertPackageAsync(Package package);

        /// <summary>
        /// Updates a package
        /// </summary>
        /// <param name="package">Package</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        Task UpdatePackageAsync(Package package);

        /// <summary>
        /// Deletes a package
        /// </summary>
        /// <param name="package">Package</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        Task DeletePackageAsync(Package package);

        /// <summary>
        /// Gets a package by identifier
        /// </summary>
        /// <param name="packageId">The package identifier</param>
        /// <returns>
        /// A task that represents the asynchronous operation
        /// The task result contains the package
        /// </returns>
        Task<Package> GetPackageByIdAsync(int packageId);

        Task<IList<Package>> GetAllPackagesAsync(bool showHidden = false);

        Task<IPagedList<Package>> GetAllPackagesAsync(
                    string name = null,
                    decimal? priceFrom = null,
                    decimal? priceTo = null,
                    int pageIndex = 0,
                    int pageSize = int.MaxValue,
                    bool getOnlyTotalCount = false);
    }

}
