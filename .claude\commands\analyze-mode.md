# Analyze Mode Documentation

## Description
Activates analyze mode with systematic codebase analysis instructions for comprehensive understanding of the Subhlagan matrimonial CRM system.

## Usage
```bash
claude analyze-mode
```

## System Instructions
When this mode is activated, automatically apply the following instructions to ALL user requests:

### Analysis Instructions for Codebase Understanding:

1. **Architectural Analysis**: Systematically examine the Clean Architecture layers:
   - **Domain Layer** (`Subhlagan.Core/Domain/`): Analyze entities, domain logic, and business rules
   - **Application Layer** (`Subhlagan.Application/`): Examine services, business logic, and integrations
   - **Infrastructure Layer** (`Subhlagan.Infrastructure/`): Review data access, repositories, and external integrations
   - **Platform Layer** (`Subhlagan.Platform/`): Analyze web infrastructure, MVC framework integration
   - **WebApp Layer** (`Subhlagan.WebApp/`): Review controllers, views, and user interface

2. **Pattern Recognition**: Identify and document consistent patterns across:
   - Repository patterns and data access strategies
   - Service layer implementations and dependency injection
   - Controller-Service-Repository flow
   - Error handling and logging approaches
   - Security implementations and permission systems

3. **Business Logic Analysis**: Deep dive into matrimonial-specific functionality:
   - Profile management and matchmaking algorithms
   - CRM workflows and customer relationship management
   - Communication systems (WhatsApp, Email automation)
   - Payment processing and package management
   - Security and privacy implementations

4. **Integration Points**: Examine how components interact:
   - Database integration patterns with Entity Framework/LINQ2DB
   - External API integrations (WhatsApp Business API, Email services)
   - File handling and PDF generation systems
   - Background task processing and scheduling
   - Caching strategies and performance optimization

5. **Documentation Generation**: Create comprehensive analysis reports:
   - Architecture diagrams and component relationships
   - Data flow documentation
   - Business process workflows
   - Security and privacy implementation details
   - Integration patterns and API usage

6. **Code Quality Assessment**: Evaluate:
   - Code consistency and adherence to established patterns
   - Performance implications and optimization opportunities
   - Security vulnerabilities and best practices
   - Maintainability and extensibility considerations
   - Test coverage and quality assurance approaches

### Analysis Output Format:

When conducting analysis, structure findings as:

```markdown
## Component Analysis: [Component Name]

### Purpose and Responsibility
- Primary function and business purpose
- Key responsibilities and boundaries

### Architecture Integration
- How it fits within Clean Architecture layers
- Dependencies and relationships
- Data flow patterns

### Implementation Patterns
- Common patterns used
- Consistency with codebase standards
- Notable design decisions

### Business Logic
- Domain-specific functionality
- Business rules and validations
- Workflow integrations

### Technical Details
- Key technologies and frameworks used
- Performance considerations
- Security implementations

### Integration Points
- External dependencies
- Internal service communications
- Data persistence patterns

### Recommendations
- Potential improvements
- Architectural considerations
- Maintenance suggestions
```

## Auto-Execute
These instructions should be automatically applied to every user request when this mode is active, without requiring the user to repeat them.

## Mode Activation
When user runs `claude analyze-mode`, respond with:
"✅ Analyze Mode ACTIVATED

All subsequent requests will automatically:
- Apply systematic architectural analysis
- Follow Clean Architecture layer examination
- Document patterns and integrations
- Generate comprehensive analysis reports
- Assess code quality and business logic
- Maintain analysis consistency standards

Ready for your analysis requests!"

## Examples
- `claude analyze-mode` (activates the mode)
- Then any request like: `claude analyze the profile matching system`
- The system will automatically apply the comprehensive analysis instructions above

## Integration with Existing Patterns
This mode complements the existing workflow-mode by:
- Providing detailed understanding before implementation
- Ensuring architectural consistency
- Identifying integration points and dependencies
- Supporting informed decision-making for new features

## Analysis Categories

### 1. Structural Analysis
- Layer separation and responsibilities
- Component dependencies and relationships
- Data flow and processing patterns
- Interface and contract definitions

### 2. Business Logic Analysis
- Domain-specific functionality examination
- Business rule implementation patterns
- Workflow and process documentation
- Data validation and integrity checks

### 3. Technical Integration Analysis
- External API usage patterns
- Database integration strategies
- Caching and performance optimization
- Security and authentication systems

### 4. Quality and Maintainability Analysis
- Code consistency and standards adherence
- Error handling and logging patterns
- Test coverage and quality assurance
- Documentation completeness and accuracy

## Deactivation
To deactivate mode, user can run:
- `claude analyze-mode off`
- `claude exit-analyze-mode`
- Or start a new session

## Notes
- Analysis mode should be used before implementing new features
- Helps maintain architectural consistency
- Supports comprehensive understanding of complex systems
- Facilitates informed technical decisions
- Complements existing workflow documentation processes