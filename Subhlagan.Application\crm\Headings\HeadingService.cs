﻿using AutoMapper;
using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.Headings
{
    public partial class HeadingService : IHeadingService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Heading> _headingRepository;
        private readonly IStaticCacheManager _staticCacheManager;

        #endregion

        #region Ctor

        public HeadingService(EnhancedEntityRepository<Heading> headingRepository, IStaticCacheManager staticCacheManager)
        {
            _headingRepository = headingRepository;
            _staticCacheManager = staticCacheManager;
        }

        #endregion

        #region Methods

        public virtual async Task InsertHeadingAsync(Heading heading)
        {
            if (heading == null)
                throw new ArgumentNullException(nameof(heading));

            await _headingRepository.InsertAsync(heading);
        }

        public virtual async Task UpdateHeadingAsync(Heading heading)
        {
            if (heading == null)
                throw new ArgumentNullException(nameof(heading));

            await _headingRepository.UpdateAsync(heading);
        }

        public virtual async Task DeleteHeadingAsync(Heading heading)
        {
            if (heading == null)
                throw new ArgumentNullException(nameof(heading));

            await _headingRepository.DeleteAsync(heading);
        }

        public virtual async Task<Heading> GetHeadingByIdAsync(int headingId)
        {
            return await _headingRepository.GetByIdAsync(headingId, cache => default);
        }

        public virtual async Task<IPagedList<Heading>> GetAllHeadingsAsync(
            string subject = null,
            DateTime? createdOnFrom = null,
            DateTime? createdOnTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false)
        {
            var headings = await _headingRepository.GetAllPagedAsync(query =>
            {
                if (!string.IsNullOrWhiteSpace(subject))
                    query = query.Where(h => h.Subject.Contains(subject));

                if (createdOnFrom.HasValue)
                    query = query.Where(h => h.CreatedOnUtc >= createdOnFrom.Value);

                if (createdOnTo.HasValue)
                    query = query.Where(h => h.CreatedOnUtc <= createdOnTo.Value);

                return query.OrderBy(h => h.CreatedOnUtc);
            }, pageIndex, pageSize, getOnlyTotalCount);

            return headings;
        }

        public async Task DeleteHeadingAsync(List<Heading> news)
        {
           await _headingRepository.DeleteAsync(news);
        }

        public async Task<IList<Heading>> GetAllHeadingsAsync()
        {
            var cacheKey = _staticCacheManager.PrepareKeyForDefaultCache(PageBaaSDefaults.HeadingsCacheKey);

            var headings = await _staticCacheManager.GetAsync(cacheKey, async () =>
            {
                var query = _headingRepository.Table;
                query = query.OrderByDescending(f => f.Id);
                return await query.ToListAsync();
            });

            return headings;
        }

        #endregion
    }

}
