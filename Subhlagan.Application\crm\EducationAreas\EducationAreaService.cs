﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.EducationAreas
{
    public partial class EducationAreaService : IEducationAreaService
    {
        #region Fields

        private readonly EnhancedEntityRepository<EducationArea> _educationAreaRepository;

        #endregion

        #region Ctor

        public EducationAreaService(EnhancedEntityRepository<EducationArea> educationAreaRepository)
        {
            _educationAreaRepository = educationAreaRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertEducationAreaAsync(EducationArea educationArea)
        {
            if (educationArea == null)
                throw new ArgumentNullException(nameof(educationArea));

            educationArea.Name = CommonHelper.EnsureNotNull(educationArea.Name).Trim();
            educationArea.Name = CommonHelper.EnsureMaximumLength(educationArea.Name, 255);

            await _educationAreaRepository.InsertAsync(educationArea);
        }

        public virtual async Task UpdateEducationAreaAsync(EducationArea educationArea)
        {
            if (educationArea == null)
                throw new ArgumentNullException(nameof(educationArea));

            educationArea.Name = CommonHelper.EnsureNotNull(educationArea.Name).Trim();
            educationArea.Name = CommonHelper.EnsureMaximumLength(educationArea.Name, 255);

            await _educationAreaRepository.UpdateAsync(educationArea);
        }

        public virtual async Task DeleteEducationAreaAsync(EducationArea educationArea)
        {
            if (educationArea == null)
                throw new ArgumentNullException(nameof(educationArea));

            await _educationAreaRepository.DeleteAsync(educationArea);
        }

        public virtual async Task<EducationArea> GetEducationAreaByIdAsync(int educationAreaId)
        {
            return await _educationAreaRepository.GetByIdAsync(educationAreaId, cache => default);
        }

        public virtual async Task<IList<EducationArea>> GetAllEducationAreasAsync(string name = null, bool showHidden = false)
        {
            var educationAreas = await _educationAreaRepository.GetAllAsync(query =>
            {
                query = query.Where(ea => !ea.Deleted);

                query = query.OrderBy(ea => ea.DisplayOrder).ThenBy(ea => ea.Name);

                return query;
            }, cache => default);

            if (!string.IsNullOrWhiteSpace(name))
                educationAreas = educationAreas.Where(ea => ea.Name.Contains(name)).ToList();

            if (!showHidden)
                educationAreas = educationAreas.Where(c => c.Active).ToList();

            return educationAreas;
        }

        #endregion
    }
}
