-- ========================================================================
-- SAFE TABLES TO DELETE - SubhLaganNew_v1
-- ========================================================================
-- These are empty tables that appear to be unused e-commerce features
-- from the original platform (likely nopCommerce) that were never used
-- in your matrimonial CRM system.
-- 
-- IMPORTANT: Always backup your database before running this script!
-- ========================================================================

USE SubhLaganNew_v1;

-- Create backup first (manual step)
-- BACKUP DATABASE SubhLaganNew_v1 TO DISK = 'C:\Backups\SubhLaganNew_v1_BeforeCleanup.bak'

PRINT 'Starting safe table deletion process...';
PRINT 'Tables being deleted are empty e-commerce tables not used in matrimonial CRM';
PRINT '';

-- List of empty tables safe to delete (confirmed from your analysis)
DECLARE @SafeTablesToDelete TABLE (TableName NVARCHAR(128));

INSERT INTO @SafeTablesToDelete VALUES
('UrlRecord'), --checked
('TopicTemplate'), --checked
('Topic'), --checked
('ShippingMethod'), --checked
('ProductAvailabilityRange'), --checked
('ProductTemplate'), --checked
('StockQuantityHistory'), --checked
('Product'), --checked
('ManufacturerTemplate'), --checked
('DeliveryDate'), --checked
('Order'), --checked
('BlogPost'), --checked
('AddressAttribute'), --checked
('AddressAttributeValue'), --checked
('Affiliate'), --ManageAffiliates permission delete later
('BackInStockSubscription'), -- checked
('BlogComment'), --checked
('Campaign'), --checked
('Category'), --checked
('CheckoutAttribute'), --checked
('CheckoutAttributeValue'), --checked
('CrossSellProduct'), --checked
--('CustomerAttribute'),
--('CustomerAttributeValue'),
('Discount_AppliedToCategories'), --checked
('Discount_AppliedToManufacturers'), --checked
('Discount_AppliedToProducts'), --checked
('DiscountRequirement'), --checked
('ExternalAuthenticationRecord'), --checked
('Forums_Forum'), --checked
('Forums_Group'), --checked
('Forums_Post'), --checked
('Forums_PostVote'), --checked
('Forums_PrivateMessage'), --checked
('Forums_Subscription'), --checked
('Forums_Topic'), --checked
('GdprConsent'), --checked
('GdprLog'), --checked
('GiftCard'), --checked
('GiftCardUsageHistory'), --checked
('HeadingArchive'), --checked
('Manufacturer'), --checked
-- ('MassEmailTemplate'), do not delete
('News'),
('NewsComment'), --checked
('NewsLetterSubscription'), --checked
('Poll'), --checked
('PollAnswer'), --checked
('PollVotingRecord'), --checked
('PredefinedProductAttributeValue'), --checked
('Product_Category_Mapping'), --checked
('Product_Manufacturer_Mapping'), --checked
('Product_Picture_Mapping'), --checked
('Product_ProductAttribute_Mapping'), --checked
('Product_ProductTag_Mapping'), --checked
('Product_SpecificationAttribute_Mapping'), --checked
('ProductAttribute'), --checked
('ProductAttributeCombination'), --checked
('ProductAttributeValue'), --checked
('ProductReview'), --checked
('ProductReview_ReviewType_Mapping'), --checked
('ProductReviewHelpfulness'), --checked
('ProductTag'), --checked
('ProductVideo'), --checked
('ProductWarehouseInventory'), --checked
('RecurringPayment'), --checked
('RecurringPaymentHistory'), --checked
('RelatedProduct'), --checked
('ReturnRequest'), --checked
('ReviewType'), --checked
('RewardPointsHistory'), --checked
('Shipment'), --checked
('ShipmentItem'), --checked
('ShippingMethodRestrictions'), --checked
('ShoppingCartItem'), --checked
('SpecificationAttribute'), --checked
('SpecificationAttributeGroup'), --checked
('SpecificationAttributeOption'), --checked
('StoreMapping'), --checked
('TierPrice'), --checked
('Vendor'), --checked
('VendorAttribute'), --checked
('VendorAttributeValue'), --checked
('VendorNote'), --checked
('Video'), --checked
('Warehouse'); --checked

DROP TABLE [DiscountRequirement]
DROP TABLE [DiscountUsageHistory]
DROP TABLE [OrderNote]
DROP TABLE [OrderItem]
DROP TABLE [Order]
DROP TABLE [RewardPointsHistory]
DROP TABLE [BlogPost]
DROP TABLE [DeliveryDate]
DROP TABLE [ManufacturerTemplate]
DROP TABLE [StockQuantityHistory]
DROP TABLE [Product]
DROP TABLE [ProductAvailabilityRange]
DROP TABLE [ProductTemplate]
DROP TABLE [ShippingMethodRestrictions]
DROP TABLE [ShippingMethod]
DROP TABLE [TopicTemplate]
DROP TABLE [Topic]
DROP TABLE [UrlRecord]

SELECT 
    f.name AS ForeignKey,
    OBJECT_NAME(f.parent_object_id) AS TableName,
    COL_NAME(fc.parent_object_id, fc.parent_column_id) AS ColumnName
FROM 
    sys.foreign_keys AS f
INNER JOIN 
    sys.foreign_key_columns AS fc 
    ON f.OBJECT_ID = fc.constraint_object_id
WHERE 
    f.referenced_object_id = OBJECT_ID('DiscountRequirement');


-- Show what will be deleted
PRINT 'Tables that will be deleted (all are empty):';
SELECT TableName FROM @SafeTablesToDelete ORDER BY TableName;

-- Verify all tables are actually empty
PRINT '';
PRINT 'Verifying all tables are empty before deletion...';

DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @Count INT;
DECLARE @HasData BIT = 0;

DECLARE table_cursor CURSOR FOR
SELECT TableName FROM @SafeTablesToDelete;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = @TableName)
    BEGIN
        SET @SQL = 'SELECT @Count = COUNT(*) FROM [' + @TableName + ']';
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count OUTPUT;
        
        IF @Count > 0
        BEGIN
            PRINT 'WARNING: Table ' + @TableName + ' has ' + CAST(@Count AS VARCHAR) + ' records!';
            SET @HasData = 1;
        END
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

IF @HasData = 1
BEGIN
    PRINT '';
    PRINT 'STOPPING: Some tables contain data. Manual review required.';
    RETURN;
END

-- Proceed with deletion if all tables are empty
PRINT '';
PRINT 'All tables verified as empty. Proceeding with deletion...';
PRINT '';

DECLARE delete_cursor CURSOR FOR
SELECT TableName FROM @SafeTablesToDelete;

OPEN delete_cursor;
FETCH NEXT FROM delete_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    IF EXISTS (SELECT * FROM sys.tables WHERE name = @TableName)
    BEGIN
        -- Check for foreign key constraints first
        IF NOT EXISTS (
            SELECT * FROM sys.foreign_keys fk
            INNER JOIN sys.tables t ON fk.referenced_object_id = t.object_id
            WHERE t.name = @TableName
        )
        BEGIN
            SET @SQL = 'DROP TABLE [' + @TableName + ']';
            EXEC(@SQL);
            PRINT 'Deleted table: ' + @TableName;
        END
        ELSE
        BEGIN
            PRINT 'Skipped table: ' + @TableName + ' (has foreign key references)';
        END
    END
    ELSE
    BEGIN
        PRINT 'Table does not exist: ' + @TableName;
    END
    
    FETCH NEXT FROM delete_cursor INTO @TableName;
END

CLOSE delete_cursor;
DEALLOCATE delete_cursor;



PRINT '';
PRINT '========================================================================';
PRINT 'CLEANUP COMPLETE';
PRINT '========================================================================';
PRINT 'Empty e-commerce tables have been removed from your matrimonial CRM database.';
PRINT 'Your core CRM functionality remains intact.';
PRINT '';
PRINT 'Tables that were NOT deleted (intentionally kept):';
PRINT '- All ProfileAttribute* tables (disabled features with data)';
PRINT '- All active CRM tables (Profile, User, City, etc.)';
PRINT '- All platform tables (Customer, Setting, etc.)';
PRINT '========================================================================';