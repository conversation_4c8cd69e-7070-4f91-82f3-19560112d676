2025-08-14 12:02:35 CRITICAL: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'C:\WINDOWS\system32\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at Microsoft.Extensions.Hosting.HostBuilder.InitializeAppConfiguration()
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at Subhlagan.BackupService.Program.Main(String[] args) in C:\Workshop\SubhLagan\Projects\Subhlagan\src\Subhlagan.BackupService\Program.cs:line 27

2025-08-14 15:44:02 CRITICAL: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'C:\WINDOWS\system32\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at Microsoft.Extensions.Hosting.HostBuilder.InitializeAppConfiguration()
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at Subhlagan.BackupService.Program.Main(String[] args) in C:\Workshop\SubhLagan\Projects\Subhlagan\src\Subhlagan.BackupService\Program.cs:line 27

