-- =====================================================================
-- WORKING UNUSED TABLE ANALYSIS FOR SubhLaganNew_v1
-- =====================================================================
-- Purpose: Simple, guaranteed-to-work script to identify unused tables
-- Database: SubhLaganNew_v1 (Matrimonial CRM System)
-- 
-- INSTRUCTIONS:
-- 1. Open SQL Server Management Studio
-- 2. Connect to: Localhost\SQLEXPRESS
-- 3. Select Database: SubhLaganNew_v1
-- 4. Execute this entire script
-- 5. Review results - focus on tables marked as "SAFE_TO_DELETE"
-- =====================================================================

USE SubhLaganNew_v1;

PRINT '========================================';
PRINT 'SUBHLAGAN CRM - UNUSED TABLE ANALYSIS';
PRINT 'Database: SubhLaganNew_v1';
PRINT 'Analysis Date: ' + CONVERT(varchar, GETDATE(), 120);
PRINT '========================================';
PRINT '';

-- Step 1: Get all user tables with basic information
SELECT 
    t.name AS TableName,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    ISNULL(p.rows, 0) AS RecordCount,
    t.create_date AS CreatedDate,
    t.modify_date AS ModifiedDate,
    
    -- Check if table is in our known entities list (based on codebase analysis)
    CASE 
        WHEN t.name = 'AstroCity' THEN 'UNUSED - Commented in migration, no service/controller'
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 'DISABLED_FEATURE - Has implementation but commented in migration'
        WHEN t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory') THEN 'ACTIVE_ENTITY - Has service and controller'
        WHEN t.name IN ('ProfileHobbyMapping', 'ProfileProfileCategoryMapping', 'ProfileRestrictedMapping', 'ProfileTransferProfileMapping', 'ProfileUserMapping', 'RefineSearchProfileSpecificationAttributeMapping', 'UserEmailAccountMapping') THEN 'MAPPING_TABLE - Used for relationships'
        WHEN t.name IN ('Customer', 'CustomerAddressMapping', 'CustomerCustomerRoleMapping', 'CustomerPassword', 'CustomerRole', 'Address', 'Country', 'StateProvince', 'Language', 'LocaleStringResource', 'LocalizedProperty', 'ActivityLog', 'ActivityLogType', 'Log', 'Picture', 'PictureBinary', 'Download', 'EmailAccount', 'MessageTemplate', 'QueuedEmail', 'QueuedEmailAttachment', 'MassEmailCampaign', 'MassEmailLog', 'MassEmailRecipient', 'WhatsAppQueuedMessageEntity', 'ScheduleTask', 'AclRecord', 'PermissionRecord', 'PermissionRecordCustomerRoleMapping', 'Setting', 'GenericAttribute') THEN 'PLATFORM_TABLE - Core framework table'
        ELSE 'UNKNOWN - Not in codebase analysis'
    END AS CodebaseStatus,
    
    -- Recommendation based on codebase knowledge and basic checks
    CASE 
        WHEN t.name = 'AstroCity' AND ISNULL(p.rows, 0) = 0 THEN 'SAFE_TO_DELETE'
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 'DO_NOT_DELETE - Disabled feature with full implementation'
        WHEN t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory') THEN 'KEEP - Active entity'
        WHEN t.name IN ('ProfileHobbyMapping', 'ProfileProfileCategoryMapping', 'ProfileRestrictedMapping', 'ProfileTransferProfileMapping', 'ProfileUserMapping', 'RefineSearchProfileSpecificationAttributeMapping', 'UserEmailAccountMapping') THEN 'KEEP - Mapping table'
        WHEN t.name IN ('Customer', 'CustomerAddressMapping', 'CustomerCustomerRoleMapping', 'CustomerPassword', 'CustomerRole', 'Address', 'Country', 'StateProvince', 'Language', 'LocaleStringResource', 'LocalizedProperty', 'ActivityLog', 'ActivityLogType', 'Log', 'Picture', 'PictureBinary', 'Download', 'EmailAccount', 'MessageTemplate', 'QueuedEmail', 'QueuedEmailAttachment', 'MassEmailCampaign', 'MassEmailLog', 'MassEmailRecipient', 'WhatsAppQueuedMessageEntity', 'ScheduleTask', 'AclRecord', 'PermissionRecord', 'PermissionRecordCustomerRoleMapping', 'Setting', 'GenericAttribute') THEN 'KEEP - Platform table'
        WHEN ISNULL(p.rows, 0) = 0 THEN 'REVIEW_REQUIRED - Empty table not in codebase'
        ELSE 'REVIEW_REQUIRED - Unknown table with data'
    END AS Recommendation,
    
    -- Simple confidence score
    CASE 
        WHEN t.name = 'AstroCity' AND ISNULL(p.rows, 0) = 0 THEN 95
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 5
        WHEN t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory') THEN 0
        WHEN t.name IN ('ProfileHobbyMapping', 'ProfileProfileCategoryMapping', 'ProfileRestrictedMapping', 'ProfileTransferProfileMapping', 'ProfileUserMapping', 'RefineSearchProfileSpecificationAttributeMapping', 'UserEmailAccountMapping') THEN 0
        WHEN t.name IN ('Customer', 'CustomerAddressMapping', 'CustomerCustomerRoleMapping', 'CustomerPassword', 'CustomerRole', 'Address', 'Country', 'StateProvince', 'Language', 'LocaleStringResource', 'LocalizedProperty', 'ActivityLog', 'ActivityLogType', 'Log', 'Picture', 'PictureBinary', 'Download', 'EmailAccount', 'MessageTemplate', 'QueuedEmail', 'QueuedEmailAttachment', 'MassEmailCampaign', 'MassEmailLog', 'MassEmailRecipient', 'WhatsAppQueuedMessageEntity', 'ScheduleTask', 'AclRecord', 'PermissionRecord', 'PermissionRecordCustomerRoleMapping', 'Setting', 'GenericAttribute') THEN 0
        WHEN ISNULL(p.rows, 0) = 0 THEN 60
        ELSE 20
    END AS SafetyScore

FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0
ORDER BY 
    CASE 
        WHEN t.name = 'AstroCity' AND ISNULL(p.rows, 0) = 0 THEN 95
        ELSE 0
    END DESC,
    ISNULL(p.rows, 0) ASC,
    t.name;

PRINT '';
PRINT '========================================';
PRINT 'EMPTY TABLES ANALYSIS';
PRINT '========================================';

-- Show only empty tables for easy review
SELECT 
    t.name AS TableName,
    ISNULL(p.rows, 0) AS Records,
    CASE 
        WHEN t.name = 'AstroCity' THEN 'SAFE_TO_DELETE - Confirmed unused'
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 'DO_NOT_DELETE - Disabled feature'
        ELSE 'REVIEW_REQUIRED - Verify usage'
    END AS EmptyTableRecommendation
FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 AND ISNULL(p.rows, 0) = 0
ORDER BY t.name;

PRINT '';
PRINT '========================================';
PRINT 'FOREIGN KEY DEPENDENCIES';
PRINT '========================================';

-- Check for foreign key dependencies
SELECT 
    tp.name AS ParentTable,
    tr.name AS ReferencedTable,
    fk.name AS ForeignKeyName,
    CASE 
        WHEN tp.name = 'AstroCity' OR tr.name = 'AstroCity' THEN 'CHECK - Involves potentially unused table'
        ELSE 'OK - Both tables are known entities'
    END AS DependencyStatus
FROM sys.foreign_keys AS fk
INNER JOIN sys.tables AS tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables AS tr ON fk.referenced_object_id = tr.object_id
WHERE tp.is_ms_shipped = 0 AND tr.is_ms_shipped = 0
ORDER BY 
    CASE WHEN tp.name = 'AstroCity' OR tr.name = 'AstroCity' THEN 0 ELSE 1 END,
    tp.name, tr.name;

PRINT '';
PRINT '========================================';
PRINT 'FINAL SUMMARY';
PRINT '========================================';

-- Summary counts
DECLARE @TotalTables INT;
DECLARE @EmptyTables INT;
DECLARE @SafeToDelete INT;
DECLARE @DisabledFeatures INT;

SELECT @TotalTables = COUNT(*) FROM sys.tables WHERE is_ms_shipped = 0;

SELECT @EmptyTables = COUNT(*) 
FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 AND ISNULL(p.rows, 0) = 0;

SELECT @SafeToDelete = COUNT(*) 
FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 
  AND t.name = 'AstroCity' 
  AND ISNULL(p.rows, 0) = 0;

SELECT @DisabledFeatures = COUNT(*) 
FROM sys.tables t
WHERE t.is_ms_shipped = 0 
  AND t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption');

PRINT 'ANALYSIS RESULTS:';
PRINT '==================';
PRINT 'Total Tables: ' + CAST(@TotalTables AS VARCHAR);
PRINT 'Empty Tables: ' + CAST(@EmptyTables AS VARCHAR);
PRINT 'Safe to Delete: ' + CAST(@SafeToDelete AS VARCHAR) + ' (AstroCity only)';
PRINT 'Disabled Features: ' + CAST(@DisabledFeatures AS VARCHAR) + ' (DO NOT DELETE)';
PRINT '';

-- Show specific recommendations
IF @SafeToDelete > 0
BEGIN
    PRINT '✅ SAFE TO DELETE:';
    SELECT t.name AS TableName, 'Confirmed unused through codebase analysis' AS Reason
    FROM sys.tables t
    LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
    WHERE t.is_ms_shipped = 0 
      AND t.name = 'AstroCity' 
      AND ISNULL(p.rows, 0) = 0;
END
ELSE
BEGIN
    PRINT '❌ NO TABLES SAFE TO DELETE';
    PRINT 'Either AstroCity doesn''t exist or it contains data.';
END

PRINT '';
PRINT '⚠️  DISABLED FEATURES (DO NOT DELETE):';
SELECT t.name AS TableName, 'Has full implementation - commented in migration only' AS Reason
FROM sys.tables t
WHERE t.is_ms_shipped = 0 
  AND t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption')
ORDER BY t.name;

PRINT '';
PRINT '========================================';
PRINT 'CRITICAL SAFETY REMINDERS:';
PRINT '1. Only delete tables marked as SAFE_TO_DELETE';
PRINT '2. NEVER delete disabled features (Card, ProfileAttribute*)';
PRINT '3. Always backup database before any changes';
PRINT '4. Test in development environment first';
PRINT '5. AstroCity is the ONLY table confirmed safe to delete';
PRINT '========================================';

PRINT '';
PRINT '✅ Analysis Complete!';
PRINT 'Review the results above for deletion recommendations.';