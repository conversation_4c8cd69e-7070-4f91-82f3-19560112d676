﻿using System;

namespace Subhlagan.Application.MatchKundalis
{
    public class MarriageReportFormatter
    {
        /// <summary>
        /// Formats a date component into a two-digit string.
        /// </summary>
        /// <param name="value">The integer value of the date component (e.g., Day, Month).</param>
        /// <returns>A two-digit string (e.g., "08").</returns>
        public static string FormatDateComponent(int? value)
        {
            return value?.ToString("D2") ?? "00";
        }

        /// <summary>
        /// Formats a year into a four-digit string.
        /// </summary>
        /// <param name="value">The integer value of the year.</param>
        /// <returns>A four-digit string (e.g., "2024").</returns>
        public static string FormatYear(int? value)
        {
            return value?.ToString("D4") ?? "0000";
        }

        /// <summary>
        /// Formats a nullable decimal value for latitude, longitude, or GMT difference.
        /// Converts the value to a double, defaulting to 0.0 if null.
        /// </summary>
        /// <param name="value">The nullable decimal value to format.</param>
        /// <returns>A double value, defaulting to 0.0 if null.</returns>
        public static double FormatDouble(decimal? value)
        {
            // If the value is null, return 0.0
            if (!value.HasValue)
                return 0.0;

            // Directly convert the decimal to a double
            return Convert.ToDouble(value.Value);
        }


        /// <summary>
        /// Formats a profile date of birth for marriage report request.
        /// </summary>
        public static (string Day, string Month, string Year, string Hour, string Minute) FormatDateOfBirth(DateTime? dateOfBirth)
        {
            return (
                Day: FormatDateComponent(dateOfBirth?.Day),
                Month: FormatDateComponent(dateOfBirth?.Month),
                Year: FormatYear(dateOfBirth?.Year),
                Hour: FormatDateComponent(dateOfBirth?.Hour),
                Minute: FormatDateComponent(dateOfBirth?.Minute)
            );
        }
    }
}
