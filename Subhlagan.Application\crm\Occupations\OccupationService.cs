﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Users;

namespace Subhlagan.Application.Occupations
{
    public class OccupationService : IOccupationService
    {
        private readonly EnhancedEntityRepository<Occupation> _occupationRepository;
        private readonly IUserService _userService;

        public OccupationService(EnhancedEntityRepository<Occupation> occupationRepository, IUserService userService)
        {
            _occupationRepository = occupationRepository;
            _userService = userService;
        }

        public async Task InsertOccupationAsync(Occupation occupation)
        {
            if (occupation == null)
                throw new ArgumentNullException(nameof(occupation));

            occupation.Name = CommonHelper.EnsureNotNull(occupation.Name).Trim();
            occupation.Name = CommonHelper.EnsureMaximumLength(occupation.Name, 255);
            await _userService.SetCurrentUserIdAsync(occupation);

            await _occupationRepository.InsertAsync(occupation);
        }

        public async Task UpdateOccupationAsync(Occupation occupation)
        {
            if (occupation == null)
                throw new ArgumentNullException(nameof(occupation));

            occupation.Name = CommonHelper.EnsureNotNull(occupation.Name).Trim();
            occupation.Name = CommonHelper.EnsureMaximumLength(occupation.Name, 255);
            await _userService.SetCurrentUserIdAsync(occupation);

            await _occupationRepository.UpdateAsync(occupation);
        }

        public async Task DeleteOccupationAsync(Occupation occupation)
        {
            if (occupation == null)
                throw new ArgumentNullException(nameof(occupation));
            await _userService.SetCurrentUserIdAsync(occupation);

            await _occupationRepository.DeleteAsync(occupation);
        }

        public async Task<Occupation> GetOccupationByIdAsync(int occupationId)
        {
            return await _occupationRepository.GetByIdAsync(occupationId, cache => default);
        }

        public async Task<IList<Occupation>> GetAllOccupationsAsync(bool showHidden = false)
        {
            var occupations = await _occupationRepository.GetAllAsync(query =>
            {
                query = query.Where(o => !o.Deleted);
                query = query.OrderBy(o => o.Name);
                return query;
                
            }, cache => default);

            if (!showHidden)
                occupations = occupations.Where(o => o.Active).ToList();

            return occupations;
        }

        public async Task<string> GetOccupationNameByIdAsync(int occupationId)
        {
            return (await GetOccupationByIdAsync(occupationId))?.Name;
        }
    }
}
