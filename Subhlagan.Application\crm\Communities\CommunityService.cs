﻿using System;
using System.Collections.Generic;
using System.IO.Packaging;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Users;

namespace Subhlagan.Application.Communities
{
    /// <summary>
    /// Community service
    /// </summary>
    public partial class CommunityService : ICommunityService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Community> _communityRepository;
        private readonly IUserService _userService;
        #endregion

        #region Ctor

        public CommunityService(EnhancedEntityRepository<Community> communityRepository, IUserService userService)
        {
            _communityRepository = communityRepository;
            _userService = userService;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Inserts a community
        /// </summary>
        /// <param name="community">Community</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public virtual async Task InsertCommunityAsync(Community community)
        {
            if (community == null)
                throw new ArgumentNullException(nameof(community));

            community.Name = CommonHelper.EnsureNotNull(community.Name);
            community.Name = community.Name.Trim();
            community.Name = CommonHelper.EnsureMaximumLength(community.Name, 255);
            await _userService.SetCurrentUserIdAsync(community);

            await _communityRepository.InsertAsync(community);
        }

        /// <summary>
        /// Updates a community
        /// </summary>
        /// <param name="community">Community</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public virtual async Task UpdateCommunityAsync(Community community)
        {
            if (community == null)
                throw new ArgumentNullException(nameof(community));

            community.Name = CommonHelper.EnsureNotNull(community.Name);
            community.Name = community.Name.Trim();
            community.Name = CommonHelper.EnsureMaximumLength(community.Name, 255);
            await _userService.SetCurrentUserIdAsync(community);

            await _communityRepository.UpdateAsync(community);
        }

        /// <summary>
        /// Deletes a community
        /// </summary>
        /// <param name="community">Community</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public virtual async Task DeleteCommunityAsync(Community community)
        {
            if (community == null)
                throw new ArgumentNullException(nameof(community));

            await _communityRepository.DeleteAsync(community);
        }

        /// <summary>
        /// Gets a community by identifier
        /// </summary>
        /// <param name="communityId">The community identifier</param>
        /// <returns>
        /// A task that represents the asynchronous operation
        /// The task result contains the community
        /// </returns>
        public virtual async Task<Community> GetCommunityByIdAsync(int communityId)
        {
            return await _communityRepository.GetByIdAsync(communityId, cache => default);
        }

        /// <summary>
        /// Gets all communities
        /// </summary>
        /// <returns>
        /// A task that represents the asynchronous operation
        /// The task result contains the communities list        
        /// </returns>
        public virtual async Task<IList<Community>> GetAllCommunitiesAsync(bool showHidden = false)
        {
            var communities = await _communityRepository.GetAllAsync(query =>
            {
                query = query.Where(c => !c.Deleted);

                query = query.OrderBy(c => c.DisplayOrder);
                
                query = query.OrderBy(c => c.Name);
                return query;
               
            }, cache => default);

            if (!showHidden)
                communities = communities.Where(c => c.Active).ToList();

            return communities;
        }

        #endregion
    }
}
