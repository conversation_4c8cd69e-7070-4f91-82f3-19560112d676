﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Subhlagan.Application.MatchKundalis
{
    public partial interface IMatchKundaliService
    {
        Task InsertMatchKundaliAsync(MatchKundali matchKundali);
        Task UpdateMatchKundaliAsync(MatchKundali matchKundali);
        Task DeleteMatchKundaliAsync(MatchKundali matchKundali);
        Task<MatchKundali> GetMatchKundaliByIdAsync(int matchKundaliId);
        Task<IPagedList<MatchKundali>> GetAllMatchKundalisAsync(
            int? maleProfileId = null,
            int? femaleProfileId = null,
            decimal? totalGunFrom = null,
            decimal? totalGunTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            DateTime? CreatedOnFromDate =null,
            DateTime? CreatedOnToDate = null,
            int userId=0,
            IList<int> maleProfileIds = null,
            IList<int> femaleProfileIds = null,
            IList<int> matchKundaliIds = null, List<int> matchResultIds = null, IList<int> userIds = null);
        Task<MatchKundali> GetMatchKundaliByProfileIdsAsync(int maleProfileId, int femaleProfileId);
        (string Message, string Color, decimal TotalGun) GetTotalGunDescription(MatchKundali matchKundali);
        Task<string> GetDownloadPdfUrlAsync(MatchKundali matchKundali);
        Task<string> GetDownloadPdfAsync(MatchKundali matchKundali);
        string GetManglikStatus(MatchKundali matchKundali);
    }

}
