-- =====================================================================
-- FIXED UNUSED TABLE ANALYSIS FOR SubhLaganNew_v1
-- =====================================================================
-- Purpose: Single comprehensive script to identify unused database tables
--          with cross-reference to codebase analysis for maximum safety
-- 
-- Database: SubhLaganNew_v1 (Matrimonial CRM System)
-- Framework: ASP.NET Core 7.0 with Clean Architecture
-- 
-- INSTRUCTIONS:
-- 1. Open SQL Server Management Studio
-- 2. Connect to: Localhost\SQLEXPRESS
-- 3. Select Database: SubhLaganNew_v1
-- 4. Execute this entire script
-- 5. Review results - focus on tables with ConfidenceScore >= 85
-- =====================================================================

USE SubhLaganNew_v1;

PRINT '========================================';
PRINT 'SUBHLAGAN CRM - UNUSED TABLE ANALYSIS';
PRINT 'Database: SubhLaganNew_v1';
PRINT 'Analysis Date: ' + CONVERT(varchar, GETDATE(), 120);
PRINT '========================================';
PRINT '';

-- Create temporary table to store codebase knowledge
IF OBJECT_ID('tempdb..#CodebaseKnowledge') IS NOT NULL DROP TABLE #CodebaseKnowledge;
CREATE TABLE #CodebaseKnowledge (
    TableName NVARCHAR(128) PRIMARY KEY,
    HasService BIT,
    HasController BIT,
    MigrationStatus NVARCHAR(50), -- 'ACTIVE', 'COMMENTED_OUT', 'NOT_IN_MIGRATION'
    CodeReferences INT,
    EntityType NVARCHAR(50), -- 'CORE_ENTITY', 'MAPPING', 'GENERAL', 'UNKNOWN'
    Notes NVARCHAR(500)
);

-- Insert codebase knowledge based on static analysis
INSERT INTO #CodebaseKnowledge (TableName, HasService, HasController, MigrationStatus, CodeReferences, EntityType, Notes) VALUES
-- DEFINITELY UNUSED (from codebase analysis)
('AstroCity', 0, 0, 'COMMENTED_OUT', 0, 'UNKNOWN', 'Commented out in migration, no service/controller found, only commented permission refs'),

-- DISABLED FEATURES (DO NOT DELETE - have full implementations)
('Card', 1, 1, 'COMMENTED_OUT', 50, 'CORE_ENTITY', 'Has CardService, CardController, full MVC implementation - DISABLED FEATURE'),
('ProfileAttribute', 1, 1, 'COMMENTED_OUT', 100, 'CORE_ENTITY', 'Has ProfileAttributeService, ProfileAttributeController - CORE FEATURE'),
('ProfileAttributeValue', 1, 0, 'COMMENTED_OUT', 102, 'CORE_ENTITY', '102+ code references, extensive usage, caching - HEAVILY USED'),
('ProfileAttributeGroup', 1, 1, 'COMMENTED_OUT', 30, 'CORE_ENTITY', 'Has service and controller implementation - DISABLED FEATURE'),
('ProfileSpecificationAttribute', 1, 1, 'COMMENTED_OUT', 40, 'CORE_ENTITY', 'Has service and controller implementation - DISABLED FEATURE'),
('ProfileSpecificationAttributeOption', 1, 0, 'COMMENTED_OUT', 25, 'CORE_ENTITY', 'Used in profile attribute system - ACTIVE FEATURE'),

-- ACTIVE CORE ENTITIES (definitely keep)
('ActionStatus', 1, 1, 'ACTIVE', 50, 'CORE_ENTITY', 'Full implementation with ActionStatusService'),
('City', 1, 1, 'ACTIVE', 80, 'CORE_ENTITY', 'Full implementation with CityService'),
('ClientMeeting', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with ClientMeetingService'),
('Community', 1, 1, 'ACTIVE', 60, 'CORE_ENTITY', 'Full implementation with CommunityService'),
('ContactExchange', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ContactExchangeService'),
('Education', 1, 1, 'ACTIVE', 45, 'CORE_ENTITY', 'Full implementation with EducationService'),
('EducationArea', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with EducationAreaService'),
('EducationLevel', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with EducationLevelService'),
('FamilyMember', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with FamilyMemberService'),
('Feedback', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with FeedbackService'),
('FollowUp', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with FollowUpService'),
('FreshCallSchedule', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with FreshCallScheduleService'),
('Gotra', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with GotraService'),
('Heading', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with HeadingService'),
('Hobby', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with HobbyService'),
('MarriageConfirmation', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with MarriageConfirmationService'),
('MatchKundali', 1, 0, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with MatchKundaliService'),
('MedicalHistory', 1, 0, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with MedicalHistoryService'),
('Meeting', 1, 1, 'ACTIVE', 50, 'CORE_ENTITY', 'Full implementation with MeetingService'),
('MeetingStatus', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with MeetingStatusService'),
('Occupation', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with OccupationService'),
('OccupationAndBusiness', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with OccupationAndBusinessService'),
('Office', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with OfficeService'),
('OrderPaymentHistory', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with OrderPaymentHistoryService'),
('Package', 1, 1, 'ACTIVE', 45, 'CORE_ENTITY', 'Full implementation with PackageService'),
('PartnerPreference', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with PartnerPreferenceService'),
('Profile', 1, 1, 'ACTIVE', 200, 'CORE_ENTITY', 'Core entity with ProfileService - CRITICAL TABLE'),
('ProfileCategory', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ProfileCategoryService'),
('ProfileContact', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ProfileContactService'),
('ProfileMatchInteraction', 1, 1, 'ACTIVE', 60, 'CORE_ENTITY', 'Full implementation with ProfileMatchInteractionService'),
('ProfileMatchStatus', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ProfileMatchStatusService'),
('ProfilePauseRecord', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ProfilePauseRecordService'),
('ProfileStatusHistory', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ProfileStatusHistoryService'),
('ProfileTransfer', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ProfileTransferService'),
('Qualification', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with QualificationService'),
('Reason', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ReasonService'),
('Remark', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with RemarkService'),
('ShortList', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ShortListService'),
('User', 1, 0, 'ACTIVE', 100, 'CORE_ENTITY', 'Core user entity with UserService - CRITICAL TABLE'),
('UserWorkingHoursHistory', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with UserWorkingHoursHistoryService'),

-- MAPPING TABLES (keep - used for relationships)
('ProfileHobbyMapping', 0, 0, 'ACTIVE', 15, 'MAPPING', 'Relationship mapping table'),
('ProfileProfileCategoryMapping', 0, 0, 'ACTIVE', 15, 'MAPPING', 'Relationship mapping table'),
('ProfileRestrictedMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),
('ProfileTransferProfileMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),
('ProfileUserMapping', 0, 0, 'ACTIVE', 20, 'MAPPING', 'Relationship mapping table'),
('RefineSearchProfileSpecificationAttributeMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),
('UserEmailAccountMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),

-- GENERAL PLATFORM ENTITIES (keep - framework tables)
('Customer', 1, 0, 'ACTIVE', 80, 'GENERAL', 'Core customer entity with CustomerService'),
('CustomerAddressMapping', 0, 0, 'ACTIVE', 15, 'GENERAL', 'Customer address relationship'),
('CustomerCustomerRoleMapping', 0, 0, 'ACTIVE', 15, 'GENERAL', 'Customer role relationship'),
('CustomerPassword', 0, 0, 'ACTIVE', 20, 'GENERAL', 'Customer authentication'),
('CustomerRole', 0, 0, 'ACTIVE', 25, 'GENERAL', 'Customer role management'),
('Address', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Address management with AddressService'),
('Country', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Country management with CountryService'),
('StateProvince', 1, 0, 'ACTIVE', 25, 'GENERAL', 'State/Province management'),
('Language', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Localization with LanguageService'),
('LocaleStringResource', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Localization resources'),
('LocalizedProperty', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Localized entity properties'),
('ActivityLog', 1, 1, 'ACTIVE', 35, 'GENERAL', 'Activity logging with ActivityLogService'),
('ActivityLogType', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Activity log types'),
('Log', 1, 0, 'ACTIVE', 30, 'GENERAL', 'System logging'),
('Picture', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Picture management with PictureService'),
('PictureBinary', 0, 0, 'ACTIVE', 20, 'GENERAL', 'Picture binary data'),
('Download', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Download management with DownloadService'),
('EmailAccount', 1, 0, 'ACTIVE', 35, 'GENERAL', 'Email account management'),
('MessageTemplate', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Message templates'),
('QueuedEmail', 1, 0, 'ACTIVE', 45, 'GENERAL', 'Email queue management'),
('QueuedEmailAttachment', 1, 0, 'ACTIVE', 20, 'GENERAL', 'Email attachments'),
('MassEmailCampaign', 0, 0, 'ACTIVE', 30, 'GENERAL', 'Mass email campaigns'),
('MassEmailLog', 0, 0, 'ACTIVE', 20, 'GENERAL', 'Mass email logging'),
('MassEmailRecipient', 0, 0, 'ACTIVE', 25, 'GENERAL', 'Mass email recipients'),
('WhatsAppQueuedMessageEntity', 0, 0, 'ACTIVE', 30, 'GENERAL', 'WhatsApp message queue'),
('ScheduleTask', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Scheduled task management'),
('AclRecord', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Access control records'),
('PermissionRecord', 1, 0, 'ACTIVE', 35, 'GENERAL', 'Permission management'),
('PermissionRecordCustomerRoleMapping', 0, 0, 'ACTIVE', 15, 'GENERAL', 'Permission role mapping'),
('Setting', 1, 0, 'ACTIVE', 40, 'GENERAL', 'System settings management'),
('GenericAttribute', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Generic attribute system');

PRINT 'Codebase knowledge loaded: ' + CAST((SELECT COUNT(*) FROM #CodebaseKnowledge) AS VARCHAR) + ' entities';
PRINT '';

-- Create temporary table for database analysis
IF OBJECT_ID('tempdb..#DatabaseAnalysis') IS NOT NULL DROP TABLE #DatabaseAnalysis;
CREATE TABLE #DatabaseAnalysis (
    TableName NVARCHAR(128),
    SchemaName NVARCHAR(128),
    CreatedDate DATETIME,
    ModifiedDate DATETIME,
    RowCount BIGINT,
    TotalSizeMB DECIMAL(10,2),
    ForeignKeysAsParent INT,
    ForeignKeysAsChild INT,
    TotalActivity BIGINT,
    LastActivity DATETIME,
    ObjectReferences INT
);

-- Get basic table information
INSERT INTO #DatabaseAnalysis (TableName, SchemaName, CreatedDate, ModifiedDate, RowCount, TotalSizeMB, ForeignKeysAsParent, ForeignKeysAsChild, TotalActivity, LastActivity, ObjectReferences)
SELECT 
    t.name AS TableName,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.create_date AS CreatedDate,
    t.modify_date AS ModifiedDate,
    ISNULL(p.rows, 0) AS RowCount,
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS DECIMAL(10,2)) AS TotalSizeMB,
    0 AS ForeignKeysAsParent, -- Will update separately
    0 AS ForeignKeysAsChild,  -- Will update separately
    0 AS TotalActivity,       -- Will update separately
    NULL AS LastActivity,     -- Will update separately
    0 AS ObjectReferences     -- Will update separately
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.is_ms_shipped = 0
GROUP BY t.schema_id, t.name, t.object_id, t.create_date, t.modify_date, p.rows;

-- Update foreign key counts
UPDATE da
SET ForeignKeysAsParent = ISNULL(fk_counts.parent_count, 0)
FROM #DatabaseAnalysis da
LEFT JOIN (
    SELECT t.name AS table_name, COUNT(*) as parent_count
    FROM sys.foreign_keys fk
    INNER JOIN sys.tables t ON fk.referenced_object_id = t.object_id
    GROUP BY t.name
) fk_counts ON da.TableName = fk_counts.table_name;

UPDATE da
SET ForeignKeysAsChild = ISNULL(fk_counts.child_count, 0)
FROM #DatabaseAnalysis da
LEFT JOIN (
    SELECT t.name AS table_name, COUNT(*) as child_count
    FROM sys.foreign_keys fk
    INNER JOIN sys.tables t ON fk.parent_object_id = t.object_id
    GROUP BY t.name
) fk_counts ON da.TableName = fk_counts.table_name;

-- Update usage statistics
UPDATE da
SET TotalActivity = ISNULL(usage_stats.total_activity, 0),
    LastActivity = usage_stats.last_activity
FROM #DatabaseAnalysis da
LEFT JOIN (
    SELECT 
        t.name AS table_name,
        SUM(ISNULL(us.user_seeks, 0) + ISNULL(us.user_scans, 0) + 
            ISNULL(us.user_lookups, 0) + ISNULL(us.user_updates, 0)) AS total_activity,
        MAX(CASE 
            WHEN us.last_user_seek > ISNULL(us.last_user_scan, '1900-01-01')
                 AND us.last_user_seek > ISNULL(us.last_user_lookup, '1900-01-01')
                 AND us.last_user_seek > ISNULL(us.last_user_update, '1900-01-01')
            THEN us.last_user_seek
            WHEN us.last_user_scan > ISNULL(us.last_user_lookup, '1900-01-01')
                 AND us.last_user_scan > ISNULL(us.last_user_update, '1900-01-01')
            THEN us.last_user_scan
            WHEN us.last_user_lookup > ISNULL(us.last_user_update, '1900-01-01')
            THEN us.last_user_lookup
            ELSE us.last_user_update
        END) AS last_activity
    FROM sys.dm_db_index_usage_stats us
    INNER JOIN sys.tables t ON us.object_id = t.object_id
    WHERE us.database_id = DB_ID()
    GROUP BY t.name
) usage_stats ON da.TableName = usage_stats.table_name;

-- Update object references
UPDATE da
SET ObjectReferences = ISNULL(obj_refs.reference_count, 0)
FROM #DatabaseAnalysis da
LEFT JOIN (
    SELECT 
        d.referenced_entity_name AS table_name,
        COUNT(DISTINCT d.referencing_id) as reference_count
    FROM sys.sql_expression_dependencies d
    WHERE d.referenced_entity_name IS NOT NULL
    GROUP BY d.referenced_entity_name
) obj_refs ON da.TableName = obj_refs.table_name;

PRINT 'Database analysis completed for ' + CAST((SELECT COUNT(*) FROM #DatabaseAnalysis) AS VARCHAR) + ' tables';
PRINT '';

-- Final analysis with recommendations
SELECT 
    da.TableName,
    da.RowCount,
    da.TotalSizeMB,
    ISNULL(ck.MigrationStatus, 'NOT_IN_CODEBASE') AS MigrationStatus,
    ISNULL(ck.HasService, 0) AS HasService,
    ISNULL(ck.HasController, 0) AS HasController,
    ISNULL(ck.CodeReferences, 0) AS CodeReferences,
    ISNULL(ck.EntityType, 'UNKNOWN') AS EntityType,
    da.ForeignKeysAsParent,
    da.ForeignKeysAsChild,
    da.TotalActivity,
    CONVERT(VARCHAR(19), da.LastActivity, 120) AS LastActivity,
    da.ObjectReferences,
    
    -- Calculate confidence score (0-100, higher = safer to delete)
    CASE 
        -- Definitely unsafe to delete (active entities with implementations)
        WHEN ISNULL(ck.EntityType, 'UNKNOWN') IN ('CORE_ENTITY', 'GENERAL') AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'ACTIVE' THEN 0
        WHEN ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.HasController, 0) = 1 THEN 0
        WHEN ISNULL(ck.CodeReferences, 0) > 50 THEN 0
        WHEN da.ForeignKeysAsParent > 0 THEN 0  -- Other tables depend on this
        WHEN da.RowCount > 1000 THEN 0  -- Has significant data
        
        -- Disabled features (commented in migration but have implementations) - DO NOT DELETE
        WHEN ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' AND (ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.CodeReferences, 0) > 10) THEN 5
        
        -- Potential unused tables
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' THEN 95
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 THEN 85
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 THEN 75
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 THEN 65
        WHEN da.RowCount = 0 THEN 50
        WHEN da.TotalActivity = 0 AND da.ObjectReferences = 0 AND da.ForeignKeysAsParent = 0 THEN 40
        WHEN da.TotalActivity = 0 AND da.ObjectReferences = 0 THEN 30
        WHEN da.TotalActivity = 0 THEN 20
        ELSE 10
    END AS ConfidenceScore,
    
    -- Clear recommendation
    CASE 
        WHEN (da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT') THEN 'SAFE_TO_DELETE'
        WHEN (da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0) THEN 'LIKELY_SAFE_TO_DELETE'
        WHEN (da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0) THEN 'REVIEW_REQUIRED'
        WHEN (da.RowCount = 0 AND da.ForeignKeysAsParent = 0) THEN 'REVIEW_REQUIRED'
        WHEN (da.RowCount = 0) THEN 'PROBABLY_KEEP'
        ELSE 'DEFINITELY_KEEP'
    END AS Recommendation,
    
    -- Detailed reasoning
    CASE 
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' THEN 'Empty table, no relationships, no usage, commented in migration, no code implementation'
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 THEN 'Empty table, no relationships, no usage - but verify no dynamic references'
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 THEN 'Empty table, no relationships - but may have usage statistics'
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 THEN 'Empty table but has some object references'
        WHEN da.RowCount = 0 THEN 'Empty table but has foreign key relationships'
        WHEN ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' AND (ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.CodeReferences, 0) > 10) THEN 'DISABLED FEATURE - Do not delete, has full implementation'
        WHEN ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.HasController, 0) = 1 THEN 'ACTIVE ENTITY - Has service/controller implementation'
        WHEN ISNULL(ck.EntityType, 'UNKNOWN') IN ('CORE_ENTITY', 'GENERAL') THEN 'CORE SYSTEM TABLE - Essential for application'
        WHEN da.ForeignKeysAsParent > 0 THEN 'REFERENCED BY OTHER TABLES - Cannot delete safely'
        WHEN da.RowCount > 1000 THEN 'CONTAINS SIGNIFICANT DATA - Do not delete'
        ELSE 'KEEP - Active or potentially needed table'
    END AS Reasoning,
    
    ISNULL(ck.Notes, 'No codebase information available') AS CodebaseNotes,
    da.CreatedDate,
    da.ModifiedDate
    
FROM #DatabaseAnalysis da
LEFT JOIN #CodebaseKnowledge ck ON da.TableName = ck.TableName

-- Add confidence score calculation for ORDER BY
ORDER BY 
    CASE 
        WHEN ISNULL(ck.EntityType, 'UNKNOWN') IN ('CORE_ENTITY', 'GENERAL') AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'ACTIVE' THEN 0
        WHEN ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.HasController, 0) = 1 THEN 0
        WHEN ISNULL(ck.CodeReferences, 0) > 50 THEN 0
        WHEN da.ForeignKeysAsParent > 0 THEN 0
        WHEN da.RowCount > 1000 THEN 0
        WHEN ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' AND (ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.CodeReferences, 0) > 10) THEN 5
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' THEN 95
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 THEN 85
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 THEN 75
        WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 THEN 65
        WHEN da.RowCount = 0 THEN 50
        WHEN da.TotalActivity = 0 AND da.ObjectReferences = 0 AND da.ForeignKeysAsParent = 0 THEN 40
        WHEN da.TotalActivity = 0 AND da.ObjectReferences = 0 THEN 30
        WHEN da.TotalActivity = 0 THEN 20
        ELSE 10
    END DESC, 
    da.RowCount ASC, 
    da.TableName;

-- Summary statistics
PRINT '';
PRINT '========================================';
PRINT 'ANALYSIS SUMMARY';
PRINT '========================================';

DECLARE @TotalTables INT, @SafeToDelete INT, @LikelySafe INT, @ReviewRequired INT, @DefinitelyKeep INT;

SELECT @TotalTables = COUNT(*) FROM #DatabaseAnalysis;

SELECT @SafeToDelete = COUNT(*) 
FROM #DatabaseAnalysis da
LEFT JOIN #CodebaseKnowledge ck ON da.TableName = ck.TableName
WHERE da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT';

SELECT @LikelySafe = COUNT(*) 
FROM #DatabaseAnalysis da
LEFT JOIN #CodebaseKnowledge ck ON da.TableName = ck.TableName
WHERE da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') != 'COMMENTED_OUT';

SELECT @ReviewRequired = COUNT(*) 
FROM #DatabaseAnalysis da
WHERE da.RowCount = 0 AND da.ForeignKeysAsParent = 0;

SELECT @DefinitelyKeep = @TotalTables - @SafeToDelete - @LikelySafe - @ReviewRequired;

PRINT 'Total Tables Analyzed: ' + CAST(@TotalTables AS VARCHAR);
PRINT 'Safe to Delete (95%): ' + CAST(@SafeToDelete AS VARCHAR);
PRINT 'Likely Safe to Delete (85%): ' + CAST(@LikelySafe AS VARCHAR);
PRINT 'Review Required (60-84%): ' + CAST(@ReviewRequired AS VARCHAR);  
PRINT 'Definitely Keep (0-59%): ' + CAST(@DefinitelyKeep AS VARCHAR);
PRINT '';

IF @SafeToDelete > 0
BEGIN
    PRINT '⚠️  TABLES RECOMMENDED FOR DELETION:';
    SELECT da.TableName, 95 AS ConfidenceScore, 'Empty table, no relationships, no usage, commented in migration, no code implementation' AS Reasoning
    FROM #DatabaseAnalysis da
    LEFT JOIN #CodebaseKnowledge ck ON da.TableName = ck.TableName
    WHERE da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT';
    PRINT '';
END

IF @LikelySafe > 0
BEGIN
    PRINT '⚠️  TABLES LIKELY SAFE FOR DELETION (verify first):';
    SELECT da.TableName, 85 AS ConfidenceScore, da.RowCount, 'Empty table, no relationships, no usage - but verify no dynamic references' AS Reasoning
    FROM #DatabaseAnalysis da
    LEFT JOIN #CodebaseKnowledge ck ON da.TableName = ck.TableName
    WHERE da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') != 'COMMENTED_OUT';
    PRINT '';
END

PRINT '========================================';
PRINT 'IMPORTANT SAFETY NOTES:';
PRINT '1. Only delete tables with Recommendation = SAFE_TO_DELETE';
PRINT '2. NEVER delete tables marked as DISABLED_FEATURE';
PRINT '3. Always backup database before deletion';
PRINT '4. Test in development environment first';
PRINT '5. Tables with CodeReferences > 0 likely have implementations';
PRINT '========================================';

-- Cleanup
DROP TABLE #CodebaseKnowledge;
DROP TABLE #DatabaseAnalysis;

PRINT '';
PRINT '✅ Analysis Complete!';
PRINT 'Review the results above to identify unused tables.';
PRINT 'Focus on tables with Recommendation = SAFE_TO_DELETE';