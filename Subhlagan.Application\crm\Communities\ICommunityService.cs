﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Communities
{
    public partial interface ICommunityService
    {
        Task InsertCommunityAsync(Community community);
        Task UpdateCommunityAsync(Community community);
        Task DeleteCommunityAsync(Community community);
        Task<Community> GetCommunityByIdAsync(int communityId);
        Task<IList<Community>> GetAllCommunitiesAsync(bool showHidden = false);
    }

}
