﻿using Subhlagan.Core;
using Subhlagan.Core.Domain.Enum;
using Subhlagan.Core.Domain;
using System.Threading.Tasks;

namespace Subhlagan.Application.OccupationAndBusinesses
{
    public partial interface IOccupationAndBusinessService
    {
        Task InsertOccupationAndBusinessAsync(OccupationAndBusiness entity);
        Task UpdateOccupationAndBusinessAsync(OccupationAndBusiness entity);
        Task DeleteOccupationAndBusinessAsync(OccupationAndBusiness entity);
        Task<OccupationAndBusiness> GetOccupationAndBusinessByIdAsync(int id);
        Task<IPagedList<OccupationAndBusiness>> GetAllOccupationAndBusinessesAsync(
            int? profileId = null,
            string firmName = null,
            int natureOfBusinessId = 0,
            decimal? annualRevenueFrom = null,
            decimal? annualRevenueTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false);
    }

}
