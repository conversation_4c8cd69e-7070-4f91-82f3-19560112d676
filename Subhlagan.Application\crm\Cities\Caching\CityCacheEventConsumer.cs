﻿using System.Threading.Tasks;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;

namespace Subhlagan.Application.Cities.Caching
{
    public partial class CityCacheEventConsumer : CacheEventConsumer<City>
    {
        protected override async Task ClearCacheAsync(City entity, EntityEventType entityEventType)
        {
            await RemoveByPrefixAsync(KsEntityCacheDefaults<City>.Prefix);
            await base.ClearCacheAsync(entity);
        }
    }
}
