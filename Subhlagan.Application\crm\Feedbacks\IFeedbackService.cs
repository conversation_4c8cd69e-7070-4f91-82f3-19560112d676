﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Feedbacks
{
    public interface IFeedbackService
    {
         Task InsertFeedbackAsync(Feedback feedback);
        Task UpdateFeedbackAsync(Feedback feedback);
        Task DeleteFeedbackAsync(Feedback feedback);
        Task<Feedback> GetFeedbackByIdAsync(int feedbackId);
        Task<IList<Feedback>> GetAllFeedbacksAsync();
        Task<IList<Feedback>> GetAllFeedbacksByProfileIdAsync(int profileId);
    }
}
