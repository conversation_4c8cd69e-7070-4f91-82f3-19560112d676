﻿using Subhlagan.Core;
using Subhlagan.Core.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Core.Domain.Enum;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Profiles;
using Subhlagan.Application;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.Routing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Subhlagan.Application.MatchKundalis
{
    public partial class MatchKundaliService : IMatchKundaliService
    {
        private readonly EnhancedEntityRepository<MatchKundali> _matchKundaliRepository;

        private readonly IProfileService _profileService;
        private readonly IKsFileProvider _fileProvider;
        private readonly IUrlHelperFactory _urlHelperFactory;
        private readonly IActionContextAccessor _actionContextAccessor;


        private readonly IProfileMatchService _profileMatchService;
        private readonly EnhancedEntityRepository<Profile> _profileRepository;

        public MatchKundaliService(EnhancedEntityRepository<MatchKundali> matchKundaliRepository, IProfileService profileService, IKsFileProvider fileProvider, IUrlHelperFactory urlHelperFactory, IActionContextAccessor actionContextAccessor, IProfileMatchService profileMatchService, EnhancedEntityRepository<Profile> profileRepository)
        {
            _matchKundaliRepository = matchKundaliRepository;

            _profileService = profileService;
            _fileProvider = fileProvider;
            _urlHelperFactory = urlHelperFactory;
            _actionContextAccessor = actionContextAccessor;


            _profileMatchService = profileMatchService;
            _profileRepository = profileRepository;
        }

        public async Task InsertMatchKundaliAsync(MatchKundali matchKundali)
        {
            if (matchKundali == null)
                throw new ArgumentNullException(nameof(matchKundali));

            await _matchKundaliRepository.InsertAsync(matchKundali);
        }

        public async Task UpdateMatchKundaliAsync(MatchKundali matchKundali)
        {
            if (matchKundali == null)
                throw new ArgumentNullException(nameof(matchKundali));

            await _matchKundaliRepository.UpdateAsync(matchKundali);
        }

        public async Task DeleteMatchKundaliAsync(MatchKundali matchKundali)
        {
            if (matchKundali == null)
                throw new ArgumentNullException(nameof(matchKundali));

            await _matchKundaliRepository.DeleteAsync(matchKundali);
        }

        public async Task<MatchKundali> GetMatchKundaliByIdAsync(int matchKundaliId)
        {
            return await _matchKundaliRepository.GetByIdAsync(matchKundaliId);
        }

        public async Task<MatchKundali> GetMatchKundaliByProfileIdsAsync(int maleProfileId, int femaleProfileId)
        {
            if (maleProfileId <= 0 || femaleProfileId <= 0)
                throw new ArgumentException("Profile IDs must be greater than 0.");

            var matchKundali = await _matchKundaliRepository.Table
                .FirstOrDefaultAsync(kundali =>
                    kundali.MaleProfileId == maleProfileId &&
                    kundali.FemaleProfileId == femaleProfileId);

            if (matchKundali == null)
                return null; // Return null if no match is found

            // Ensure related profile data is fetched only if the entity exists
            matchKundali.MaleProfile = await _profileService.GetProfileByIdAsync(matchKundali.MaleProfileId);
            matchKundali.FemaleProfile = await _profileService.GetProfileByIdAsync(matchKundali.FemaleProfileId);

            return matchKundali;
        }

        public async Task<IPagedList<MatchKundali>> GetAllMatchKundalisAsync(
            int? maleProfileId = null,
            int? femaleProfileId = null,
            decimal? totalGunFrom = null,
            decimal? totalGunTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            DateTime? CreatedOnFromDate = null,
            DateTime? CreatedOnToDate = null,
            int userId = 0,
            IList<int> maleProfileIds = null,
            IList<int> femaleProfileIds = null,
            IList<int> matchKundaliIds = null, List<int> matchResultIds = null,
            IList<int> userIds = null)
        {
            var query = _matchKundaliRepository.Table;

            if (maleProfileId.HasValue)
                query = query.Where(m => m.MaleProfileId == maleProfileId.Value);

            if (femaleProfileId.HasValue)
                query = query.Where(m => m.FemaleProfileId == femaleProfileId.Value);

            if (totalGunFrom.HasValue)
                query = query.Where(m => m.TotalGun >= totalGunFrom.Value);

            if (totalGunTo.HasValue)
                query = query.Where(m => m.TotalGun <= totalGunTo.Value);

            if (CreatedOnFromDate.HasValue)
                query = query.Where(m => m.CreatedOnUtc >= CreatedOnFromDate.Value);

            if (CreatedOnToDate.HasValue)
                query = query.Where(m => m.CreatedOnUtc <= CreatedOnToDate.Value);

            if (userId > 0)
                query = query.Where(m => m.UserId == userId);

            // filter by user  Ids
            if (userIds != null && userIds.Any())
                query = query.Where(m => userIds.Contains(m.UserId));

            // filter by Male Profile Id
            if (maleProfileIds != null && maleProfileIds.Any())
                query = query.Where(m => maleProfileIds.Contains(m.MaleProfileId));

            // filter by female Profile ID
            if (femaleProfileIds != null && femaleProfileIds.Any())
                query = query.Where(m => femaleProfileIds.Contains(m.FemaleProfileId));

            if (matchKundaliIds != null && matchKundaliIds.Any())
                query = query.Where(m => matchKundaliIds.Contains(m.Id));

            //if (matchResultIds?.Any() ?? false)
            //{
            //    var matchResultProfileIds = new HashSet<int>(await _profileMatchService.GetProfileIdsByMatchResultAsync(matchResultIds));

            //    query = query.Where(p => matchResultProfileIds.Contains(p.MaleProfileId) &&
            //                             matchResultProfileIds.Contains(p.FemaleProfileId));
            //}

            if (matchResultIds?.Any() ?? false)
            {
                query = from pm in query
                        join initiator in _profileRepository.Table on pm.MaleProfileId equals initiator.Id
                        join matched in _profileRepository.Table on pm.FemaleProfileId equals matched.Id
                        where matchResultIds.Contains(initiator.MatchResultId) &&
                              matchResultIds.Contains(matched.MatchResultId)
                        select pm;
            }

            query = query.OrderByDescending(m => m.Id);

            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);
        }

        public (string Message, string Color, decimal TotalGun) GetTotalGunDescription(MatchKundali matchKundali)
        {
            if (matchKundali == null)
                return ("Kundali match data is not available. Please click on the 'Calculate' button to generate the match details.", "gray", 0);

            // Use G29 for precise formatting
            string totalGunFormatted = matchKundali.TotalGun.ToG29();

            if (matchKundali.TotalGun >= 18)
            {
                return ($"Great compatibility! {totalGunFormatted} out of 36 Gun matched.", "green", matchKundali.TotalGun);
            }
            else if (matchKundali.TotalGun < 18 && matchKundali.TotalGun >= 12)
            {
                return ($"Compatibility is low. Only {totalGunFormatted} out of 36 Gun matched.", "orange", matchKundali.TotalGun);
            }
            else
            {
                return ($"Compatibility is very low. Only {totalGunFormatted} out of 36 Gun matched.", "red", matchKundali.TotalGun);
            }
        }


        public async Task<string> GetDownloadPdfUrlAsync(MatchKundali matchKundali)
        {
            if (matchKundali == null || string.IsNullOrEmpty(matchKundali.PdfFileName))
                throw new ArgumentException("MatchKundali or PdfFileName cannot be null or empty.");

            var domainUrl = CompanyInfo.Url;
            if (string.IsNullOrEmpty(domainUrl))
                throw new Exception("URL cannot be null");

            var urlHelper = _urlHelperFactory.GetUrlHelper(_actionContextAccessor.ActionContext);
            var url = urlHelper.RouteUrl("ViewMatchKundaliPdf", new { name = matchKundali.PdfFileName });

            return new Uri(new Uri(domainUrl), url).AbsoluteUri;
        }

        public async Task<string> GetDownloadPdfAsync(MatchKundali matchKundali)
        {
            try
            {
                var pdfUrl = await GetDownloadPdfUrlAsync(matchKundali);
                return $"<a href=\"{System.Net.WebUtility.HtmlEncode(pdfUrl)}\" target=\"_blank\">Download Details Report (PDF)</a>";
            }
            catch
            {
                return "PDF is not available.";
            }
        }

        public string GetManglikStatus(MatchKundali matchKundali)
        {
            if (matchKundali == null)
                //return "Kundali match data is not available. Please click on the 'Calculate' button to generate the match details.";
                return string.Empty;

            var maleStatus = matchKundali.MaleManglikStatus.GetDisplayName();
            var femaleStatus = matchKundali.FemaleManglikStatus.GetDisplayName();

            if (string.IsNullOrEmpty(maleStatus) || matchKundali.MaleManglikStatus == KundaliManglikStatus.Unknown)
                maleStatus = "Unknown";

            if (string.IsNullOrEmpty(femaleStatus) || matchKundali.FemaleManglikStatus == KundaliManglikStatus.Unknown)
                femaleStatus = "Unknown";

            return $"Male manglik status: {maleStatus}, Female manglik status: {femaleStatus}.";
        }
    }
}
