﻿using Subhlagan.Core;
using Subhlagan.Core.Infrastructure;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace Subhlagan.Application.FileDownload
{
    public class FileDownloadService : IFileDownloadService
    {
        private readonly IKsFileProvider _fileProvider;
        private readonly HttpClient _httpClient;

        public FileDownloadService(IKsFileProvider fileProvider, HttpClient httpClient)
        {
            _fileProvider = fileProvider;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Downloads a file from the specified URL and saves it to the specified directory.
        /// If a file name is not provided, a unique name will be generated.
        /// </summary>
        /// <param name="downloadUrl">The URL to download the file from.</param>
        /// <param name="saveDirectory">The directory to save the downloaded file.</param>
        /// <param name="fileName">Optional. The name of the file to save. If null, a unique name will be generated.</param>
        /// <returns>The name of the saved file.</returns>
        /// <exception cref="ArgumentException">Thrown when the download URL or save directory is null or empty.</exception>
        /// <exception cref="HttpRequestException">Thrown when the HTTP request fails.</exception>
        /// <exception cref="IOException">Thrown when file operations fail.</exception>
        /// <exception cref="Exception">Thrown for any unexpected errors during the file download process.</exception>
        public async Task<string> DownloadFileAsync(string downloadUrl, string saveDirectory, string fileName = null)
        {
            if (string.IsNullOrWhiteSpace(downloadUrl))
                throw new ArgumentException("Download URL cannot be null or empty.", nameof(downloadUrl));

            if (string.IsNullOrWhiteSpace(saveDirectory))
                throw new ArgumentException("Save directory cannot be null or empty.", nameof(saveDirectory));

            // Ensure the save directory exists
            var absoluteSaveDirectory = _fileProvider.MapPath(saveDirectory);
            if (!_fileProvider.DirectoryExists(absoluteSaveDirectory))
                _fileProvider.CreateDirectory(absoluteSaveDirectory);

            // Generate file name if not provided
            fileName ??= $"{Guid.NewGuid()}_{CommonHelper.GenerateRandomDigitCode(4)}.pdf";
            var filePath = _fileProvider.Combine(absoluteSaveDirectory, fileName);

            try
            {
                // Download the file
                var response = await _httpClient.GetAsync(downloadUrl);

                if (!response.IsSuccessStatusCode)
                    throw new Exception($"Failed to download the file. Status code: {response.StatusCode}");

                // Save the file
                await using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None);
                await response.Content.CopyToAsync(fileStream);

                return fileName; // Return the saved file path
            }
            catch (HttpRequestException ex)
            {
                // Log the exception if logging is implemented
                throw new InvalidOperationException($"HTTP request failed: {ex.Message}", ex);
            }
            catch (IOException ex)
            {
                throw new InvalidOperationException($"File operation failed: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"An unexpected error occurred: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Constructs the absolute file path based on the provided directory path and file name.
        /// </summary>
        /// <param name="directoryPath">The directory where the file is located.</param>
        /// <param name="fileName">The name of the file.</param>
        /// <returns>The absolute file path.</returns>
        /// <exception cref="ArgumentException">Thrown when the directory path or file name is null or empty.</exception>
        public Task<string> GetFilePathAsync(string directoryPath, string fileName)
        {
            if (string.IsNullOrWhiteSpace(directoryPath))
                throw new ArgumentException("Directory path cannot be null or empty.", nameof(directoryPath));

            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("File name cannot be null or empty.", nameof(fileName));

            var absoluteDirectory = _fileProvider.MapPath(directoryPath);
            var filePath = _fileProvider.Combine(absoluteDirectory, fileName);

            return Task.FromResult(filePath);
        }
    }
}
