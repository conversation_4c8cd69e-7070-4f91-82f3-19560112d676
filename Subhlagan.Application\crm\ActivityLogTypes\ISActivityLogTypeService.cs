﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Subhlagan.Application.ActivityLogTypes
{
    public interface ISActivityLogTypeService
    {
        Task AddOrUpdateAsync();        
        Task<IList<ActivityLogType>> GetAllActivityTypesAsync();
        Task PrepareActivityLogTypesAsync(IList<SelectListItem> items, bool withSpecialDefaultItem = true, string defaultItemText = null);
    }
}
