﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.Headings
{
    public partial interface IHeadingService
    {
        Task InsertHeadingAsync(Heading heading);
        Task UpdateHeadingAsync(Heading heading);
        Task DeleteHeadingAsync(Heading heading);
        Task<Heading> GetHeadingByIdAsync(int headingId);
        Task<IPagedList<Heading>> GetAllHeadingsAsync(
            string subject,
            DateTime? createdOnFrom = null,
            DateTime? createdOnTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false);

        Task<IList<Heading>> GetAllHeadingsAsync();

        Task DeleteHeadingAsync(List<Heading> news);
    }

}
