﻿using Subhlagan.Core.Domain.Customers;
using Subhlagan.Application.Caching;
using System.Threading.Tasks;

namespace Subhlagan.Application.Customers.Caching
{
    public partial class CustomerCacheEventConsumer : CacheEventConsumer<Customer>
    {
        //protected override async Task ClearCacheAsync(Customer entity, EntityEventType entityEventType)
        //{
        //    await RemoveByPrefixAsync(KsCommonDefaults.CustomerProfilesPrefix);
        //}
    }
}
