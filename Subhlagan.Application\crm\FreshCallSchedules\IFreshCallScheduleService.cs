﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.FreshCallSchedules
{
    public partial interface IFreshCallScheduleService
    {
        Task InsertFreshCallScheduleAsync(FreshCallSchedule schedule);
        Task UpdateFreshCallScheduleAsync(FreshCallSchedule schedule);
        Task DeleteFreshCallScheduleAsync(FreshCallSchedule schedule);
        Task<FreshCallSchedule> GetFreshCallScheduleByIdAsync(int id);
        Task<IPagedList<FreshCallSchedule>> GetAllFreshCallSchedulesAsync(
            int? initiatorProfileId = null,
            int? matchedProfileId = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false);
        Task InsertFreshCallScheduleAsync(Profile initiatorProfile, IList<Profile> matchedProfiles);
        Task<IList<FreshCallSchedule>> GetDueSchedulesAsync();
    }
}
