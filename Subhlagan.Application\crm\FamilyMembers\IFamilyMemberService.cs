﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.FamilyMembers
{
    public partial interface IFamilyMemberService
    {
        Task InsertFamilyMemberAsync(FamilyMember familyMember);
        Task UpdateFamilyMemberAsync(FamilyMember familyMember);
        Task DeleteFamilyMemberAsync(FamilyMember familyMember);
        Task<FamilyMember> GetFamilyMemberByIdAsync(int familyMemberId);
        Task<IPagedList<FamilyMember>> GetAllFamilyMembersAsync(
            string name = null,
            int? relationId = null,
            bool? isDeceased = null,
            bool? isPrimaryContact = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            int profileId = 0);
    }

}
