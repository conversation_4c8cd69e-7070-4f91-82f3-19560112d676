﻿using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Application.FeedbackForms;

namespace Subhlagan.Application.FeedbackForms
{
    public partial class FeedbackFormService : IFeedbackFormService
    {
        #region Fields

        private readonly EnhancedEntityRepository<FeedbackForm> _feedbackFormRepository;
        private readonly IStaticCacheManager _staticCacheManager;

        #endregion

        #region Ctor

        public FeedbackFormService( EnhancedEntityRepository<FeedbackForm> feedbakFormRepository, IStaticCacheManager staticCacheManager)
        {
            _feedbackFormRepository = feedbakFormRepository;
            _staticCacheManager = staticCacheManager;
        }

        #endregion

        #region Methods

        public virtual async Task InsertFeedbackFormAsync(FeedbackForm feedbackForm)
        {
            if (feedbackForm == null)
                throw new ArgumentNullException(nameof(feedbackForm));

            await _feedbackFormRepository.InsertAsync(feedbackForm);
        }

        public virtual async Task UpdateFeedbackFormAsync(FeedbackForm feedbackForm)
        {
            if (feedbackForm == null)
                throw new ArgumentNullException(nameof(feedbackForm));

            await _feedbackFormRepository.UpdateAsync(feedbackForm);
        }

        public virtual async Task DeleteFeedbackFormAsync(FeedbackForm feedbackForm)
        {
            if (feedbackForm == null)
                throw new ArgumentNullException(nameof(feedbackForm));

            await _feedbackFormRepository.DeleteAsync(feedbackForm);
        }

        public virtual async Task<FeedbackForm> GetFeedbackFormByIdAsync(int feedbackFormId)
        {
            return await _feedbackFormRepository.GetByIdAsync(feedbackFormId, cache => default);
        }

        public virtual async Task<IPagedList<FeedbackForm>> GetAllFeedbackFormsAsync(int residencePropertyTypeId = 0, int officePropertyTypeId = 0, int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false)
        {
            var preferences = await _feedbackFormRepository.GetAllPagedAsync(query =>
            {
                if (residencePropertyTypeId > 0)
                    query = query.Where(f => f.ResidencePropertyTypeId== residencePropertyTypeId);

                if (officePropertyTypeId>0)
                    query = query.Where(f=>f.OfficePropertyTypeId == officePropertyTypeId);

                return query;
            }, pageIndex, pageSize, getOnlyTotalCount);

            return preferences;
        }

        public virtual async Task<FeedbackForm> GetFeedbackFormByProfileIdAsync(int profileId, bool getFromDatabase = false)
        {
            if (profileId <= 0)
                return null;

            if (getFromDatabase)
                return await _feedbackFormRepository.Table
                    .Where(p => p.ProfileId == profileId)
                    .SingleOrDefaultAsync();

            var cacheKey = _staticCacheManager.PrepareKeyForDefaultCache(PageBaaSDefaults.FeedbackFormByProfileIdCacheKey, profileId);

            return await _staticCacheManager.GetAsync(cacheKey, async () =>
            {
                return await _feedbackFormRepository.Table
                    .Where(p => p.ProfileId == profileId)
                    .SingleOrDefaultAsync();
            });
        }
        
        #endregion
    }

}
