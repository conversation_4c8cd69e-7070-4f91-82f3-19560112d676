﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Cities
{
    public interface ICityService
    {
        Task InsertCityAsync(City city);
        Task UpdateCityAsync(City city);
        Task DeleteCityAsync(City city);
        Task<City> GetCityByIdAsync(int cityId);
        Task<IList<City>> GetAllCitiesAsync(int countryId = 0, int stateProvinceId = 0, bool includeDeleted = false);
        Task<IList<City>> GetCitiesByCountryIdAsync(int countryId);
        Task<IList<City>> GetCitiesByStateProvinceIdAsync(int stateProvinceId, bool showHidden = false);
        Task<City> GetCityByNameAsync(string name);

        Task<IPagedList<City>> GetAllCitiesAsync(string name = null, int? countryId = null, int? stateProvinceId = null,
        int pageIndex = 0, int pageSize = int.MaxValue, bool includeDeleted = false, IList<int> cityIds = null);
        Task<IList<City>> GetCityByIdsAsync(IList<int> cityIds);
        Task<IList<string>> GetDuplicateNameWarningsAsync(string name, int? stateProvinceId);
    }

}
