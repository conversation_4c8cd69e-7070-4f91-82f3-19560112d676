﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain.Enum;
using Subhlagan.Core.Domain;
using System;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Infrastructure;

namespace Subhlagan.Application.OccupationAndBusinesses
{
    public partial class OccupationAndBusinessService : IOccupationAndBusinessService
    {
        private readonly EnhancedEntityRepository<OccupationAndBusiness> _repository;

        public OccupationAndBusinessService(EnhancedEntityRepository<OccupationAndBusiness> repository)
        {
            _repository = repository;
        }

        public virtual async Task InsertOccupationAndBusinessAsync(OccupationAndBusiness entity)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            await _repository.InsertAsync(entity);
        }

        public virtual async Task UpdateOccupationAndBusinessAsync(OccupationAndBusiness entity)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            await _repository.UpdateAsync(entity);
        }

        public virtual async Task DeleteOccupationAndBusinessAsync(OccupationAndBusiness entity)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));

            await _repository.DeleteAsync(entity);
        }

        public virtual async Task<OccupationAndBusiness> GetOccupationAndBusinessByIdAsync(int id)
        {
            return await _repository.GetByIdAsync(id, cache => default);
        }

        public virtual async Task<IPagedList<OccupationAndBusiness>> GetAllOccupationAndBusinessesAsync(
            int? profileId = null,
            string firmName = null,
            int natureOfBusinessId = 0,
            decimal? annualRevenueFrom = null,
            decimal? annualRevenueTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false)
        {
            var query = _repository.Table;

            if (profileId.HasValue)
                query = query.Where(x => x.ProfileId == profileId.Value);

            if (!string.IsNullOrEmpty(firmName))
                query = query.Where(x => x.FirmName.Contains(firmName));

            if (natureOfBusinessId > 0)
                query = query.Where(x => x.NatureOfBusinessId == natureOfBusinessId);

            if (annualRevenueFrom.HasValue)
                query = query.Where(x => x.AnnualRevenue >= annualRevenueFrom.Value);

            if (annualRevenueTo.HasValue)
                query = query.Where(x => x.AnnualRevenue <= annualRevenueTo.Value);

            query = query.OrderBy(x => x.FirmName).ThenBy(x => x.AnnualRevenue);

            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);
        }
    }

}
