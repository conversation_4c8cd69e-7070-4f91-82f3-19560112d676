-- =====================================================================
-- SIMPLE UNUSED TABLE ANALYSIS FOR SubhLaganNew_v1
-- =====================================================================
-- Purpose: Simple, guaranteed-to-work script to identify unused tables
-- Database: SubhLaganNew_v1 (Matrimonial CRM System)
-- 
-- INSTRUCTIONS:
-- 1. Open SQL Server Management Studio
-- 2. Connect to: Localhost\SQLEXPRESS
-- 3. Select Database: SubhLaganNew_v1
-- 4. Execute this entire script
-- 5. Review results - focus on tables marked as "SAFE_TO_DELETE"
-- =====================================================================

USE SubhLaganNew_v1;

PRINT '========================================';
PRINT 'SUBHLAGAN CRM - SIMPLE UNUSED TABLE ANALYSIS';
PRINT 'Database: SubhLaganNew_v1';
PRINT 'Analysis Date: ' + CONVERT(varchar, GETDATE(), 120);
PRINT '========================================';
PRINT '';

-- Step 1: Get all user tables with basic information
SELECT 
    t.name AS TableName,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    ISNULL(p.rows, 0) AS RowCount,
    t.create_date AS CreatedDate,
    t.modify_date AS ModifiedDate,
    
    -- Check if table is in our known entities list (based on codebase analysis)
    CASE 
        WHEN t.name = 'AstroCity' THEN 'UNUSED - Commented in migration, no service/controller'
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 'DISABLED_FEATURE - Has implementation but commented in migration'
        WHEN t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory') THEN 'ACTIVE_ENTITY - Has service and controller'
        WHEN t.name IN ('ProfileHobbyMapping', 'ProfileProfileCategoryMapping', 'ProfileRestrictedMapping', 'ProfileTransferProfileMapping', 'ProfileUserMapping', 'RefineSearchProfileSpecificationAttributeMapping', 'UserEmailAccountMapping') THEN 'MAPPING_TABLE - Used for relationships'
        WHEN t.name IN ('Customer', 'CustomerAddressMapping', 'CustomerCustomerRoleMapping', 'CustomerPassword', 'CustomerRole', 'Address', 'Country', 'StateProvince', 'Language', 'LocaleStringResource', 'LocalizedProperty', 'ActivityLog', 'ActivityLogType', 'Log', 'Picture', 'PictureBinary', 'Download', 'EmailAccount', 'MessageTemplate', 'QueuedEmail', 'QueuedEmailAttachment', 'MassEmailCampaign', 'MassEmailLog', 'MassEmailRecipient', 'WhatsAppQueuedMessageEntity', 'ScheduleTask', 'AclRecord', 'PermissionRecord', 'PermissionRecordCustomerRoleMapping', 'Setting', 'GenericAttribute') THEN 'PLATFORM_TABLE - Core framework table'
        ELSE 'UNKNOWN - Not in codebase analysis'
    END AS CodebaseStatus,
    
    -- Recommendation based on codebase knowledge and basic checks
    CASE 
        WHEN t.name = 'AstroCity' AND ISNULL(p.rows, 0) = 0 THEN 'SAFE_TO_DELETE'
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 'DO_NOT_DELETE - Disabled feature with full implementation'
        WHEN t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory') THEN 'KEEP - Active entity'
        WHEN t.name IN ('ProfileHobbyMapping', 'ProfileProfileCategoryMapping', 'ProfileRestrictedMapping', 'ProfileTransferProfileMapping', 'ProfileUserMapping', 'RefineSearchProfileSpecificationAttributeMapping', 'UserEmailAccountMapping') THEN 'KEEP - Mapping table'
        WHEN t.name IN ('Customer', 'CustomerAddressMapping', 'CustomerCustomerRoleMapping', 'CustomerPassword', 'CustomerRole', 'Address', 'Country', 'StateProvince', 'Language', 'LocaleStringResource', 'LocalizedProperty', 'ActivityLog', 'ActivityLogType', 'Log', 'Picture', 'PictureBinary', 'Download', 'EmailAccount', 'MessageTemplate', 'QueuedEmail', 'QueuedEmailAttachment', 'MassEmailCampaign', 'MassEmailLog', 'MassEmailRecipient', 'WhatsAppQueuedMessageEntity', 'ScheduleTask', 'AclRecord', 'PermissionRecord', 'PermissionRecordCustomerRoleMapping', 'Setting', 'GenericAttribute') THEN 'KEEP - Platform table'
        WHEN ISNULL(p.rows, 0) = 0 THEN 'REVIEW_REQUIRED - Empty table not in codebase'
        ELSE 'REVIEW_REQUIRED - Unknown table with data'
    END AS Recommendation,
    
    -- Simple confidence score
    CASE 
        WHEN t.name = 'AstroCity' AND ISNULL(p.rows, 0) = 0 THEN 95
        WHEN t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption') THEN 5
        WHEN t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory') THEN 0
        WHEN t.name IN ('ProfileHobbyMapping', 'ProfileProfileCategoryMapping', 'ProfileRestrictedMapping', 'ProfileTransferProfileMapping', 'ProfileUserMapping', 'RefineSearchProfileSpecificationAttributeMapping', 'UserEmailAccountMapping') THEN 0
        WHEN t.name IN ('Customer', 'CustomerAddressMapping', 'CustomerCustomerRoleMapping', 'CustomerPassword', 'CustomerRole', 'Address', 'Country', 'StateProvince', 'Language', 'LocaleStringResource', 'LocalizedProperty', 'ActivityLog', 'ActivityLogType', 'Log', 'Picture', 'PictureBinary', 'Download', 'EmailAccount', 'MessageTemplate', 'QueuedEmail', 'QueuedEmailAttachment', 'MassEmailCampaign', 'MassEmailLog', 'MassEmailRecipient', 'WhatsAppQueuedMessageEntity', 'ScheduleTask', 'AclRecord', 'PermissionRecord', 'PermissionRecordCustomerRoleMapping', 'Setting', 'GenericAttribute') THEN 0
        WHEN ISNULL(p.rows, 0) = 0 THEN 60
        ELSE 20
    END AS SafetyScore

FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0
ORDER BY 
    CASE 
        WHEN t.name = 'AstroCity' AND ISNULL(p.rows, 0) = 0 THEN 95
        ELSE 0
    END DESC,
    ISNULL(p.rows, 0) ASC,
    t.name;

PRINT '';
PRINT '========================================';
PRINT 'ADDITIONAL ANALYSIS';
PRINT '========================================';

-- Check for foreign key dependencies
PRINT 'Foreign Key Dependencies:';
SELECT 
    tp.name AS ParentTable,
    tr.name AS ReferencedTable,
    fk.name AS ForeignKeyName
FROM sys.foreign_keys AS fk
INNER JOIN sys.tables AS tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables AS tr ON fk.referenced_object_id = tr.object_id
WHERE tp.is_ms_shipped = 0 AND tr.is_ms_shipped = 0
ORDER BY tp.name, tr.name;

PRINT '';
PRINT 'Tables with Zero Rows:';
SELECT 
    t.name AS TableName,
    ISNULL(p.rows, 0) AS RowCount,
    t.create_date AS CreatedDate
FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 AND ISNULL(p.rows, 0) = 0
ORDER BY t.create_date DESC;

PRINT '';
PRINT '========================================';
PRINT 'SUMMARY AND RECOMMENDATIONS';
PRINT '========================================';

DECLARE @TotalTables INT;
DECLARE @SafeToDelete INT;
DECLARE @DisabledFeatures INT;
DECLARE @ActiveTables INT;

SELECT @TotalTables = COUNT(*) FROM sys.tables WHERE is_ms_shipped = 0;

SELECT @SafeToDelete = COUNT(*) 
FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 
  AND t.name = 'AstroCity' 
  AND ISNULL(p.rows, 0) = 0;

SELECT @DisabledFeatures = COUNT(*) 
FROM sys.tables t
WHERE t.is_ms_shipped = 0 
  AND t.name IN ('Card', 'ProfileAttribute', 'ProfileAttributeValue', 'ProfileAttributeGroup', 'ProfileSpecificationAttribute', 'ProfileSpecificationAttributeOption');

SELECT @ActiveTables = COUNT(*) 
FROM sys.tables t
WHERE t.is_ms_shipped = 0 
  AND t.name IN ('ActionStatus', 'City', 'ClientMeeting', 'Community', 'ContactExchange', 'Education', 'EducationArea', 'EducationLevel', 'FamilyMember', 'Feedback', 'FollowUp', 'FreshCallSchedule', 'Gotra', 'Heading', 'Hobby', 'MarriageConfirmation', 'MatchKundali', 'MedicalHistory', 'Meeting', 'MeetingStatus', 'Occupation', 'OccupationAndBusiness', 'Office', 'OrderPaymentHistory', 'Package', 'PartnerPreference', 'Profile', 'ProfileCategory', 'ProfileContact', 'ProfileMatchInteraction', 'ProfileMatchStatus', 'ProfilePauseRecord', 'ProfileStatusHistory', 'ProfileTransfer', 'Qualification', 'Reason', 'Remark', 'ShortList', 'User', 'UserWorkingHoursHistory');

PRINT 'Total Tables: ' + CAST(@TotalTables AS VARCHAR);
PRINT 'Safe to Delete: ' + CAST(@SafeToDelete AS VARCHAR);
PRINT 'Disabled Features (Do NOT Delete): ' + CAST(@DisabledFeatures AS VARCHAR);
PRINT 'Active Tables (Keep): ' + CAST(@ActiveTables AS VARCHAR);

PRINT '';
PRINT '========================================';
PRINT 'IMPORTANT NOTES:';
PRINT '1. Only AstroCity is confirmed safe to delete (if empty)';
PRINT '2. NEVER delete Card or ProfileAttribute* tables - they are disabled features with full implementations';
PRINT '3. Always backup database before any deletion';
PRINT '4. All other tables should be kept unless specifically verified as unused';
PRINT '========================================';

PRINT '';
PRINT '✅ Simple Analysis Complete!';
PRINT 'Look for tables with Recommendation = SAFE_TO_DELETE';
PRINT 'AstroCity should be the only table recommended for deletion.';