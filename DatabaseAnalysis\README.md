# 🗄️ Database Analysis Tools for SubhLaganNew_v1

## 📋 Quick Start

**To identify unused tables in your database:**

1. **Run the analysis:**
   ```powershell
   .\RunDatabaseAnalysis.ps1 -ServerName "Localhost\SQLEXPRESS" -DatabaseName "SubhLaganNew_v1"
   ```

2. **Review the results:**
   - Check `DatabaseAnalysisResults/DatabaseAnalysisReport.txt`
   - Look for tables with **UnusedConfidenceScore >= 85**

3. **Follow the execution guide:**
   - Read `EXECUTION_GUIDE.md` for detailed steps
   - Review `COMPREHENSIVE_ANALYSIS_REPORT.md` for complete findings

## 📁 What's Included

### **Analysis Scripts (Run Automatically)**
- `01_GetAllTables.sql` - Table inventory with sizes
- `02_GetTableRelationships.sql` - Foreign key analysis  
- `03_GetTableUsageStats.sql` - Usage statistics
- `04_GetTableReferences.sql` - Database object references
- `05_DatabaseAnalysisSummary.sql` - Complete analysis with scoring

### **Automation & Safety**
- `RunDatabaseAnalysis.ps1` - **Main automation script**
- `06_CreateBackupScripts.sql` - Database backup procedures
- `UnusedTableCleanupMigration.cs` - Safe cleanup migration

### **Documentation & Guidance**
- `EXECUTION_GUIDE.md` - **Step-by-step implementation guide**
- `COMPREHENSIVE_ANALYSIS_REPORT.md` - **Complete analysis findings**
- `CodebaseEntityMapping.json` - Code-to-database mapping

## 🎯 Key Findings Summary

Based on codebase analysis:

- ✅ **1 table safe to delete**: `AstroCity` (unused, no implementation)
- ⚠️ **6 disabled features**: Don't delete (Card, ProfileAttribute*, etc.)
- 📊 **52+ active tables**: Keep (full implementations)

## ⚠️ Important Safety Notes

1. **ALWAYS backup** before any deletion
2. **Test in development** environment first  
3. **Don't delete tables** with confidence score < 85
4. **Disabled features ≠ unused tables**

## 🚀 Next Steps

1. Run `.\RunDatabaseAnalysis.ps1` to get current database state
2. Follow `EXECUTION_GUIDE.md` for safe implementation
3. Review findings against your business requirements
4. Execute cleanup only after thorough validation

---

**Need Help?** Check the `EXECUTION_GUIDE.md` for detailed instructions and troubleshooting.