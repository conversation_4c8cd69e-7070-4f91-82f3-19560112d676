-- =====================================================================
-- Database Table Usage Statistics Script for SubhLaganNew_v1
-- Purpose: Extract table usage patterns and activity metrics
-- =====================================================================

USE SubhLaganNew_v1;

-- Get table usage statistics from sys.dm_db_index_usage_stats
SELECT 
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    us.user_seeks,
    us.user_scans,
    us.user_lookups,
    us.user_updates,
    us.last_user_seek,
    us.last_user_scan,
    us.last_user_lookup,
    us.last_user_update,
    us.system_seeks,
    us.system_scans,
    us.system_lookups,
    us.system_updates,
    -- Calculate total user activity
    (ISNULL(us.user_seeks, 0) + ISNULL(us.user_scans, 0) + 
     ISNULL(us.user_lookups, 0) + ISNULL(us.user_updates, 0)) AS TotalUserActivity,
    -- Get last activity date
    CASE 
        WHEN us.last_user_seek > us.last_user_scan 
             AND us.last_user_seek > us.last_user_lookup 
             AND us.last_user_seek > us.last_user_update
        THEN us.last_user_seek
        WHEN us.last_user_scan > us.last_user_lookup 
             AND us.last_user_scan > us.last_user_update
        THEN us.last_user_scan
        WHEN us.last_user_lookup > us.last_user_update
        THEN us.last_user_lookup
        ELSE us.last_user_update
    END AS LastUserActivity
FROM 
    sys.tables t
    INNER JOIN sys.indexes i ON t.object_id = i.object_id
    LEFT JOIN sys.dm_db_index_usage_stats us ON i.object_id = us.object_id AND i.index_id = us.index_id
WHERE 
    t.is_ms_shipped = 0
    AND us.database_id = DB_ID() OR us.database_id IS NULL
ORDER BY 
    TotalUserActivity DESC, SchemaName, TableName;

-- Get tables with zero activity (potential candidates for deletion)
SELECT 
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    t.create_date AS CreatedDate,
    t.modify_date AS ModifiedDate,
    ISNULL(p.rows, 0) AS RowCount,
    'No Usage Statistics' AS ActivityStatus
FROM 
    sys.tables t
    LEFT JOIN sys.indexes i ON t.object_id = i.object_id AND i.index_id <= 1
    LEFT JOIN sys.dm_db_index_usage_stats us ON i.object_id = us.object_id AND i.index_id = us.index_id AND us.database_id = DB_ID()
    LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE 
    t.is_ms_shipped = 0
    AND us.object_id IS NULL
ORDER BY 
    RowCount DESC, TableName;

-- Get table column information for analysis
SELECT 
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    c.name AS ColumnName,
    TYPE_NAME(c.user_type_id) AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN pk.column_id IS NOT NULL THEN 'Yes' ELSE 'No' END AS IsPrimaryKey
FROM 
    sys.tables t
    INNER JOIN sys.columns c ON t.object_id = c.object_id
    LEFT JOIN (
        SELECT ic.object_id, ic.column_id
        FROM sys.index_columns ic
        INNER JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id
        WHERE i.is_primary_key = 1
    ) pk ON c.object_id = pk.object_id AND c.column_id = pk.column_id
WHERE 
    t.is_ms_shipped = 0
ORDER BY 
    SchemaName, TableName, c.column_id;