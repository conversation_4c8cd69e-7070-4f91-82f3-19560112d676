﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Subhlagan.Application.crm.Messages
{

    public class SubhEmailTemplatesData
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public bool IsActive { get; set; }
        public string Category { get; set; }
    }
    public class SubhEmailTemplatesMetadata
    {
        public string Subject { get; set; }
        public string Category { get; set; }
    }

    public class SubhEmailTemplates
    {
        private static readonly object _lock = new object();
        private static List<SubhEmailTemplatesData> _subhEmailTempleList;
        private const string TemplatesFolderPath = "App_Data/EmailTemplates";
        private const string MetadataFileName = "EmailTemplateMetadata.json";

        public static List<SubhEmailTemplatesData> SubhEmailTempleList
        {
            get => GetSubhEmailTemplates();
            set => _subhEmailTempleList = value;
        }

        public static List<SubhEmailTemplatesData> GetSubhEmailTemplates()
        {
            if (_subhEmailTempleList == null)
            {
                lock (_lock)
                {
                    if (_subhEmailTempleList == null)
                    {
                        _subhEmailTempleList = LoadTemplatesFromFiles();
                    }
                }
            }
            return _subhEmailTempleList;
        }

        private static List<SubhEmailTemplatesData> LoadTemplatesFromFiles()
        {
            try
            {
                string folderPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, TemplatesFolderPath);

                if (System.IO.Directory.Exists(folderPath))
                {
                    var templates = new List<SubhEmailTemplatesData>();
                    int id = 1;

                    // Load metadata from JSON file
                    var metadata = LoadEmailTemplateMetadata(folderPath);

                    // Get all HTML files in the templates directory
                    var templateFiles = System.IO.Directory.GetFiles(folderPath, "*.html", System.IO.SearchOption.AllDirectories);

                    foreach (var file in templateFiles)
                    {
                        try
                        {
                            // Use filename as fallback name (without extension)
                            string fileName = Path.GetFileNameWithoutExtension(file);

                            // Get relative path for metadata lookup
                            string relativePath = GetRelativePath(folderPath, file);
                            
                            // Read the HTML content
                            string body = File.ReadAllText(file);

                            // Get category from subfolder if exists
                            string category = "Email";
                            string directory = Path.GetDirectoryName(file);
                            if (directory != folderPath)
                            {
                                // Get the subfolder name as category
                                string relativeDir = directory.Substring(folderPath.Length).TrimStart('\\', '/');
                                string[] pathParts = relativeDir.Split('\\', '/');
                                if (pathParts.Length > 0)
                                {
                                    category = pathParts[0];
                                }
                            }

                            // Get subject and category from metadata, fall back to filename
                            string subject = fileName; // Default fallback
                            if (metadata.ContainsKey(relativePath))
                            {
                                subject = metadata[relativePath].Subject;
                                category = metadata[relativePath].Category ?? category;
                            }

                            templates.Add(new SubhEmailTemplatesData
                            {
                                Id = id++,
                                Name = fileName,
                                Subject = subject,
                                Body = body,
                                IsActive = true,
                                Category = category
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error loading template file {file}: {ex.Message}");
                        }
                    }

                    return templates.Count > 0 ? templates : GetDefaultTemplates();
                }

                // Fall back to default templates if directory doesn't exist
                return GetDefaultTemplates();
            }
            catch (Exception ex)
            {
                // Log exception
                Console.WriteLine($"Error loading email templates: {ex.Message}");
                return GetDefaultTemplates();
            }
        }

        private static Dictionary<string, SubhEmailTemplatesMetadata> LoadEmailTemplateMetadata(string folderPath)
        {
            try
            {
                string metadataPath = Path.Combine(folderPath, MetadataFileName);
                if (File.Exists(metadataPath))
                {
                    string jsonContent = File.ReadAllText(metadataPath);
                    return JsonConvert.DeserializeObject<Dictionary<string, SubhEmailTemplatesMetadata>>(jsonContent) 
                           ?? new Dictionary<string, SubhEmailTemplatesMetadata>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading email template metadata: {ex.Message}");
            }
            return new Dictionary<string, SubhEmailTemplatesMetadata>();
        }

        private static string GetRelativePath(string folderPath, string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(folderPath) || string.IsNullOrEmpty(filePath))
                    return Path.GetFileName(filePath) ?? string.Empty;

                if (!filePath.StartsWith(folderPath, StringComparison.OrdinalIgnoreCase))
                    return Path.GetFileName(filePath) ?? string.Empty;

                string relativePath = filePath.Substring(folderPath.Length).TrimStart('\\', '/');
                return relativePath.Replace('\\', '/');
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting relative path for {filePath}: {ex.Message}");
                return Path.GetFileName(filePath) ?? string.Empty;
            }
        }

        private static List<SubhEmailTemplatesData> GetDefaultTemplates()
        {

            return new List<SubhEmailTemplatesData>();
        }

        //private static List<SubhEmailTemplatesData> GetDefaultTemplates()
        //{
        //    Return the existing hardcoded templates as fallback
        //    return new List<SubhEmailTemplatesData>
        //    {
        //        new SubhEmailTemplatesData
        //        {
        //            Id =1,
        //            Name = "MOBILE NO. NOT Reachable",
        //            Subject = "MOBILE NO. NOT Reachable/ OPP SIDE ACCEPTED / MEETING",
        //            Body = @"
        //            <div style='font-family:Helvetica, Arial, sans-serif; color:#000; font-size:14px; line-height:22px; padding:10px 0;'>
        //                <p style='text-align:left;'>Dear Sir,</p>
        //                <p style='text-align:left;'>Greetings of the day!</p>
        //                <p style='text-align:left;'>We have been attempting to reach you but have been unable to connect.</p>
        //                <p style='text-align:left;'>
        //                    We would like to bring to your attention the profile of 
        //                    <strong style='display:inline-block; font-size:16px;'>{XXXX}</strong>, which we had shared with you earlier.
        //                    She has expressed interest in your profile.
        //                </p>
        //                <p style='text-align:left;'>
        //                    Kindly share your feedback regarding the same so that we can proceed with arranging a meeting.
        //                </p>
        //                <p style='text-align:left;'>We look forward to your response.</p>
        //                <p style='text-align:left;'>Best regards,</p>
        //            </div>",
        //            IsActive = true,
        //            Category ="Follow-up email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =2,
        //            Name = "REGULER  MAIL",
        //            Subject = "REGULER  MAIL",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>We have shared the following profile for your consideration.</p>
        //                   <p><h3>{XXXXX}</h3></p>
        //                   <p>Kindly provide your feedback regarding this profile at your earliest convenience, so we may proceed accordingly.</p>
        //                   <p>We look forward to hearing from you.</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Follow-up email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =3,
        //            Name = "Not Answering",
        //            Subject = "Follow-Up on Shared Profile",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>We previously shared the profile of <h3>{XXX}</h3> with you. Despite our efforts to connect, we have been unable to reach you.</p>
        //                   <p>We would greatly appreciate your feedback on this profile</p>
        //                   <p>Looking forward to your response.</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Follow-up email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =4,
        //            Name = "Opp Side Accepted",
        //            Subject = "Follow-Up on Meeting Request",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear <h3>{Recipient's Name}</h3>,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>The family of <h3>{XXX}</h3> is ready to proceed with a meeting. Kindly share your thoughts on this profile so we can arrange the meeting accordingly.</p>
        //                   <p>This matter has been pending for the <h3>{past month}</h3>.Please let us know at your earliest convenience if you are interested.</p>
        //                   <p>We look forward to your response.</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Follow-up email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =5,
        //            Name = "Opp side rejected",
        //            Subject = "Update on Profile Decision",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>We wish to inform you that <h3>{XXX}</h3> has decided not to proceed further due to concerns regarding the location.</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Follow-up email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =6,
        //            Name = "Require photographs",
        //            Subject = "Request for Photographs",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>We kindly request you to share 2-3 photographs of  XXXfor our records and to facilitate further procedures.</p>
        //                   <p>We look forward to your response</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =7,
        //            Name = "Follow-up Regarding",
        //            Subject = "Follow-up Regarding {XXX}",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                    <p>I hope this message finds you well</p>
        //                   <p>Earlier you shared the biodata of  <h3>{XXX}</h3> with us. As discussed with him at the time, you mentioned that you would consult with your family and get back to us, but we have not received a response from your end.</p>
        //                   <p>If you are interested in giving us the opportunity to work ,kindly let us know. Otherwise, we will proceed with removing biodata from our database.</p>
        //                   <p>We look forward to hearing from you.</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =8,
        //            Name = "Request for Profile Updates",
        //            Subject = "Request for Profile Updates",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                    <p>I hope this message finds you well.</p>
        //                   <p>We are currently in the process of updating our profiles. If there are any updates to be made to your profile—such as qualifications, business, profession, or other details—please let us know so we can make the necessary changes as per your requirements.</p>
        //                   <p>To assist you, we have attached our biodata format. Kindly review and confirm if the information we have is accurate or provide us with the updated details. This will help us serve you more efficiently.</p>
        //                   <p>We look forward to your response.</p>
        //                   <p>Best regards,</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Email",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =9,
        //            Name = "Non – Payment of Registration Fee",
        //            Subject = "Profile Removal Notification (While Deleting)",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear <h3>{Recipient's Name}</h3>,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>We wish to inform you that your profile is being removed from our database, as per our corporate policy, due to non-payment of the registration fees.</p>
        //                   <p>Please note that you will remain liable for our fees if a matrimonial alliance is formed through any of the profiles shared with you by Subhlagan.com Pvt. Ltd. Should you choose to finalize the alliance through us, please feel free to contact us directly.</p>
        //                   <p>For any clarifications or assistance, do not hesitate to reach out to your Relationship Manager (RM).</p>
        //                   <p>Thank you for your understanding.</p>
        //                   <p>Best regards,</p>
        //                   <p><h3>{Your Name}</h3></p>
        //                   <p>Subhlagan.com Pvt.Ltd.</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="While Deleting",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =10,
        //            Name = "No Response",
        //            Subject = "Profile Removal Notification (Deleting)",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear <h3>{Recipient's Name}</h3>,</p>
        //                   <p>Greetings of the day!</p>
        //                    <p>As we have not received a response from you, we are proceeding to remove your profile from our database.</p>
        //                   <p>Please note that you will still be liable for our fees if a matrimonial alliance is formed through any of the profiles shared with you by Subhlagan.com Pvt. Ltd. Should you wish to finalize such an alliance through us, feel free to contact us.</p>
        //                   <p>For any clarifications or assistance, please do not hesitate to get in touch.</p>
        //                   <p>Thank you for your understanding, and we extend our best wishes to you</p>
        //                   <p>Best regards,</p>
        //                   <p>Subhlagan.com Pvt.Ltd.</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //             Category ="Deleting",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =11,
        //            Name = "Not interested in Marriage",
        //            Subject = "Profile Removal Notification (Deleting)",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear <h3>{Recipient's Name}</h3>,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>We are removing your profile from our records, as your son is currently not interested in pursuing marriage.</p>
        //                   <p>However, we would like to make it clear that if an alliance is formed in the future through any of the matrimonial profiles shared with you by Subhlagan.com Pvt. Ltd., you will be liable for our fees. Should you wish to finalize such an alliance through us, please feel free to contact us.</p>
        //                   <p>For any clarifications or further assistance, do not hesitate to get in touch.</p>
        //                   <p>Thank you and best regards,</p>
        //                   <p><h3>{Your Name}</h3></p>
        //                   <p>Subhlagan.com Pvt.Ltd.</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Deleting",
        //        },
        //        new SubhEmailTemplatesData
        //        {
        //            Id =12,
        //            Name = "Clarification Regarding Matrimonial Services",
        //            Subject = "Clarification Regarding Matrimonial Services (Deleting)",
        //            Body = @"                        
        //                <div style='text-align:center;font-family:Helvetica,Arial,sans-serif;color:#000;font-size:14px;padding:10px 0;line-height:22px'>
        //                   <p>Dear Sir,</p>
        //                   <p>Greetings of the day!</p>
        //                   <p>As per your telephonic conversation , we have decided to suspend our services for <h3>{XXX}</h3> for one year.</p>
        //                   <p>However, we would like to clearly state that if an alliance is formed through any matrimonial profile shared with you by Subhlagan.com Pvt. Ltd., you will be liable to pay our success fee. Please feel free to contact us if you wish to proceed with formalizing such an alliance through our services.</p>
        //                   <p>Do not hesitate to reach out for any further clarification.</p>
        //                   <p>Best regards,</p>
        //                   <p>Subhlagan</p>
        //                </div>
        //            ",
        //            IsActive = true,
        //            Category ="Deleting",
        //        },
        //    };
        //}

    }
}



