﻿using Subhlagan.Core.Domain;
using Subhlagan.Application.Common.Pdf;
using System.Collections.Generic;

namespace Subhlagan.Application.Pdf
{
    public partial class BioDataSource : DocumentSource
    {
        public BioDataSource()
        {
            FamilyMembers = new Dictionary<string, List<FamilyMemberItem>>();
            FooterTextColumn1 = new List<string>();
            FooterTextColumn2 = new List<string>();
        }

        public string FullName { get; set; }
        public string ProfileId { get; set; }
        public string FormatProfileId { get; set; }
        public string DateOfBirth { get; set; }
        public string TimeOfBirth { get; set; }
        public int Age { get; set; }
        public string BirthPlace { get; set; }
        public string Height { get; set; }
        public string Weight { get; set; }
        public string MaritalStatus { get; set; }
        public string SkinTone { get; set; }
        public string BodyType { get; set; }
        //public string Complexion { get; set; }
        public string ZodiacSign { get; set; }
        public string Community { get; set; }
        public string GotraSakha { get; set; }
        public string FoodHabits { get; set; }
        public string PresentCity { get; set; }
        public string NativePlace { get; set; }
        public string Hobbies { get; set; }
        public string AboutMe { get; set; }
        public List<EducationItem> Educations { get; set; }
        //public List<FamilyMemberItem> FamilyMembers { get; set; }
        public Dictionary<string, List<FamilyMemberItem>> FamilyMembers { get; set; }
        public string AdditionalInformation { get; set; }
        public string FirmOccupation { get; set; }
        public string Residence { get; set; }
        public string AboutFamily { get; set; }
        public string ContactPerson { get; set; }
        public string Mobile { get; set; }
        public string Email { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }

        // Footer
        public List<string> FooterTextColumn1 { get; set; }
        public List<string> FooterTextColumn2 { get; set; }
        public RelationshipManagerUserItem Manager { get; set; }
        public List<string> PicturePaths { get; set; }
        public OccupationAndBusinessItem FamilyOccupationAndBusiness { get; set; }
        public OccupationAndBusinessItem OccupationAndBusiness { get; set; }
        public string EducationAdditionalInfo { get; set; }
        public MedicalHistory MedicalHistory { get; set; }
    }

}
