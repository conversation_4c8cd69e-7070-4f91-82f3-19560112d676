﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;

namespace Subhlagan.Application.OrdersPaymentHistory
{
    public partial class OrderPaymentHistoryService : IOrderPaymentHistoryService
    {
        private readonly EnhancedEntityRepository<OrderPaymentHistory> _orderPaymentHistoryRepository;
        private readonly EnhancedEntityRepository<Profile> _profileRepository;

        public OrderPaymentHistoryService(EnhancedEntityRepository<OrderPaymentHistory> orderPaymentHistoryRepository, EnhancedEntityRepository<Profile> profileRepository)
        {
            _orderPaymentHistoryRepository = orderPaymentHistoryRepository;
            _profileRepository = profileRepository;
        }

        public virtual async Task<OrderPaymentHistory> InsertOrderPaymentHistoryAsync(OrderPaymentHistory orderPaymentHistory)
        {
            if (orderPaymentHistory == null)
                throw new ArgumentNullException(nameof(orderPaymentHistory));

            await _orderPaymentHistoryRepository.InsertAsync(orderPaymentHistory);
            return orderPaymentHistory;
        }

        public virtual async Task UpdateOrderPaymentHistoryAsync(OrderPaymentHistory orderPaymentHistory)
        {
            if (orderPaymentHistory == null)
                throw new ArgumentNullException(nameof(orderPaymentHistory));

            await _orderPaymentHistoryRepository.UpdateAsync(orderPaymentHistory);
        }

        public virtual async Task DeleteOrderPaymentHistoryAsync(OrderPaymentHistory orderPaymentHistory)
        {
            if (orderPaymentHistory == null)
                throw new ArgumentNullException(nameof(orderPaymentHistory));

            await _orderPaymentHistoryRepository.DeleteAsync(orderPaymentHistory);
        }

        public virtual async Task<OrderPaymentHistory> GetOrderPaymentHistoryByIdAsync(int orderPaymentHistoryId)
        {
            return await _orderPaymentHistoryRepository.GetByIdAsync(orderPaymentHistoryId);
        }




        public virtual async Task<IList<OrderPaymentHistory>> GetAllOrderPaymentHistoriesAsync(int profileId = 0)
        {
            return await _orderPaymentHistoryRepository.GetAllAsync(query =>
            {
                if (profileId > 0)
                    query = query.Where(o => o.ProfileId == profileId);

                query = query.OrderBy(o => o.Id);
                return query;
            });
        }

        public virtual async Task<IPagedList<OrderPaymentHistory>> GetAllOrderPaymentHistoriesAsync(IList<int> profileIds = null, DateTime? paymentUpdatedDateFrom = null,
            DateTime? paymentUpdatedDateTo = null, int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            int officeId = 0, IList<int> matchResultIds = null, IList<int> paymentStatusIds = null)
        {
            var query = _orderPaymentHistoryRepository.Table;
            var profileQuery = _profileRepository.Table;

            if (profileIds != null && profileIds.Any())
            {
                profileQuery = profileQuery.Where(x => profileIds.Contains(x.Id));
            }
            if (officeId > 0)
            {
                profileQuery = profileQuery.Where(x => x.OfficeId == officeId);
            }
            if (matchResultIds != null && matchResultIds.Any())
            {
                profileQuery = profileQuery.Where(x => matchResultIds.Contains(x.MatchResultId));
            }
            if (paymentStatusIds != null && paymentStatusIds.Any())
            {
                profileQuery = profileQuery.Where(x => paymentStatusIds.Contains(x.PaymentStatusId));
            }
            var filteredIds = profileQuery.Select(p => p.Id).ToList();

            if (profileQuery.Count() == 0)
            {
                return new PagedList<OrderPaymentHistory>(new List<OrderPaymentHistory>(), 0, 1, 0);
            }
            if (profileQuery.Count() > 0)
            {
                query = query.Where(x => filteredIds.Contains(x.ProfileId));
            }

            if (paymentUpdatedDateFrom.HasValue)
                query = query.Where(o => paymentUpdatedDateFrom.Value <= o.PaidDateUtc);

            if (paymentUpdatedDateTo.HasValue)
                query = query.Where(o => paymentUpdatedDateTo.Value >= o.PaidDateUtc);

            query = query.OrderBy(o => o.Id).OrderByDescending(o => o.PaidDateUtc);
            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);
        }

        public virtual async Task<IList<OrderPaymentHistory>> GetOrderPaymentHistoryByIdsAsync(int[] orderPaymentHistoryIds)
        {
            return await _orderPaymentHistoryRepository.GetByIdsAsync(orderPaymentHistoryIds);
        }
    }
}
