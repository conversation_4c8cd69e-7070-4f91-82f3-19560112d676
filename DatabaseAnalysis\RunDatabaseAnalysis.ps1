# =====================================================================
# PowerShell Script to Run Complete Database Analysis
# Purpose: Execute all SQL scripts and generate comprehensive report
# =====================================================================

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerName = "Localhost\SQLEXPRESS",
    
    [Parameter(Mandatory=$true)]
    [string]$DatabaseName = "SubhLaganNew_v1",
    
    [string]$OutputPath = ".\DatabaseAnalysisResults"
)

# Import SQL Server module
Import-Module SqlServer -ErrorAction SilentlyContinue
if (-not (Get-Module -Name SqlServer)) {
    Write-Error "SQL Server PowerShell module is not installed. Please install it using: Install-Module -Name SqlServer"
    exit 1
}

# Create output directory
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force
}

$ConnectionString = "Data Source=$ServerName;Initial Catalog=$DatabaseName;Integrated Security=True;Persist Security Info=False;Trust Server Certificate=True"

Write-Host "Starting Database Analysis for $DatabaseName..." -ForegroundColor Green
Write-Host "Output will be saved to: $OutputPath" -ForegroundColor Yellow

try {
    # Script 1: Get All Tables
    Write-Host "`n1. Extracting table inventory..." -ForegroundColor Cyan
    $TablesResult = Invoke-Sqlcmd -ConnectionString $ConnectionString -InputFile "01_GetAllTables.sql"
    $TablesResult | Export-Csv -Path "$OutputPath\01_AllTables.csv" -NoTypeInformation
    Write-Host "   Results saved to: 01_AllTables.csv" -ForegroundColor Gray

    # Script 2: Get Table Relationships
    Write-Host "`n2. Analyzing table relationships..." -ForegroundColor Cyan
    $RelationshipsResult = Invoke-Sqlcmd -ConnectionString $ConnectionString -InputFile "02_GetTableRelationships.sql"
    $RelationshipsResult | Export-Csv -Path "$OutputPath\02_TableRelationships.csv" -NoTypeInformation
    Write-Host "   Results saved to: 02_TableRelationships.csv" -ForegroundColor Gray

    # Script 3: Get Table Usage Statistics
    Write-Host "`n3. Gathering usage statistics..." -ForegroundColor Cyan
    $UsageResult = Invoke-Sqlcmd -ConnectionString $ConnectionString -InputFile "03_GetTableUsageStats.sql"
    $UsageResult | Export-Csv -Path "$OutputPath\03_TableUsageStats.csv" -NoTypeInformation
    Write-Host "   Results saved to: 03_TableUsageStats.csv" -ForegroundColor Gray

    # Script 4: Get Table References
    Write-Host "`n4. Finding table references in database objects..." -ForegroundColor Cyan
    $ReferencesResult = Invoke-Sqlcmd -ConnectionString $ConnectionString -InputFile "04_GetTableReferences.sql"
    $ReferencesResult | Export-Csv -Path "$OutputPath\04_TableReferences.csv" -NoTypeInformation
    Write-Host "   Results saved to: 04_TableReferences.csv" -ForegroundColor Gray

    # Script 5: Generate Summary Analysis
    Write-Host "`n5. Generating comprehensive analysis summary..." -ForegroundColor Cyan
    $SummaryResult = Invoke-Sqlcmd -ConnectionString $ConnectionString -InputFile "05_DatabaseAnalysisSummary.sql"
    $SummaryResult | Export-Csv -Path "$OutputPath\05_AnalysisSummary.csv" -NoTypeInformation
    Write-Host "   Results saved to: 05_AnalysisSummary.csv" -ForegroundColor Gray

    # Create a consolidated report
    Write-Host "`n6. Creating consolidated report..." -ForegroundColor Cyan
    
    # Filter high-confidence unused tables
    $UnusedTables = $SummaryResult | Where-Object { $_.UnusedConfidenceScore -ge 85 }
    $ReviewTables = $SummaryResult | Where-Object { $_.UnusedConfidenceScore -ge 60 -and $_.UnusedConfidenceScore -lt 85 }
    
    $ReportContent = @"
DATABASE ANALYSIS REPORT
========================
Database: $DatabaseName
Analysis Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Total Tables Analyzed: $($SummaryResult.Count)

HIGH CONFIDENCE UNUSED TABLES (Safe to Delete):
$(if ($UnusedTables.Count -gt 0) { $UnusedTables | Format-Table -Property TableName, RowCount, UnusedConfidenceScore, Recommendation -AutoSize | Out-String } else { "None found" })

TABLES REQUIRING REVIEW:
$(if ($ReviewTables.Count -gt 0) { $ReviewTables | Format-Table -Property TableName, RowCount, UnusedConfidenceScore, Recommendation -AutoSize | Out-String } else { "None found" })

SUMMARY:
- Tables safe to delete: $($UnusedTables.Count)
- Tables requiring review: $($ReviewTables.Count)
- Active tables: $(($SummaryResult | Where-Object { $_.UnusedConfidenceScore -lt 60 }).Count)

For detailed analysis, review the individual CSV files in the output directory.
"@

    $ReportContent | Out-File -FilePath "$OutputPath\DatabaseAnalysisReport.txt" -Encoding UTF8
    Write-Host "   Consolidated report saved to: DatabaseAnalysisReport.txt" -ForegroundColor Gray

    Write-Host "`n✅ Database Analysis Complete!" -ForegroundColor Green
    Write-Host "Check the output directory for detailed results: $OutputPath" -ForegroundColor Yellow
    
    if ($UnusedTables.Count -gt 0) {
        Write-Host "`n⚠️  Found $($UnusedTables.Count) tables that appear safe to delete!" -ForegroundColor Red
        Write-Host "Review the consolidated report before proceeding with any deletions." -ForegroundColor Yellow
    }

} catch {
    Write-Error "An error occurred during analysis: $($_.Exception.Message)"
    Write-Host "Please check your connection string and SQL Server permissions." -ForegroundColor Red
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Review the DatabaseAnalysisReport.txt file" -ForegroundColor White
Write-Host "2. Cross-reference findings with your application code" -ForegroundColor White  
Write-Host "3. Create database backup before making any changes" -ForegroundColor White
Write-Host "4. Use the generated migration scripts for safe deletion" -ForegroundColor White