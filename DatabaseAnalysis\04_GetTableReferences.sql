-- =====================================================================
-- Database Table References Script for SubhLaganNew_v1
-- Purpose: Find tables referenced in stored procedures, views, functions, and triggers
-- =====================================================================

USE SubhLaganNew_v1;

-- Get all stored procedures and their table references
SELECT 
    'Stored Procedure' AS ObjectType,
    SCHEMA_NAME(p.schema_id) AS SchemaName,
    p.name AS ObjectName,
    p.create_date AS CreatedDate,
    p.modify_date AS ModifiedDate,
    d.referenced_entity_name AS ReferencedTable,
    d.referenced_schema_name AS ReferencedSchema
FROM 
    sys.procedures p
    INNER JOIN sys.sql_expression_dependencies d ON p.object_id = d.referencing_id
WHERE 
    d.referenced_entity_name IS NOT NULL
    AND d.referenced_entity_name NOT LIKE 'sys%'
UNION ALL

-- Get all views and their table references
SELECT 
    'View' AS ObjectType,
    SCHEMA_NAME(v.schema_id) AS SchemaName,
    v.name AS ObjectName,
    v.create_date AS CreatedDate,
    v.modify_date AS ModifiedDate,
    d.referenced_entity_name AS ReferencedTable,
    d.referenced_schema_name AS ReferencedSchema
FROM 
    sys.views v
    INNER JOIN sys.sql_expression_dependencies d ON v.object_id = d.referencing_id
WHERE 
    d.referenced_entity_name IS NOT NULL
    AND d.referenced_entity_name NOT LIKE 'sys%'
UNION ALL

-- Get all functions and their table references
SELECT 
    'Function' AS ObjectType,
    SCHEMA_NAME(f.schema_id) AS SchemaName,
    f.name AS ObjectName,
    f.create_date AS CreatedDate,
    f.modify_date AS ModifiedDate,
    d.referenced_entity_name AS ReferencedTable,
    d.referenced_schema_name AS ReferencedSchema
FROM 
    sys.objects f
    INNER JOIN sys.sql_expression_dependencies d ON f.object_id = d.referencing_id
WHERE 
    f.type IN ('FN', 'IF', 'TF')  -- Scalar, Inline Table-Valued, Table-Valued Functions
    AND d.referenced_entity_name IS NOT NULL
    AND d.referenced_entity_name NOT LIKE 'sys%'
UNION ALL

-- Get all triggers and their table references
SELECT 
    'Trigger' AS ObjectType,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS ObjectName,
    t.create_date AS CreatedDate,
    t.modify_date AS ModifiedDate,
    d.referenced_entity_name AS ReferencedTable,
    d.referenced_schema_name AS ReferencedSchema
FROM 
    sys.triggers t
    INNER JOIN sys.sql_expression_dependencies d ON t.object_id = d.referencing_id
WHERE 
    d.referenced_entity_name IS NOT NULL
    AND d.referenced_entity_name NOT LIKE 'sys%'
ORDER BY 
    ReferencedTable, ObjectType, SchemaName, ObjectName;

-- Get tables that are NOT referenced by any database objects
WITH ReferencedTables AS (
    SELECT DISTINCT d.referenced_entity_name AS TableName
    FROM sys.sql_expression_dependencies d
    WHERE d.referenced_entity_name IS NOT NULL
      AND d.referenced_entity_name NOT LIKE 'sys%'
)
SELECT 
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    t.create_date AS CreatedDate,
    ISNULL(p.rows, 0) AS RowCount,
    'Not Referenced by DB Objects' AS Status
FROM 
    sys.tables t
    LEFT JOIN ReferencedTables rt ON t.name = rt.TableName
    LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE 
    t.is_ms_shipped = 0
    AND rt.TableName IS NULL
ORDER BY 
    RowCount DESC, TableName;

-- Get comprehensive object dependency summary
SELECT 
    d.referenced_entity_name AS TableName,
    COUNT(DISTINCT d.referencing_id) AS ReferenceCount,
    STRING_AGG(
        CONCAT(
            CASE o.type
                WHEN 'P' THEN 'SP'
                WHEN 'V' THEN 'View'
                WHEN 'FN' THEN 'Func'
                WHEN 'IF' THEN 'Func'
                WHEN 'TF' THEN 'Func'
                WHEN 'TR' THEN 'Trigger'
                ELSE o.type
            END,
            ':',
            SCHEMA_NAME(o.schema_id),
            '.',
            o.name
        ), 
        '; '
    ) AS ReferencingObjects
FROM 
    sys.sql_expression_dependencies d
    INNER JOIN sys.objects o ON d.referencing_id = o.object_id
WHERE 
    d.referenced_entity_name IS NOT NULL
    AND d.referenced_entity_name NOT LIKE 'sys%'
GROUP BY 
    d.referenced_entity_name
ORDER BY 
    ReferenceCount DESC, TableName;