﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.PartnerPreferences
{
    public partial interface IPartnerPreferenceService
    {
        Task InsertPartnerPreferenceAsync(PartnerPreference preference);
        Task UpdatePartnerPreferenceAsync(PartnerPreference preference);
        Task DeletePartnerPreferenceAsync(PartnerPreference preference);
        Task<PartnerPreference> GetPartnerPreferenceByIdAsync(int preferenceId);
        Task<IPagedList<PartnerPreference>> GetAllPartnerPreferencesAsync(
            int? fromAge = null, int? toAge = null, bool? published = null,
            int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false);
        Task<PartnerPreference> GetPartnerPreferenceByProfileIdAsync(int profileId, bool getFromDatabase = false);
    }

}
