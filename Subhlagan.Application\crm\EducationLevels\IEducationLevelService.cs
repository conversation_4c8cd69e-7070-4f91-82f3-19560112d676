﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.EducationLevels
{
    public partial interface IEducationLevelService
    {
        Task InsertEducationLevelAsync(EducationLevel educationLevel);
        Task UpdateEducationLevelAsync(EducationLevel educationLevel);
        Task DeleteEducationLevelAsync(EducationLevel educationLevel);
        Task<EducationLevel> GetEducationLevelByIdAsync(int educationLevelId);
        Task<IPagedList<EducationLevel>> GetAllEducationLevelsAsync(
            string name = null,
            bool? published = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false);
    }
}
