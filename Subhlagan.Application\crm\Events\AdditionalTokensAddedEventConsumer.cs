﻿using Subhlagan.Core.Domain.Messages;
using Subhlagan.Application.Messages;
using Subhlagan.Application.Events;
using Subhlagan.Application.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Subhlagan.Application.Events
{
    public class AdditionalTokensAddedEventConsumer : IConsumer<AdditionalTokensAddedEvent>
    {

        public Task HandleEventAsync(AdditionalTokensAddedEvent eventMessage)
        {
            var allowedTokens = new Dictionary<string, IEnumerable<string>>();
            allowedTokens.Add(Messages.TokenGroupNames.RegistrationFormTokens, new[]
            {
                "%RegistrationForm.URL%",
            });

            allowedTokens.Add(Messages.TokenGroupNames.MessageTokens, new[]
           {
                "%Message.Body%",
            });

            allowedTokens.Add(Messages.TokenGroupNames.WelcomeTokens, new[]
            {
                "%Customer.FullName%",
                "%Customer.Email%",
                "%Customer.Username%",
                "%Customer.Password%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.MeetingScheduleTokens, new[]
            {
                "%Meeting.InitiatorProfileFullName%",
                "%Meeting.Venue%",
                "%Meeting.Venue%",
                "%Meeting.ScheduledDateTime%",
                "%Meeting.Description%",
                "%Meeting.GroomSideRepresentative%",
                "%Meeting.BrideSideRepresentative%",
                "%Meeting.Notes%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.RelationshipManagerTokens, new[]
            {
                "%RelationshipManager.Name%",
                "%RelationshipManager.Number%",
                "%AssistantRelationshipManager.Name%",
                "%AssistantRelationshipManager.Number%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.MatchKundaliTokens, new[]
            {
                "%MatchKundali.MaleProfileFullName%",
                "%MatchKundali.FemaleProfileFullName%",
                "%MatchKundali.TotalGun%",
                "%MatchKundali.PdfUrl%",
                "%MatchKundali.MaleManglikStatus%",
                "%MatchKundali.FemaleManglikStatus%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.ProfileActivationTokens, new[]
            {
                "%Profile.ActivationDate%",
                //"%RelationshipManager.Name%",
            });

            allowedTokens.Add(Messages.TokenGroupNames.ContactExchangeTokens, new[]
            {
                "%ContactExchange.InitiatorName%",
                "%ContactExchange.InitiatorRelation%",
                "%ContactExchange.InitiatorPhone%",
                "%ContactExchange.MatchedName%",
                "%ContactExchange.MatchedRelation%",
                "%ContactExchange.MatchedPhone%",
                "%ContactExchange.Notes%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.MatchProfileTokens, new[]
            {
                "%InitiatorProfile.FullName%",
                "%Match.Profile(s)%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.MatchAcceptedTokens, new[]
            {
                "%MatchAccepter.FullName%",
                "%MatchAccepter.ProfileId%",
                "%ProposalReceiver.FullName%",
                "%ProposalReceiver.ProfileId%",
                //"%RelationshipManager.Name%",
                "%Match.Profile(s)%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.IntimationOfRegistrationTokens, new[]
            {
                "%Customer.FullName%",
                "%Customer.Email%",
                "%Customer.Username%",
                "%RegistrationFee%",
                "%GSTAmount%",
                "%TotalAmount%",
                "%BankAccountName%",
                "%BankAccountNumber%",
                "%BankName%",
                "%BankIFSCCode%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.RegistrationReceiptTokens, new[]
            {
                "%Customer.FullName%",
                "%PaidAmount%",
                "%GSTAmount%",
                "%TotalAmount%",
                "%AmountInWords%",
                "%ServiceTaxCode%",
                "%PanCardNumber%"
            });

            allowedTokens.Add(Messages.TokenGroupNames.BankAccountTokens, new[]
           {
                "%SubhLagan.BankAccountName%",
                "%SubhLagan.BankAccountNumber%",
                "%SubhLagan.BankName%",
                "%SubhLagan.BankIFSCCode%",
                "%SubhLagan.PANCardNumber%",
            });

            allowedTokens.Add(Messages.TokenGroupNames.CongratulationsTokens, new[]
            {
                "%Customer.FullName%",
                "%MatchedProfile.FullName%",
            });

            allowedTokens.Add(Messages.TokenGroupNames.LoginDetailsTokens, new[]
            {
                "%Customer.FullName%",
                "%Customer.Email%",
                "%Customer.Password%"
            });

            foreach (var tokenGroup in allowedTokens.Values)
            {
                eventMessage.AddTokens(tokenGroup.ToArray());
            }

            var tokenGroups = eventMessage.TokenGroups?.ToList() ?? new List<string>();
            tokenGroups.AddRange(new[]
            {
                TokenGroupNames.StoreTokens,
                TokenGroupNames.CustomerTokens,
                TokenGroupNames.MessageTokens,
                TokenGroupNames.WelcomeTokens,
                TokenGroupNames.MeetingScheduleTokens,
                TokenGroupNames.RelationshipManagerTokens,
                TokenGroupNames.MatchKundaliTokens,
                TokenGroupNames.ProfileActivationTokens,
                TokenGroupNames.ContactExchangeTokens,
                TokenGroupNames.MatchProfileTokens,
                TokenGroupNames.MatchAcceptedTokens,
                TokenGroupNames.IntimationOfRegistrationTokens,
                TokenGroupNames.RegistrationReceiptTokens,
                TokenGroupNames.BankAccountTokens,
                TokenGroupNames.CongratulationsTokens,
                TokenGroupNames.LoginDetailsTokens
            });

            eventMessage.TokenGroups = tokenGroups;
            return Task.CompletedTask;
        }
    }
}
