﻿using Subhlagan.Core;
using Subhlagan.Core.Domain.Common;
using Subhlagan.Core.Domain.Customers;
using Subhlagan.Core.Domain.Localization;
using Subhlagan.Core.Domain;
using Subhlagan.Application.ActionStatuses;
using Subhlagan.Application.Cities;
using Subhlagan.Application.Communities;
using Subhlagan.Application.EducationAreas;
using Subhlagan.Application.Gotras;
using Subhlagan.Application.Offices;
using Subhlagan.Application.Packages;
using Subhlagan.Application.ProfileMatchInteractions;
using Subhlagan.Application.Profiles;
using Subhlagan.Application.Reasons;
using Subhlagan.Application.Users;
using Subhlagan.Application;
using Subhlagan.Application.Common;
using Subhlagan.Application.Customers;
using Subhlagan.Application.Directory;
using Subhlagan.Application.ExportImport.Help;
using Subhlagan.Application.Helpers;
using Subhlagan.Application.Localization;
using Subhlagan.Application.Logging;
using Subhlagan.Application.Media;
using Subhlagan.Core.Domain.Messages;
using System.Data;
using Subhlagan.Application.crm.Reports;
using Subhlagan.Core.Infrastructure;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.ExtendedProperties;


namespace Subhlagan.Application.ExportImport
{
    public class SubhExportManager : ExportManager
    {
        #region Fields

        private readonly AddressSettings _addressSettings;
        private readonly ICustomerActivityService _customerActivityService;
        private readonly CustomerSettings _customerSettings;
        private readonly DateTimeSettings _dateTimeSettings;
        private readonly IAddressService _addressService;
        private readonly ICountryService _countryService;
        private readonly ICustomerService _customerService;
        private readonly IDateTimeHelper _dateTimeHelper;
        private readonly IGenericAttributeService _genericAttributeService;
        private readonly ILanguageService _languageService;
        private readonly ILocalizationService _localizationService;
        private readonly ILocalizedEntityService _localizedEntityService;
        private readonly IPictureService _pictureService;
        private readonly IStateProvinceService _stateProvinceService;


        private readonly IWorkContext _workContext;
        private readonly IUserService _userService;
        private readonly IProfileService _profileService;
        private readonly ICityService _cityService;
        private readonly IPackageService _packageService;
        private readonly IOfficeService _officeService;
        private readonly IProfileMatchInteractionService _profileMatchInteractionService;
        private readonly IEducationAreaService _educationAreaService;
        private readonly IActionStatusService _actionStatusService;
        private readonly IReasonService _reasonService;
        private readonly ICommunityService _communityService;
        private readonly IGotraService _gotraService;
        private readonly IKsFileProvider _fileProvider;

        #endregion
        public SubhExportManager(AddressSettings addressSettings, ICustomerActivityService customerActivityService, CustomerSettings customerSettings, DateTimeSettings dateTimeSettings, IAddressService addressService, ICountryService countryService, ICustomerService customerService, IDateTimeHelper dateTimeHelper, IGenericAttributeService genericAttributeService, ILanguageService languageService, ILocalizationService localizationService, ILocalizedEntityService localizedEntityService, IPictureService pictureService, IStateProvinceService stateProvinceService, IWorkContext workContext, IUserService userService, IProfileService profileService, ICityService cityService, IPackageService packageService, IOfficeService officeService, IProfileMatchInteractionService profileMatchInteractionService, IEducationAreaService educationAreaService, IActionStatusService actionStatusService, IReasonService reasonService, ICommunityService communityService, IGotraService gotraService, IKsFileProvider fileProvider)
        : base(addressSettings, customerActivityService, customerSettings, dateTimeSettings, addressService, countryService, customerService, dateTimeHelper, genericAttributeService, languageService, localizationService, localizedEntityService, pictureService, stateProvinceService, workContext)
        {
            _addressSettings = addressSettings;
            _customerActivityService = customerActivityService;
            _customerSettings = customerSettings;
            _dateTimeSettings = dateTimeSettings;
            _addressService = addressService;
            _countryService = countryService;
            _customerService = customerService;
            _dateTimeHelper = dateTimeHelper;
            _genericAttributeService = genericAttributeService;
            _languageService = languageService;
            _localizationService = localizationService;
            _localizedEntityService = localizedEntityService;
            _pictureService = pictureService;
            _stateProvinceService = stateProvinceService;


            _workContext = workContext;
            _userService = userService;
            _profileService = profileService;
            _cityService = cityService;
            _packageService = packageService;
            _officeService = officeService;
            _profileMatchInteractionService = profileMatchInteractionService;
            _educationAreaService = educationAreaService;
            _actionStatusService = actionStatusService;
            _reasonService = reasonService;
            _communityService = communityService;
            _gotraService = gotraService;
            _fileProvider = fileProvider;
        }

        public virtual async Task<byte[]> ExportMeetingsToXlsxAsync(IList<Meeting> meetings)
        {
            //property manager 
            var manager = new PropertyManager<Meeting, Language>(new[]
            {
                new PropertyByName<Meeting, Language>("MeetingId", (m, l) => m.Id),
                new PropertyByName<Meeting, Language>("UserName", async (m, l) => (await _userService.GetUserFullNameAsync(m.UserId))),
                new PropertyByName<Meeting, Language>("UserShortName", async (m, l) => (await _userService.GetUserShortNameByUserIdAsync(m.UserId))),
                new PropertyByName<Meeting, Language>("MaleName", async (m,l)=> (await _profileService.FormatProfileDetailsAsync(m.MaleProfileId))),
                new PropertyByName<Meeting, Language>("FemaleName", async (m,l)=> (await _profileService.FormatProfileDetailsAsync(m.FemaleProfileId))),
                new PropertyByName<Meeting, Language>("Venue",  (m, l) => m.Venue),
                new PropertyByName<Meeting, Language>("ScheduledDateTime", async(m, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(m.ScheduledDateTime.Value, DateTimeKind.Utc))),
                new PropertyByName<Meeting, Language>("Description", (m, l) => m.Description ),
                new PropertyByName<Meeting, Language>("GroomSideRepresentative", (m, l) => m.GroomSideRepresentative),
                new PropertyByName<Meeting, Language>("BrideSideRepresentative",(m,i) => m.BrideSideRepresentative),
                new PropertyByName<Meeting, Language>("IsMailSent",  (m, l) => m.IsMailSent),
                new PropertyByName<Meeting, Language>("Notes", (m, l) => m.Notes),
                new PropertyByName<Meeting, Language>("CreatedOn", async(m, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(m.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<Meeting, Language>("UpdatedOn", async(m, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(m.UpdatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportMeetings",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportMeetings"), meetings.Count));

            return await manager.ExportToXlsxAsync(meetings);
        }

        public virtual async Task<byte[]> ExportShortListsToXlsxAsync(IList<ShortList> shortlists)
        {
            //property manager 
            var manager = new PropertyManager<ShortList, Language>(new[]
            {
                new PropertyByName<ShortList, Language>("ShortListId", (s, l) => s.Id),
                new PropertyByName<ShortList, Language>("UserName", async (s, l) => (await _userService.GetUserFullNameAsync(s.UserId))),
                new PropertyByName<ShortList, Language>("UserShortName", async (s, l) => (await _userService.GetUserShortNameByUserIdAsync(s.UserId))),
                new PropertyByName<ShortList, Language>("InitiatorProfileName", async (s,l)=> (await _profileService.FormatProfileDetailsAsync(s.InitiatorProfileId))),
                new PropertyByName<ShortList, Language>("MatchedProfileName", async (s,l)=> (await _profileService.FormatProfileDetailsAsync(s.MatchedProfileId))),
                new PropertyByName<ShortList, Language>("CreatedOn", async(s, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(s.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<ShortList, Language>("UpdatedOn", async(s, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(s.UpdatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportShortLists",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), shortlists.Count));
            return await manager.ExportToXlsxAsync(shortlists);
        }

        public virtual async Task<byte[]> ExportMatchKundalisToXlsxAsync(IList<MatchKundali> matchkundalis)
        {
            //property manager 
            var manager = new PropertyManager<MatchKundali, Language>(new[]
            {
                new PropertyByName<MatchKundali, Language>("MatchKundaliId", (mk, l) => mk.Id),
                new PropertyByName<MatchKundali, Language>("UserName", async (mk, l) => (await _userService.GetUserFullNameAsync(mk.UserId))),
                new PropertyByName<MatchKundali, Language>("UserShortName", async (mk, l) => (await _userService.GetUserShortNameByUserIdAsync(mk.UserId))),
                new PropertyByName<MatchKundali, Language>("MaleName", async (mk,l)=> (await _profileService.FormatProfileDetailsAsync(mk.MaleProfileId))),
                new PropertyByName<MatchKundali, Language>("FemaleName", async (mk,l)=> (await _profileService.FormatProfileDetailsAsync(mk.FemaleProfileId))),
                new PropertyByName<MatchKundali, Language>("TotalGun", (mk, l) => mk.TotalGun),
                new PropertyByName<MatchKundali, Language>("PdfFileName", (mk,i)=>mk.PdfFileName),
                new PropertyByName<MatchKundali, Language>("MaleManglikStatus", (mk,i)=>mk.MaleManglikStatus.GetDisplayName()),
                new PropertyByName<MatchKundali, Language>("FemaleManglikStatus", (mk,i)=>mk.FemaleManglikStatus.GetDisplayName()),
                new PropertyByName<MatchKundali, Language>("FetchCount", (mk,i)=>mk.FetchCount),
                new PropertyByName<MatchKundali, Language>("CreatedOn", async(mk, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(mk.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<MatchKundali, Language>("UpdatedOn", async(mk, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(mk.UpdatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportMatchKundalis",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportMatchKundalis"), matchkundalis.Count));
            return await manager.ExportToXlsxAsync(matchkundalis);
        }

        public virtual async Task<byte[]> ExportContactExchangesToXlsxAsync(IList<ContactExchange> contactExchanges)
        {
            //property manager 
            var manager = new PropertyManager<ContactExchange, Language>(new[]
            {
                new PropertyByName<ContactExchange, Language>("ContactExchangeId", (ce, l) => ce.Id),
                new PropertyByName<ContactExchange, Language>("UserName", async (ce, l) => (await _userService.GetUserFullNameAsync(ce.UserId))),
                new PropertyByName<ContactExchange, Language>("UserShortName", async (ce, l) => (await _userService.GetUserShortNameByUserIdAsync(ce.UserId))),
                new PropertyByName<ContactExchange, Language>("InitiatorProfileName", async (ce,l)=> (await _profileService.FormatProfileDetailsAsync(ce.InitiatorProfileId))),
                new PropertyByName<ContactExchange, Language>("InitiatorProfileRelation", (ce,l)=> ce.InitiatorRelation),
                new PropertyByName<ContactExchange, Language>("InitiatorProfilePhoneNo", (ce,l)=> _profileService.GetMaskedMobileNumber(ce.InitiatorPhone)),
                new PropertyByName<ContactExchange, Language>("MatchedProfileName", async (ce,l)=> (await _profileService.FormatProfileDetailsAsync(ce.MatchedProfileId))),
                new PropertyByName<ContactExchange, Language>("MatchedProfileRelation", (ce,l)=> (ce.MatchedRelation)),
                new PropertyByName<ContactExchange, Language>("MatchedProfilePhoneNo", (ce,l)=> _profileService.GetMaskedMobileNumber(ce.MatchedPhone)),
                new PropertyByName<ContactExchange, Language>("Notes",  (ce,l)=> ce.Notes),
                new PropertyByName<ContactExchange, Language>("CreatedOn", async(ce, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(ce.CreatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportContactExchanges",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportContactExchanges"), contactExchanges.Count));
            return await manager.ExportToXlsxAsync(contactExchanges);
        }

        public virtual async Task<byte[]> ExportEmailListsToXlsxAsync(IList<QueuedEmail> queuedEmails)
        {
            //property manager 
            var manager = new PropertyManager<QueuedEmail, Language>(new[]
            {
                new PropertyByName<QueuedEmail, Language>("EmailId", (e, l) => e.Id),
                new PropertyByName<QueuedEmail, Language>("UserName", async (e, l) => (await _userService.GetUserFullNameAsync(e.UserId))),
                new PropertyByName<QueuedEmail, Language>("UserShortName", async (e, l) => (await _userService.GetUserShortNameByUserIdAsync(e.UserId))),
                new PropertyByName<QueuedEmail, Language>("ProfileName", async (e, l) => (await _profileService.FormatProfileDetailsAsync(e.ProfileId))),
                new PropertyByName<QueuedEmail, Language>("From", (e,l)=>e.From),
                new PropertyByName<QueuedEmail, Language>("FromName", (e,l)=> e.FromName),
                new PropertyByName<QueuedEmail, Language>("To", (e,l)=> e.To),
                new PropertyByName<QueuedEmail, Language>("ToName",  (e,l)=> e.ToName),
                new PropertyByName<QueuedEmail, Language>("ReplyTo", (e,l)=> e.ReplyTo),
                new PropertyByName<QueuedEmail, Language>("ReplyToName", (e,l)=> e.ReplyToName),
                new PropertyByName<QueuedEmail, Language>("CC",  (e,l)=> e.CC),
                new PropertyByName<QueuedEmail, Language>("Bcc",  (e,l)=> e.Bcc),
                new PropertyByName<QueuedEmail, Language>("Subject",  (e,l)=> e.Subject),
                //new PropertyByName<QueuedEmail, Language>("Body",  (e,l)=> e.Body),
                new PropertyByName<QueuedEmail, Language>("AttachmentFilePath",  (e,l)=> e.AttachmentFilePath),
                new PropertyByName<QueuedEmail, Language>("AttachmentFileName",  (e,l)=> e.AttachmentFileName),
                new PropertyByName<QueuedEmail, Language>("CreatedOn", async(e, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(e.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<QueuedEmail, Language>("SendOn",  async(e, l) => (e.SentOnUtc.HasValue ? await _dateTimeHelper.ConvertToUserTimeAsync(e.SentOnUtc.Value, DateTimeKind.Utc):null)),
                new PropertyByName<QueuedEmail, Language>("SendStatus",  (e, l) => (e.SentOnUtc.HasValue ? "Sent":"Pending")),
                new PropertyByName<QueuedEmail, Language>("DontSendBeforeDate",  async(e, l) => (e.DontSendBeforeDateUtc.HasValue ?await _dateTimeHelper.ConvertToUserTimeAsync(e.DontSendBeforeDateUtc.Value, DateTimeKind.Utc):null)),
                new PropertyByName<QueuedEmail, Language>("SentTries",  (e,l)=> e.SentTries),
            });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportQueuedEmails",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportQueuedEmails"), queuedEmails.Count));
            return await manager.ExportToXlsxAsync(queuedEmails);
        }

        public virtual async Task<byte[]> ExportPaymentListsToXlsxAsync_old(IList<Profile> profiles)
        {
            //var s = (await _packageService.GetPackageByIdAsync(0))?.Name ?? "";
            //property manager 

            var manager = new PropertyManager<Profile, Language>(new[]
            {
                new PropertyByName<Profile, Language>("ProfileId", (p, l) => p.Id),
                new PropertyByName<Profile, Language>("UserName", async (p, l) => (await _userService.GetUserFullNameAsync(p.UserId))),
                new PropertyByName<Profile, Language>("UserShortName", async (p, l) => (await _userService.GetUserShortNameByUserIdAsync(p.UserId))),
                new PropertyByName<Profile, Language>("ProfileName", async (p, l) => (await _profileService.FormatProfileDetailsAsync(p.Id))),
                new PropertyByName<Profile, Language>("Package",  async (p,l)=> (await _packageService.GetPackageByIdAsync(p.PackageId))?.Name ?? ""),
                new PropertyByName<Profile, Language>("Office", async(p,l)=> (await _officeService.GetOfficeByIdAsync(p.OfficeId))?.Name ?? ""),
                new PropertyByName<Profile, Language>("RegistrationCharges", (p,l)=> p.RegistrationCharges),
                new PropertyByName<Profile, Language>("TaxApplied", (p,l)=>p.TaxApplied),
                new PropertyByName<Profile, Language>("TotalAmount", (p,l)=>p.TotalAmount),
                new PropertyByName<Profile, Language>("AmountPaid", (p,l)=>p.AmountPaid),
                new PropertyByName<Profile, Language>("PaymentStatus", (p,l) => (p.PaymentStatus.GetDisplayName())),
                 //new PropertyByName<Profile, Language>("MobileNumber", async (p,l)=> (_profileService.GetMaskedMobileNumber(p.MobileNumber))),
                new PropertyByName<Profile, Language>("RelationshipManager", async (p,l)=> (p.RelationshipManagerUserId>0 ? await _userService.GetUserFullNameAsync(p.RelationshipManagerUserId):"")),
                new PropertyByName<Profile, Language>("AssistantRelationshipManager", async (p,l)=> (p.AssistantRelationshipManagerUserId>0 ? await _userService.GetUserFullNameAsync(p.AssistantRelationshipManagerUserId):"")),
                new PropertyByName<Profile, Language>("ProfileStatus", (p,l)=> p.ProfileStatus.GetDisplayName()),
                new PropertyByName<Profile, Language>("BirthPlace", async (p,l)=> (await _cityService.GetCityByIdAsync(p.BirthPlaceId))?.Name ?? ""),
                new PropertyByName<Profile, Language>("PresentCity", (p,l)=> p.Customer.City !=null ? p.Customer.City:""),
                new PropertyByName<Profile, Language>("TimeofBirth", (p,l)=> p.Customer.DateOfBirth.Value.ToString("yyyy-MM-dd")),
                new PropertyByName<Profile, Language>("Community", async (p,l)=> p.CommunityId > 0 ? (await _communityService.GetCommunityByIdAsync(p.CommunityId)).Name : ""),
                new PropertyByName<Profile, Language>("Gotra", async (p,l)=> p.GotraId > 0 ? (await _gotraService.GetGotraByIdAsync(p.GotraId)).Name : ""),
                new PropertyByName<Profile, Language>("Height", (p,l)=> p.Height.GetDisplayName()),
                new PropertyByName<Profile, Language>("Weight", (p,l)=> p.Weight),
                new PropertyByName<Profile, Language>("BodyType", (p,l)=> p.BodyType.GetDisplayName()),
                new PropertyByName<Profile, Language>("SkinTones", (p,l)=> p.SkinTone.GetDisplayName()),
               // new PropertyByName<Profile, Language>("AbountMe", (p,l)=> p.AboutMe!=null? Regex.Replace(p.AboutMe, "<.*?>", String.Empty) : ""),
                new PropertyByName<Profile, Language>("CreatedOn", async (p,l)=> (await _dateTimeHelper.ConvertToUserTimeAsync(p.Customer.CreatedOnUtc, DateTimeKind.Utc)))
            });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportPaymentList",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportPaymentLists"), profiles.Count));
            return await manager.ExportToXlsxAsync(profiles);
        }

        public virtual async Task<byte[]> ExportStatusListsToXlsxAsync(IList<ProfileMatchInteraction> profileMatchInteractions)
        {
            //var s = (await _packageService.GetPackageByIdAsync(0))?.Name ?? "";
            //property manager 

            var initiatorIds = profileMatchInteractions.Select(i => i.InitiatorProfileId);
            var matchedIds = profileMatchInteractions.Select(i => i.MatchedProfileId);
            var profileIds = new HashSet<int>(initiatorIds.Concat(matchedIds).Distinct());

            var bothProfileMatchInteractions = await _profileMatchInteractionService.GetAllProfileMatchInteractionsAsync(profileIds: profileIds);


            async Task<string> getMatchedProfileMatchStatus(ProfileMatchInteraction profileMatchInteraction)
            {

                var matchInteraction = bothProfileMatchInteractions.FirstOrDefault(p => p.MatchedProfileId == profileMatchInteraction.InitiatorProfileId && p.InitiatorProfileId == profileMatchInteraction.MatchedProfileId);
                if (matchInteraction == null)
                {
                    matchInteraction = new ProfileMatchInteraction();//TODO remove this line on production
                }
                return await Task.FromResult(matchInteraction.MatchStatus.GetDisplayName());
            }

            async Task<object> getMatchedProfileMeetingStatus(ProfileMatchInteraction profileMatchInteraction)
            {

                var matchInteraction = bothProfileMatchInteractions.FirstOrDefault(p => p.MatchedProfileId == profileMatchInteraction.InitiatorProfileId && p.InitiatorProfileId == profileMatchInteraction.MatchedProfileId);
                if (matchInteraction == null)
                {
                    matchInteraction = new ProfileMatchInteraction();//TODO remove this line on production
                }
                var result = matchInteraction.MeetingStatusId > 0 ? matchInteraction.MeetingStatus.GetDisplayName() : "Pending";
                return await Task.FromResult(result);
            }

            async Task<object> getMatchedProfileMarriageConfirmationStatus(ProfileMatchInteraction profileMatchInteraction)
            {

                var matchInteraction = bothProfileMatchInteractions.FirstOrDefault(p => p.MatchedProfileId == profileMatchInteraction.InitiatorProfileId && p.InitiatorProfileId == profileMatchInteraction.MatchedProfileId);
                if (matchInteraction == null)
                {
                    matchInteraction = new ProfileMatchInteraction();//TODO remove this line on production
                }
                return await Task.FromResult(matchInteraction.MarriageConfirmationStatusId > 0 ? matchInteraction.MarriageConfirmationStatus.GetDisplayName() : "");
            }

            var manager = new PropertyManager<ProfileMatchInteraction, Language>(new[]
            {

                new PropertyByName<ProfileMatchInteraction, Language>("ProfileMatchInteractionId", (p, l) => p.Id),
                new PropertyByName<ProfileMatchInteraction, Language>("UserName", async (p, l) => (await _userService.GetUserFullNameAsync(p.UserId))),
                new PropertyByName<ProfileMatchInteraction, Language>("UserShortName", async (p, l) => (await _userService.GetUserShortNameByUserIdAsync(p.UserId))),
                new PropertyByName<ProfileMatchInteraction, Language>("InitiatorProfileName", async (p, l) => (await _profileService.FormatProfileDetailsAsync(p.InitiatorProfileId))),
                new PropertyByName<ProfileMatchInteraction, Language>("MatchedProfileName", async (p, l) => (await _profileService.FormatProfileDetailsAsync(p.MatchedProfileId))),
                new PropertyByName<ProfileMatchInteraction, Language>("ProfileExchangedStatus",  (p, l) => (p.ProfileSendType.GetDisplayName())),
                new PropertyByName<ProfileMatchInteraction, Language>("MobileExchangedStatus",  (p, l) => (p.IsContactExchanged==true ? "Yes" : "No")),
                new PropertyByName<ProfileMatchInteraction, Language>("InitiatorProfileMatchStatus",  (p, l) => (p.MatchStatus.GetDisplayName())),
                new PropertyByName<ProfileMatchInteraction, Language>("MatchedProfileMatchStatus", async (p, l) => await getMatchedProfileMatchStatus(p)),
               new PropertyByName<ProfileMatchInteraction, Language>("InitiatorProfileMeetingStatus", (p, l) => (p.MeetingStatusId > 0 ? p.MeetingStatus.GetDisplayName() : "Pending")),
                new PropertyByName<ProfileMatchInteraction, Language>("MatchedProfileMeetingStatus", async (p, l) => await getMatchedProfileMeetingStatus(p)),
                 new PropertyByName<ProfileMatchInteraction, Language>("FreshCallStaus",  (p, l) => (p.IsFreshedCall == true ? "Yes" : "No")),
                new PropertyByName<ProfileMatchInteraction, Language>("NoAnswerStatus",  (p, l) => (p.IsNoAnswer==true ? "Yes" : "No")),
                new PropertyByName<ProfileMatchInteraction, Language>("CreatedOn", async (p, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(p.CreatedOnUtc, DateTimeKind.Utc)))
            });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportStatusList",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportStatusLists"), profileMatchInteractions.Count));
            return await manager.ExportToXlsxAsync(profileMatchInteractions);
        }

        public virtual async Task<byte[]> ExportUsersToXlsxAsync(IList<User> users)
        {
            async Task<object> getStateProvince(Customer customer)
            {
                var stateProvinceId = customer.StateProvinceId;

                var stateProvince = await _stateProvinceService.GetStateProvinceByIdAsync(stateProvinceId);

                return stateProvince?.Name ?? string.Empty;
            }

            //property manager 
            var manager = new PropertyManager<User, Language>(new[]
            {
                new PropertyByName<User, Language>("UserId", (u, l) => u.Id),
                new PropertyByName<User, Language>("UserName", (u, l) => u.Customer.Username),
                new PropertyByName<User, Language>("UserShortname", (u, l) => u.ShortName),
                new PropertyByName<User, Language>("Email", (u, l) => u.Customer.Email),
                new PropertyByName<User, Language>("FirstName", (u, l) => u.Customer.FirstName),
                new PropertyByName<User, Language>("LastName", (u, l) => u.Customer.LastName),
                new PropertyByName<User, Language>("Gender",  (u, l) => u.Customer.Gender),
                new PropertyByName<User, Language>("DateOfBirth", (u, l) => u.Customer.DateOfBirth),
                new PropertyByName<User, Language>("StreetAddress", (u, l) => u.Customer.StreetAddress, !_customerSettings.StreetAddressEnabled),
                new PropertyByName<User, Language>("StreetAddress2", (u, l) => u.Customer.StreetAddress2, !_customerSettings.StreetAddress2Enabled),
                new PropertyByName<User, Language>("ZipPostalCode", (u, l) => u.Customer.ZipPostalCode, !_customerSettings.ZipPostalCodeEnabled),
                new PropertyByName<User, Language>("City", (u, l) => u.Customer.City, !_customerSettings.CityEnabled),
                new PropertyByName<User, Language>("County", (u, l) => u.Customer.County, !_customerSettings.CountyEnabled),
                new PropertyByName<User, Language>("StateProvince",  async (u, l) => await getStateProvince(u.Customer), !_customerSettings.StateProvinceEnabled),
                new PropertyByName<User, Language>("Phone", (u, l) => u.Customer.Phone, !_customerSettings.PhoneEnabled),
                new PropertyByName<User, Language>("UserRoles",  async (u, l) =>  string.Join(", ",
                    (await _customerService.GetCustomerRolesAsync(u.Customer)).Select(role => role.Name))),
                new PropertyByName<User, Language>("TimeZone", (u, l) => u.Customer.TimeZoneId, !_dateTimeSettings.AllowCustomersToSetTimeZone),
                new PropertyByName<User, Language>("Office", async (u, l) => (u.OfficeId > 0 ? (await _officeService.GetOfficeByIdAsync(u.OfficeId)).Name:"")),
                  new PropertyByName<User, Language>("ReportManager", async(u, l) => (u.ReportManagerId > 0 ? await _userService.GetUserFullNameAsync(u.ReportManagerId) : "")),
                  new PropertyByName<User, Language>("EmailAccount", (u, l) => u.EmailAccount!=null ? u.EmailAccount.Email : ""),
                new PropertyByName<User, Language>("CreatedOnUtc", async (u, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(u.Customer.CreatedOnUtc, DateTimeKind.Utc))),
                 new PropertyByName<User, Language>("LastActivityOnUtc", async (u, l) =>  (await _dateTimeHelper.ConvertToUserTimeAsync(u.Customer.LastActivityDateUtc, DateTimeKind.Utc))),
                 new PropertyByName<User, Language>("LastLoginDateOnUtc", async (u, l) => (u.Customer.LastLoginDateUtc.HasValue?(await _dateTimeHelper.ConvertToUserTimeAsync(u.Customer.LastLoginDateUtc.Value, DateTimeKind.Utc)):null)),
                 new PropertyByName<User, Language>("Active", (u, l) => u.Customer.Active==true ? "Active" : "Inactive")

            });

            //activity log
            await _customerActivityService.InsertActivityAsync("ExportUsers",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportUsers"), users.Count));

            return await manager.ExportToXlsxAsync(users);
        }

        public virtual async Task<byte[]> ExportProfilesListsToXlsxAsync(IList<Profile> profiles)
        {
            //var s = (await _packageService.GetPackageByIdAsync(0))?.Name ?? "";
            //property manager 

            async Task<object> getAge(Customer customer)
            {
                DateTime? dateOfBirth = customer.DateOfBirth;
                int age = 0;
                if (dateOfBirth.HasValue)
                {
                    DateTime dtcurrent = DateTime.Now;
                    age = (dtcurrent.Year - dateOfBirth.Value.Year);

                }
                return await Task.FromResult(age);
            }

            var manager = new PropertyManager<Profile, Language>(new[]
            {
                new PropertyByName<Profile, Language>("ProfileId", (p, l) => p.Id),
                new PropertyByName<Profile, Language>("ProfileName", async (p, l) => (await _profileService.FormatProfileDetailsAsync(p.Id))),
                new PropertyByName<Profile, Language>("DateOfBirth", (p,l)=> p.Customer.DateOfBirth.Value.ToString("yyyy-MM-dd")),
                new PropertyByName<Profile, Language>("Age", async  (p,l)=> (await getAge(p.Customer))),
                new PropertyByName<Profile, Language>("Height", (p,l)=> p.HeightId>0 ? p.Height.GetDisplayName() : ""),
                new PropertyByName<Profile, Language>("Weight", (p,l)=> p.Weight),
                new PropertyByName<Profile, Language>("Education", async (p,l)=> (await _educationAreaService.GetEducationAreaByIdAsync(p.EducationAreaId))?.Name ?? ""),
                new PropertyByName<Profile, Language>("BirthPlace", async (p,l)=> (await _cityService.GetCityByIdAsync(p.BirthPlaceId))?.Name ?? ""),
                new PropertyByName<Profile, Language>("PresentCity", (p,l)=> p.Customer.City),
                new PropertyByName<Profile, Language>("Community", async (p,l)=> p.CommunityId > 0 ? (await _communityService.GetCommunityByIdAsync(p.CommunityId)).Name : ""),
                new PropertyByName<Profile, Language>("Gotra", async (p,l)=> p.GotraId > 0 ? (await _gotraService.GetGotraByIdAsync(p.GotraId)).Name : ""),
                new PropertyByName<Profile, Language>("BodyType", (p,l)=> p.BodyTypeId > 0 ? p.BodyType.GetDisplayName() : ""),
                new PropertyByName<Profile, Language>("SkinTones", (p,l)=> p.SkinTonesId > 0 ? p.SkinTone.GetDisplayName() : ""),
                new PropertyByName<Profile, Language>("CreatedOn", async (p,l)=> (await _dateTimeHelper.ConvertToUserTimeAsync(p.Customer.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<Profile, Language>("UserName", async (p, l) => (await _userService.GetUserFullNameAsync(p.UserId))),
                new PropertyByName<Profile, Language>("UserShortName", async (p, l) => (await _userService.GetUserShortNameByUserIdAsync(p.UserId))),
                //new PropertyByName<Profile, Language>("Package",  async (p,l)=> (await _packageService.GetPackageByIdAsync(p.PackageId))?.Name ?? ""),
                //new PropertyByName<Profile, Language>("Office", async(p,l)=> (await _officeService.GetOfficeByIdAsync(p.OfficeId))?.Name ?? ""),
               // new PropertyByName<Profile, Language>("RegistrationCharges", (p,l)=> p.RegistrationCharges),
               // new PropertyByName<Profile, Language>("TaxApplied", (p,l)=>p.TaxApplied),
               //new PropertyByName<Profile, Language>("TotalAmount", (p,l)=>p.TotalAmount),
               // new PropertyByName<Profile, Language>("AmountPaid", (p,l)=>p.AmountPaid),
                //new PropertyByName<Profile, Language>("PaymentStatus", async (p,l) => (p.PaymentStatus.GetDisplayName())),
               // new PropertyByName<Profile, Language>("MobileNumber", async (p,l)=> (_profileService.GetMaskedMobileNumber(p.MobileNumber))),
                //new PropertyByName<Profile, Language>("RelationshipManager", async (p,l)=> (p.RelationshipManagerUserId>0 ? await _userService.GetUserFullNameAsync(p.RelationshipManagerUserId):"")),
                //new PropertyByName<Profile, Language>("AssistantRelationshipManager", async (p,l)=> (p.AssistantRelationshipManagerUserId>0 ? await _userService.GetUserFullNameAsync(p.AssistantRelationshipManagerUserId):"")),
                //new PropertyByName<Profile, Language>("ProfileStatus", (p,l)=> p.ProfileStatus.GetDisplayName()),              
                
                //new PropertyByName<Profile, Language>("AbountMe", (p,l)=> p.AboutMe!=null? Regex.Replace(p.AboutMe, "<.*?>", String.Empty) : "")
               
            });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportProfilesList",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportProfilesList"), profiles.Count));
            return await manager.ExportToXlsxAsync(profiles);
        }

        public virtual async Task<byte[]> ExportFollowUpsToXlsxAsync(IList<FollowUp> followUps)
        {
            //property manager 
            var manager = new PropertyManager<FollowUp, Language>(new[]
            {
                new PropertyByName<FollowUp, Language>("FollowUpId", (f, l) => f.Id),
                new PropertyByName<FollowUp, Language>("ProfileName", async (f, l) => (await _profileService.FormatProfileDetailsAsync(f.InitiatorProfileId) + " - " + await _profileService.FormatProfileDetailsAsync(f.MatchedProfileId))),
                new PropertyByName<FollowUp, Language>("ActionStatus", async (f, l) => (await _actionStatusService.GetActionStatusNameByIdAsync(f.ActionStatusId) + " - " + await _reasonService.GetReasonNameByIdAsync(f.ReasonId))),
                new PropertyByName<FollowUp, Language>("ContactNummber", (f, l) => f.ContactNumber),
                new PropertyByName<FollowUp, Language>("Remarks", (f, l) => f.Remarks),
                new PropertyByName<FollowUp, Language>("UserName", async (f, l) => (await _userService.GetUserFullNameAsync(f.UserId))),
                new PropertyByName<FollowUp, Language>("NextFollowUpDate", async(f, l) => f.NextFollowUpDateTimeUtc.HasValue ? (await _dateTimeHelper.ConvertToUserTimeAsync(f.NextFollowUpDateTimeUtc.Value, DateTimeKind.Utc)) : null),
                new PropertyByName<FollowUp, Language>("CreatedOn", async(f, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(f.CreatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportFollowUps",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportMeetings"), followUps.Count));

            return await manager.ExportToXlsxAsync(followUps);
        }

        public virtual async Task<byte[]> ExportMatchStatusListsToXlsxAsync(IList<QueuedEmail> queuedEmails)
        {
            //async Task<object> getPostedFor(int profileMatchInteractionId)
            //{
            //    var profileMatchInteraction = await _profileMatchInteractionService.GetProfileMatchInteractionByIdAsync(profileMatchInteractionId);
            //    return profileMatchInteraction != null ? await _profileService.FormatProfileDetailsAsync(profileMatchInteraction.MatchedProfileId) : "";
            //}
            //property manager 
            var manager = new PropertyManager<QueuedEmail, Language>(new[]
            {
                // new PropertyByName<(<int,QueuedEmail>), Language>("PostedFor", async (e, l) => (await _profileService.FormatProfileDetailsAsync(e.))),
                new PropertyByName<QueuedEmail, Language>("EmailId", (e, l) => e.Id),
               new PropertyByName<QueuedEmail, Language>("BatchNo",  (e,l)=> ""),
                new PropertyByName<QueuedEmail, Language>("UserName", async (e, l) => (await _userService.GetUserFullNameAsync(e.UserId))),
                new PropertyByName<QueuedEmail, Language>("UserShortName", async (e, l) => (await _userService.GetUserShortNameByUserIdAsync(e.UserId))),
                new PropertyByName<QueuedEmail, Language>("ProfileName", async (e, l) => (await _profileService.FormatProfileDetailsAsync(e.ProfileId))),
                new PropertyByName<QueuedEmail, Language>("From", (e,l)=>e.From),
                new PropertyByName<QueuedEmail, Language>("FromName", (e,l)=> e.FromName),
                new PropertyByName<QueuedEmail, Language>("To", (e,l)=> e.To),
                new PropertyByName<QueuedEmail, Language>("ToName",  (e,l)=> e.ToName),               
                //new PropertyByName<QueuedEmail, Language>("Email To/By Hand", (e,l)=> ""),
                new PropertyByName<QueuedEmail, Language>("ReplyTo", (e,l)=> e.ReplyTo),
                new PropertyByName<QueuedEmail, Language>("ReplyToName", (e,l)=> e.ReplyToName),
                new PropertyByName<QueuedEmail, Language>("CC",  (e,l)=> e.CC),
                new PropertyByName<QueuedEmail, Language>("Bcc",  (e,l)=> e.Bcc),
                new PropertyByName<QueuedEmail, Language>("Subject",  (e,l)=> e.Subject),
                new PropertyByName<QueuedEmail, Language>("AttachmentFilePath",  (e,l)=> e.AttachmentFilePath),
                new PropertyByName<QueuedEmail, Language>("AttachmentFileName",  (e,l)=> e.AttachmentFileName),
                new PropertyByName<QueuedEmail, Language>("CreatedOn", async(e, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(e.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<QueuedEmail, Language>("SendOn",  async(e, l) => (e.SentOnUtc.HasValue ? await _dateTimeHelper.ConvertToUserTimeAsync(e.SentOnUtc.Value, DateTimeKind.Utc):null)),
                new PropertyByName<QueuedEmail, Language>("SendStatus",  (e, l) => (e.SentOnUtc.HasValue ? "Sent":"Pending")),
                new PropertyByName<QueuedEmail, Language>("DontSendBeforeDate",  async(e, l) => (e.DontSendBeforeDateUtc.HasValue ?await _dateTimeHelper.ConvertToUserTimeAsync(e.DontSendBeforeDateUtc.Value, DateTimeKind.Utc):null)),
                new PropertyByName<QueuedEmail, Language>("SentTries",  (e,l)=> e.SentTries),
            });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportMatchStatus",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportMatchStatus"), queuedEmails.Count));
            return await manager.ExportToXlsxAsync(queuedEmails);
        }

        public virtual async Task<byte[]> ExportClientMeetingsToXlsxAsync(IList<ClientMeeting> clientMeetings)
        {
            //property manager 
            var manager = new PropertyManager<ClientMeeting, Language>(new[]
            {
                new PropertyByName<ClientMeeting, Language>("MeetingId", (m, l) => m.Id),
                new PropertyByName<ClientMeeting, Language>("UserName", async (m, l) => (await _userService.GetUserFullNameAsync(m.UserId))),
                new PropertyByName<ClientMeeting, Language>("UserShortName", async (m, l) => (await _userService.GetUserShortNameByUserIdAsync(m.UserId))),
                new PropertyByName<ClientMeeting, Language>("ContactName", async (m,l)=> (await _profileService.FormatProfileDetailsAsync(m.ProfileId))),
                new PropertyByName<ClientMeeting, Language>("Venue",  (m, l) => m.ClientMeetingVenue.GetDisplayName()),
                new PropertyByName<ClientMeeting, Language>("ScheduledDateTime", async(m, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(m.ScheduledDateTime.Value, DateTimeKind.Utc))),
                new PropertyByName<ClientMeeting, Language>("Description", (m, l) => m.Description ),
                new PropertyByName<ClientMeeting, Language>("Notes", (m, l) => m.Notes),
                new PropertyByName<ClientMeeting, Language>("CreatedOn", async(m, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(m.CreatedOnUtc, DateTimeKind.Utc))),
                new PropertyByName<ClientMeeting, Language>("UpdatedOn", async(m, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(m.UpdatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportClientMeetings",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportClientMeetings"), clientMeetings.Count));

            return await manager.ExportToXlsxAsync(clientMeetings);
        }

        public virtual async Task<byte[]> ExportPaymentListsToXlsxAsync(IList<OrderPaymentHistory> orderPaymentHistories)
        {
            //var s = (await _packageService.GetPackageByIdAsync(0))?.Name ?? "";
            //property manager 

            var manager = new PropertyManager<OrderPaymentHistory, Language>(new[]
            {

                new PropertyByName<OrderPaymentHistory, Language>("OrderPaymentHistoryId", (p, l) => p.Id),
                new PropertyByName<OrderPaymentHistory, Language>("ProfileName", async (p, l) => (await _profileService.FormatProfileDetailsAsync(p.ProfileId))),
                new PropertyByName<OrderPaymentHistory, Language>("RegistrationCharges", async(p,l)=> (await _profileService.GetProfileByIdAsync(p.ProfileId)).RegistrationCharges),
                new PropertyByName<OrderPaymentHistory, Language>("Office", async(p,l)=> (await _officeService.GetOfficeByIdAsync((await _profileService.GetProfileByIdAsync(p.ProfileId)).OfficeId))?.Name ?? ""),
                new PropertyByName<OrderPaymentHistory, Language>("TransactionID", (p,l)=>p.TransactionId),
                new PropertyByName<OrderPaymentHistory, Language>("PaymentMethod", (p,l)=>p.PaymentMethod.GetDisplayName()),
                new PropertyByName<OrderPaymentHistory, Language>("AmountPaid", (p,l)=>p.AmountPaid),
                new PropertyByName<OrderPaymentHistory, Language>("Remarks", (p,l)=>p.Remarks),
                new PropertyByName<OrderPaymentHistory, Language>("PaidDate", async (p,l)=> (await _dateTimeHelper.ConvertToUserTimeAsync(p.PaidDateUtc, DateTimeKind.Utc)).ToString("dd/MM/yyyy")),
                new PropertyByName<OrderPaymentHistory, Language>("UserName", async (p, l) => (await _userService.GetUserFullNameAsync(p.UserId))),
                new PropertyByName<OrderPaymentHistory, Language>("UserShortName", async (p, l) => (await _userService.GetUserShortNameByUserIdAsync(p.UserId))),

            });

            //activity log
            await _customerActivityService.InsertActivityAsync("ExportPaymentList",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportPaymentLists"), orderPaymentHistories.Count));
            return await manager.ExportToXlsxAsync(orderPaymentHistories);
        }

        public virtual async Task<byte[]> ExportEightPMDailyReportRMToXlsxAsync(DataTable eightPMDailyReportRMData, string heading1, string heading2,string filename)
        {
            string baseFilePath = GetScheduledReportExcelFilePath();
            //property manager 
            var manager = new PropertyManager<ReportData, Language>(new[]
            {
                new PropertyByName<ReportData, Language>("Sl", (s, l) => s.Sl)

            });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportEightPMDailyReportRM",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), eightPMDailyReportRMData.Rows.Count));

            return await manager.ExportToXlsxAsync(eightPMDailyReportRMData, heading1, heading2, baseFilePath, filename);
        }

        public virtual async Task<byte[]> ExportEightPMDailyReportCMDToXlsxAsync(DataTable eightPMDailyReportCMDData, string heading1, string heading2,string filename)
        {
            string baseFilePath = GetScheduledReportExcelFilePath();
            //property manager 
            var manager = new PropertyManager<ReportData, Language>(new[]
            {
                new PropertyByName<ReportData, Language>("Sl", (s, l) => s.Sl)

            });

            List<string> mergeColumnsRange = new List<string>();
            mergeColumnsRange = GetMergeColumnRangeList(eightPMDailyReportCMDData, "TD");

            //activity log
            await _customerActivityService.InsertActivityAsync("ExportEightPMDailyReportCMD",
            string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), eightPMDailyReportCMDData.Rows.Count));

            return await manager.ExportToXlsxAsync(eightPMDailyReportCMDData, heading1, heading2, baseFilePath, filename, mergeColumnsRange);
        }



        public virtual async Task<byte[]> ExportWeeklyComparisonReportCMDToXlsxAsync(DataTable WeeklyComparisonReportCMDData, string heading1, string heading2,string filename)
        {
            string baseFilePath = GetScheduledReportExcelFilePath();
            //property manager 
            var manager = new PropertyManager<ReportData, Language>(new[]
            {
                new PropertyByName<ReportData, Language>("Sl", (s, l) => s.Sl)

             });

            List<string> mergeColumnsRange = new List<string>();
            mergeColumnsRange = GetMergeColumnRangeList(WeeklyComparisonReportCMDData, "LW");
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportWeeklyComparisonReportCMD",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), WeeklyComparisonReportCMDData.Rows.Count));

            return await manager.ExportToXlsxAsync(WeeklyComparisonReportCMDData, heading1, heading2, baseFilePath, filename, mergeColumnsRange);
        }

        public virtual async Task<byte[]> ExportMonthlyComparisonReportCMDToXlsxAsync(DataTable MonthlyComparisonReportCMDData, string heading1, string heading2,string filename)
        {
            string baseFilePath = GetScheduledReportExcelFilePath();
            //property manager 
            var manager = new PropertyManager<ReportData, Language>(new[]
            {
                new PropertyByName<ReportData, Language>("Sl", (s, l) => s.Sl)
            });

            List<string> mergeColumnsRange = new List<string>();
            mergeColumnsRange = GetMergeColumnRangeList(MonthlyComparisonReportCMDData, "LM");
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportMonthlyComparisonReportCMD",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), MonthlyComparisonReportCMDData.Rows.Count));


            return await manager.ExportToXlsxAsync(MonthlyComparisonReportCMDData, heading1, heading2, baseFilePath, filename, mergeColumnsRange);
        }

        public virtual async Task<byte[]> ExportAnalyticalReportToXlsxAsync(DataTable AnalyticalReportData, string heading1, string heading2, string filename)
        {
            string baseFilePath = GetScheduledReportExcelFilePath();
            //property manager 
            var manager = new PropertyManager<ReportData, Language>(new[]
            {
                new PropertyByName<ReportData, Language>("Sl", (s, l) => s.Sl)
            });

            List<string> mergeColumnsRange = new List<string>();
            mergeColumnsRange = GetMergeColumnRangeList(AnalyticalReportData, "LM");
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportAnalyticalReport",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), AnalyticalReportData.Rows.Count));


            return await manager.ExportToXlsxAsync(AnalyticalReportData, heading1, heading2, baseFilePath, filename, mergeColumnsRange);
        }

        public virtual async Task<byte[]> ExportAnalyticalReportOfRMToXlsxAsync(DataTable AnalyticalReportOfRMData, string heading1, string heading2, string filename)
        {
            string baseFilePath = GetScheduledReportExcelFilePath();
            //property manager 
            var manager = new PropertyManager<ReportData, Language>(new[]
            {
                new PropertyByName<ReportData, Language>("Sl", (s, l) => s.Sl)
            });

            List<string> mergeColumnsRange = new List<string>();
            mergeColumnsRange = GetMergeColumnRangeList(AnalyticalReportOfRMData, "LM");
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportAnalyticalReport",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportShortLists"), AnalyticalReportOfRMData.Rows.Count));


            return await manager.ExportToXlsxAsync(AnalyticalReportOfRMData, heading1, heading2, baseFilePath, filename, mergeColumnsRange);
        }
        public virtual async Task<byte[]> ExportHoursOnCRMToXlsxAsync(IList<UserWorkingHoursHistory> hoursOnCRM)
        {
            //property manager 
            var manager = new PropertyManager<UserWorkingHoursHistory, Language>(new[]
            {
                new PropertyByName<UserWorkingHoursHistory, Language>("Id", (u, l) => u.Id),
                new PropertyByName<UserWorkingHoursHistory, Language>("UserName", async (u, l) => (await _userService.GetUserFullNameAsync(u.UserId))),
                new PropertyByName<UserWorkingHoursHistory, Language>("UserShortName", async (u, l) => (await _userService.GetUserShortNameByUserIdAsync(u.UserId))),
                new PropertyByName<UserWorkingHoursHistory, Language>("WorkingDate", async(u, l) => u.WorkingDate),
                new PropertyByName<UserWorkingHoursHistory, Language>("TotalHours", (u, l) => u.TotalHours ),
                new PropertyByName<UserWorkingHoursHistory, Language>("TotalMinutes", (u, l) => u.TotalMinutes)
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportHoursOnCRM",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportHoursOnCRM"), hoursOnCRM.Count));

            return await manager.ExportToXlsxAsync(hoursOnCRM);
        }

        public virtual async Task<byte[]> ExportDataExecutiveToXlsxAsync(IList<ActivityLog> activityLogs)
        {
            //property manager 
            var manager = new PropertyManager<ActivityLog, Language>(new[]
            {
                new PropertyByName<ActivityLog, Language>("Id", (a, l) => a.Id),
                new PropertyByName<ActivityLog, Language>("ActivityLogType", async (a, l) => (await _customerActivityService.GetActivityTypeByIdAsync(a.ActivityLogTypeId))?.Name),
                new PropertyByName<ActivityLog, Language>("ProfileId", (a, l) => (a.ProfileId)),
                new PropertyByName<ActivityLog, Language>("Message", (a, l) => a.Comment),
                new PropertyByName<ActivityLog, Language>("Email", async (a, l) => (await _customerService.GetCustomerByIdAsync(a.CustomerId)).Email),
                new PropertyByName<ActivityLog, Language>("IPAddress", (a, l) => a.IpAddress),
                new PropertyByName<ActivityLog, Language>("CreatedOn", async (a, l) => (await _dateTimeHelper.ConvertToUserTimeAsync(a.CreatedOnUtc, DateTimeKind.Utc)))
               });
            //activity log
            await _customerActivityService.InsertActivityAsync("ExportDataExecutive",
                string.Format(await _localizationService.GetResourceAsync("ActivityLog.ExportDataExecutive"), activityLogs.Count));

            return await manager.ExportToXlsxAsync(activityLogs);
        }
        protected virtual string GetScheduledReportExcelFilePath()
        {
            return _fileProvider.MapPath(PageBaaSDefaults.ScheduledReportExcelFilePath);
        }

        public List<string> GetMergeColumnRangeList(DataTable reportData, string colCode)
        {
            List<string> mergeColumnsRange = new List<string>();
            for (int c = 2; c < reportData.Columns.Count; c++)
            {
                if (!reportData.Columns[c].ColumnName.Contains("_" + colCode))
                {
                    mergeColumnsRange.Add(GetExcelColumnName(c + 1) + "3:" + GetExcelColumnName(c + 2) + "3");
                }
            }
            return mergeColumnsRange;
        }

        private string GetExcelColumnName(int colno)
        {
            string colName = "";
            int divisor = 26, dividend = colno, reminder = 0, quotient = 0;
            int unicode = 0;
            if (colno > 26)
            {
                quotient = Math.DivRem(dividend, divisor, out reminder);
                unicode = 64 + quotient;
                char character = (char)unicode;
                colName = character.ToString();
                unicode = 64 + reminder;
                character = (char)unicode;
                colName = colName + character.ToString();
            }
            else
            {
                unicode = 64 + colno;
                char character = (char)unicode;
                colName = character.ToString();
            }
            return colName;

        }


    }
}
