﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain.Customers;
using Subhlagan.Core.Domain.Messages;

using Subhlagan.Core.Events;
using Subhlagan.Core.Domain;
using Subhlagan.Application.Messages;
using Subhlagan.Application.Profiles;
using Subhlagan.Application.Users;
using Subhlagan.Application.Configuration;
using Subhlagan.Application.Customers;
using Subhlagan.Application.Events;
using Subhlagan.Application.Logging;
using Subhlagan.Application.Messages;
using Subhlagan.Application.Security;


namespace Subhlagan.Application.Events
{
    public class EventConsumer :
          IConsumer<EntityTokensAddedEvent<Customer, Token>>,
          IConsumer<EntityUpdatedEvent<Customer>>,
          IConsumer<EntityDeletedEvent<Customer>>,
          IConsumer<EntityInsertedEvent<Subhlagan.Core.Domain.Logging.ActivityLog>>

    {
        private readonly IWorkContext _workContext;
        private readonly ICustomerService _customerService;
        
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger _logger;
        private readonly ISettingService _settingService;
        private readonly IStaticCacheManager _staticCacheManager;
        private readonly IEncryptionService _encryptionService;
        private readonly CustomerSettings _customerSettings;
        private readonly IProfileService _profileService;
        private readonly IUserService _userService;
        private readonly SubhMessageTokenProvider _messageTokenProvider;
        private readonly Subhlagan.Application.Logger.CustomerActivityService _customerActivityService;


        public EventConsumer(IHttpClientFactory httpClientFactory, ILogger logger, ISettingService settingService,  IStaticCacheManager staticCacheManager, IWorkContext workContext, ICustomerService customerService, IEncryptionService encryptionService, CustomerSettings customerSettings, IProfileService profileService, IUserService userService, SubhMessageTokenProvider messageTokenProvider, Logger.CustomerActivityService customerActivityService)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _settingService = settingService;
            
            _staticCacheManager = staticCacheManager;
            _workContext = workContext;
            _customerService = customerService;
            _encryptionService = encryptionService;
            _customerSettings = customerSettings;
            _profileService = profileService;
            _userService = userService;
            _messageTokenProvider = messageTokenProvider;
            _customerActivityService = customerActivityService;
        }

        public Task HandleEventAsync(EntityTokensAddedEvent<Customer, Token> eventMessage)
        {
            //eventMessage.Tokens.Add(new Token("Customer.Password", eventMessage.));
            return Task.CompletedTask;
        }

        //public async Task HandleEventAsync(EntityTokensAddedEvent<Store, Token> eventMessage)
        //{
        //    await _messageTokenProvider.AddSignatureTokensAsync(eventMessage.Tokens, eventMessage.Entity, null);
        //}

        public async Task HandleEventAsync(EntityUpdatedEvent<Customer> eventMessage)
        {
            var customer = eventMessage.Entity ?? throw new ArgumentNullException(nameof(eventMessage.Entity));
            var customerId = customer.Id;

            //await _staticCacheManager.RemoveAsync(PageBaaSDefaults.ProfileByCustomerIdCacheKey, customerId);
            //await _staticCacheManager.RemoveAsync(PageBaaSDefaults.UserByCustomerIdCacheKey, customerId);

            //var profile = await _profileService.GetProfileByCustomerIdAsync(customerId);
            //if (profile != null)
            //{
            //    await _staticCacheManager.RemoveAsync(KsEntityCacheDefaults<Profile>.ByIdCacheKey, profile.Id);
            //}

            var user = await _userService.GetUserByCustomerIdAsync(customerId);
            if (user != null)
            {
                await _staticCacheManager.RemoveAsync(KsEntityCacheDefaults<User>.ByIdCacheKey, user.Id);
            }
        }

        public async Task HandleEventAsync(EntityDeletedEvent<Customer> eventMessage)
        {
            var customer = eventMessage.Entity ?? throw new ArgumentNullException(nameof(eventMessage.Entity));
            var customerId = customer.Id;

            //await _staticCacheManager.RemoveAsync(PageBaaSDefaults.ProfileByCustomerIdCacheKey, customerId);
            //await _staticCacheManager.RemoveAsync(PageBaaSDefaults.UserByCustomerIdCacheKey, customerId);

            //var profile = await _profileService.GetProfileByCustomerIdAsync(customerId);
            //if (profile != null)
            //{
            //    await _staticCacheManager.RemoveAsync(KsEntityCacheDefaults<Profile>.ByIdCacheKey, profile.Id);
            //}

            var user = await _userService.GetUserByCustomerIdAsync(customerId);
            if (user != null)
            {
                await _staticCacheManager.RemoveAsync(KsEntityCacheDefaults<User>.ByIdCacheKey, user.Id);
            }
        }

        public async Task HandleEventAsync(EntityInsertedEvent<Subhlagan.Core.Domain.Logging.ActivityLog> eventMessage)
        {
            var activitylog = eventMessage.Entity ?? throw new ArgumentNullException(nameof(eventMessage.Entity));

            IList<string> activiyTypes = new List<string>() { "PublicStore.Login", "PublicStore.Logout" };

            var activityTypeIds = (await _customerActivityService.GetAllActivityTypesAsync())
            .Where(x => activiyTypes.Contains(x.SystemKeyword)).Select(x => x.Id).ToList();

            if (activityTypeIds.Contains(activitylog.ActivityLogTypeId))
            {
                var profile = await _profileService.GetProfileByCustomerIdAsync(activitylog.CustomerId);
                if (profile != null)
                {
                    var newactivityLog = await _customerActivityService.GetActivityLogByIdAsync(activitylog.Id);
                    if (newactivityLog != null)
                    {
                        newactivityLog.ProfileId = profile.Id;
                        await _customerActivityService.UpdateActivitiesAsync(newactivityLog);
                    }
                    
                }
            }
        }
    }
}
