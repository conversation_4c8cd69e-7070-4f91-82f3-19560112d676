﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Profiles;
using Subhlagan.Application.Users;

namespace Subhlagan.Application.Meetings
{
    public partial class MeetingService : IMeetingService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Meeting> _meetingRepository;
        private readonly EnhancedEntityRepository<Profile> _profileRepository;
        private readonly IUserService _userService;
        private readonly IProfileMatchService _profileMatchService;

        #endregion

        #region Ctor

        public MeetingService(EnhancedEntityRepository<Meeting> meetingRepository,
            EnhancedEntityRepository<Profile> profileRepository,
            IUserService userService,
            IProfileMatchService profileMatchService)
        {
            _meetingRepository = meetingRepository;
            _profileRepository = profileRepository;
            _userService = userService;
            _profileMatchService = profileMatchService;
        }

        #endregion

        #region Methods

        public virtual async Task InsertMeetingAsync(Meeting meeting)
        {
            if (meeting == null)
                throw new ArgumentNullException(nameof(meeting));

            meeting.Venue = CommonHelper.EnsureNotNull(meeting.Venue);
            meeting.Venue = meeting.Venue.Trim();
            meeting.Venue = CommonHelper.EnsureMaximumLength(meeting.Venue, 255);

            await _meetingRepository.InsertAsync(meeting);
        }

        public virtual async Task UpdateMeetingAsync(Meeting meeting)
        {
            if (meeting == null)
                throw new ArgumentNullException(nameof(meeting));

            meeting.Venue = CommonHelper.EnsureNotNull(meeting.Venue);
            meeting.Venue = meeting.Venue.Trim();
            meeting.Venue = CommonHelper.EnsureMaximumLength(meeting.Venue, 255);

            await _meetingRepository.UpdateAsync(meeting);
        }

        public virtual async Task DeleteMeetingAsync(Meeting meeting)
        {
            if (meeting == null)
                throw new ArgumentNullException(nameof(meeting));

            await _meetingRepository.DeleteAsync(meeting);
        }

        public virtual async Task<Meeting> GetMeetingByIdAsync(int meetingId)
        {
            return await _meetingRepository.GetByIdAsync(meetingId, cache => default);
        }

        /// <summary>
        /// Get all meetings with optional filters
        /// </summary>
        /// <param name="meetingDateFrom">Filter by meeting start date (inclusive); pass null to ignore</param>
        /// <param name="meetingDateTo">Filter by meeting end date (inclusive); pass null to ignore</param>
        /// <param name="maleProfileId">Filter by male profile ID; pass 0 to ignore</param>
        /// <param name="femaleProfileId">Filter by female profile ID; pass 0 to ignore</param>
        /// <param name="userId">Filter by user ID; pass 0 to ignore</param>
        /// <param name="pageIndex">Page index for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <param name="getOnlyTotalCount">Indicates whether only the total count of records is needed</param>
        /// <returns>
        /// A task that represents the asynchronous operation
        /// The task result contains the paginated list of meetings
        /// </returns>
        public virtual async Task<IPagedList<Meeting>> GetAllMeetingsAsync(
            DateTime? meetingDateFrom = null,
            DateTime? meetingDateTo = null,
            int maleProfileId = 0,
            int femaleProfileId = 0,
            IList<int> userIds = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            string Name = null,
            IList<int> maleProfileIds = null,
            IList<int> femaleProfileIds = null,
            IList<int> assignedUserIds = null, IList<int> meetingIds = null,
            List<int> matchResultIds = null, bool? isClosed = null)
        {
            var query = _meetingRepository.Table;

            // Filter by meeting start date
            if (meetingDateFrom.HasValue)
                query = query.Where(m => m.ScheduledDateTime >= meetingDateFrom.Value);

            // Filter by meeting end date
            if (meetingDateTo.HasValue)
                query = query.Where(m => m.ScheduledDateTime <= meetingDateTo.Value);

            // Filter by male profile ID
            if (maleProfileId > 0)
                query = query.Where(m => m.MaleProfileId == maleProfileId);

            // Filter by female profile ID
            if (femaleProfileId > 0)
                query = query.Where(m => m.FemaleProfileId == femaleProfileId);

            // Filter by user ID
            if (userIds?.Any() ?? false)
                query = query.Where(m => userIds.Contains(m.UserId));

            //if (matchResultIds?.Any() ?? false)
            //{
            //    var matchResultProfileIds = new HashSet<int>(await _profileMatchService.GetProfileIdsByMatchResultAsync(matchResultIds));

            //    query = query.Where(p => matchResultProfileIds.Contains(p.MaleProfileId) &&
            //                             matchResultProfileIds.Contains(p.FemaleProfileId));
            //}

            if (matchResultIds?.Any() ?? false)
            {
                query = from pm in query
                        join initiator in _profileRepository.Table on pm.MaleProfileId equals initiator.Id
                        join matched in _profileRepository.Table on pm.FemaleProfileId equals matched.Id
                        where matchResultIds.Contains(initiator.MatchResultId) &&
                              matchResultIds.Contains(matched.MatchResultId)
                        select pm;
            }

            // Order by creation date
            query = query.OrderBy(m => m.ScheduledDateTime).ThenBy(m => m.CreatedOnUtc);

            if (assignedUserIds != null && assignedUserIds.Any())
            {
                var relevantProfileIds = _profileRepository.Table
                    .Where(p => userIds.Contains(p.RelationshipManagerUserId)
                             || userIds.Contains(p.AssistantRelationshipManagerUserId))
                    .Select(p => p.Id);

                query = query.Where(m => relevantProfileIds.Contains(m.MaleProfileId)
                                      || relevantProfileIds.Contains(m.FemaleProfileId));

            }


            // filter by Male Profile Id
            if (maleProfileIds != null && maleProfileIds.Any())
                query = query.Where(m => maleProfileIds.Contains(m.MaleProfileId));

            // filter by female Profile Id
            if (femaleProfileIds != null && femaleProfileIds.Any())
                query = query.Where(m => femaleProfileIds.Contains(m.FemaleProfileId));

            if (meetingIds != null && meetingIds.Any())
            {
                query = query.Where(m => meetingIds.Contains(m.Id));
            }

            if (isClosed.HasValue)
                query = query.Where(m => m.IsClosed == isClosed);

            query = query.OrderBy(m => m.CreatedOnUtc);

            // Order by creation date
            query = query.OrderByDescending(m => m.ScheduledDateTime).ThenBy(m => m.CreatedOnUtc);

            // Return paginated result
            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);
        }

        //public virtual async Task<IList<Meeting>> GetAllMeetingsAsync(DateTime? meetingDateFrom = null, DateTime? meetingDateTo = null,
        //    int maleProfileId = 0, int femaleProfileId = 0, int userId = 0)
        //{
        //    var meetings = await _meetingRepository.GetAllAsync(query =>
        //    {
        //        //if (createdFromUtc.HasValue)
        //        //    query = query.Where(c => createdFromUtc.Value <= c.CreatedOnUtc);
        //        //if (createdToUtc.HasValue)
        //        //    query = query.Where(c => createdToUtc.Value >= c.CreatedOnUtc);

        //        if (meetingDateFrom.HasValue)
        //            query = query.Where(m => m.ScheduledDateTime >= meetingDateFrom.Value);

        //        if (meetingDateTo.HasValue)
        //            query = query.Where(m => m.ScheduledDateTime <= meetingDateTo.Value);

        //        if (maleProfileId > 0)
        //            query = query.Where(m => m.MaleProfileId == maleProfileId);
        //        if (femaleProfileId > 0)
        //            query = query.Where(m => m.FemaleProfileId == femaleProfileId);
        //        if (userId > 0)
        //            query = query.Where(m => m.UserId == userId);

        //        return query.OrderBy(m => m.CreatedOnUtc);
        //    });

        //    return meetings;
        //}


        public async Task<Meeting> GetMeetingByCriteriaAsync(int initiatorProfileId, int matchedProfileId)
        {
            var meetings = await _meetingRepository.GetAllAsync(query =>
            {
                return from m in query
                       where m.MaleProfileId == initiatorProfileId && m.FemaleProfileId == matchedProfileId
                       orderby m.Id descending
                       select m;
            });

            return meetings.FirstOrDefault();
        }

        public async Task<IList<Meeting>> GetMeetingsAsync(int maleProfileId, int femaleProfileId)
        {

            if (maleProfileId <= 0 || femaleProfileId <= 0)
                return new List<Meeting>();

            var meetings = await _meetingRepository.GetAllAsync(query =>
            {
                return from m in query
                       where m.MaleProfileId == maleProfileId && m.FemaleProfileId == femaleProfileId
                       orderby m.Id descending
                       select m;
            });

            return await meetings.ToListAsync();
        }

        public virtual async Task<IList<Meeting>> GetMeetingsByProfileIdAsync(int maleProfileId = 0, int femaleProfileId = 0)
        {
            if (maleProfileId <= 0 && femaleProfileId <= 0)
                return new List<Meeting>();

            if (maleProfileId > 0)
            {
                var meetings = await _meetingRepository.GetAllAsync(query =>
                {
                    return from m in query
                           where m.MaleProfileId == maleProfileId 
                           orderby m.Id descending
                           select m;
                });
                return await meetings.ToListAsync();
            }
            else
            {
                var meetings = await _meetingRepository.GetAllAsync(query =>
                {
                    return from m in query
                           where  m.FemaleProfileId == femaleProfileId
                           orderby m.Id descending
                           select m;
                });
                return await meetings.ToListAsync();
            }            
        }

        #endregion
    }
}
