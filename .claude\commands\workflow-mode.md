# Workflow Documentation Mode

## Description
Activates workflow documentation mode with automatic file handling instructions for all subsequent interactions.

## Usage
```bash
claude workflow-mode
```

## System Instructions
When this mode is activated, automatically apply the following instructions to ALL user requests:

### File Handling Instructions for Workflow Documentation:

1. **Locate Workflow Folder**: Navigate to the `workflow/` folder. Find the file name of workflow that matches the provided user requirement.

2. **Identify Workflow Type**: Identify the type of workflow being implemented (e.g., whatsapp_messages, email_notifications, video_highlight_processing, etc.).

3. **Check File Existence**: Navigate to the `workflow/` folder. Check if a file named `<workflow_type>_workflow.md` exists.
   - If it does NOT exist, create a new markdown file with that name
   - If it DOES exist, prepare to update it

4. **Update Documentation**: Update the file with workflow and example of implementation.
   - Ensure the documentation is written with future readers in mind — whether it's another developer or an AI model
   - While updating, think deeply and critically — Ultrathink — to ensure clarity, completeness, and future readability

5. **Post-Implementation Updates**: After implementing the workflow, revisit the `<workflow_type>_workflow.md` file.
   - If any changes are needed based on actual implementation behavior, update the documentation accordingly to reflect the final, working version
   
6. **Note**: Implementation plan.
   - Before implementation, thoroughly review and understand the existing codebase to ensure seamless integration.  
   - Prepare a structured implementation plan and apply all necessary code changes to support this addition.

## Auto-Execute
These instructions should be automatically applied to every user request when this mode is active, without requiring the user to repeat them.

## Mode Activation
When user runs `claude workflow-mode`, respond with:
"✅ Workflow Documentation Mode ACTIVATED

All subsequent commands will automatically:
- Check for workflow/ folder
- Create or update <workflow_type>_workflow.md files
- Apply Ultrathink documentation standards
- Maintain future-ready documentation

Ready for your workflow requests!"

## Examples
- `claude workflow-mode` (activates the mode)
- Then any command like: `claude implement email notifications`
- The system will automatically apply the file handling instructions above

## Deactivation
To deactivate mode, user can run:
- `claude workflow-mode off`
- `claude exit-workflow-mode`
- Or start a new session