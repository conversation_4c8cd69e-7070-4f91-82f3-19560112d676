﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Offices
{
    public interface IOfficeService
    {
        Task InsertOfficeAsync(Office office);
        Task UpdateOfficeAsync(Office office);
        Task DeleteOfficeAsync(Office office);
        Task<Office> GetOfficeByIdAsync(int officeId);
        Task<IList<Office>> GetAllOfficesAsync(bool showHidden = false);
        Task<string> GetOfficeDetailsByIdAsync(int officeId);
    }
}
