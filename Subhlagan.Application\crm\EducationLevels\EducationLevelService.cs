﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.EducationLevels
{
    public partial class EducationLevelService : IEducationLevelService
    {
        #region Fields

        private readonly EnhancedEntityRepository<EducationLevel> _educationLevelRepository;

        #endregion

        #region Ctor

        public EducationLevelService(EnhancedEntityRepository<EducationLevel> educationLevelRepository)
        {
            _educationLevelRepository = educationLevelRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertEducationLevelAsync(EducationLevel educationLevel)
        {
            if (educationLevel == null)
                throw new ArgumentNullException(nameof(educationLevel));

            educationLevel.Name = CommonHelper.EnsureNotNull(educationLevel.Name).Trim();
            educationLevel.Name = CommonHelper.EnsureMaximumLength(educationLevel.Name, 255);

            await _educationLevelRepository.InsertAsync(educationLevel);
        }

        public virtual async Task UpdateEducationLevelAsync(EducationLevel educationLevel)
        {
            if (educationLevel == null)
                throw new ArgumentNullException(nameof(educationLevel));

            educationLevel.Name = CommonHelper.EnsureNotNull(educationLevel.Name).Trim();
            educationLevel.Name = CommonHelper.EnsureMaximumLength(educationLevel.Name, 255);

            await _educationLevelRepository.UpdateAsync(educationLevel);
        }

        public virtual async Task DeleteEducationLevelAsync(EducationLevel educationLevel)
        {
            if (educationLevel == null)
                throw new ArgumentNullException(nameof(educationLevel));

            await _educationLevelRepository.DeleteAsync(educationLevel);
        }

        public virtual async Task<EducationLevel> GetEducationLevelByIdAsync(int educationLevelId)
        {
            return await _educationLevelRepository.GetByIdAsync(educationLevelId, cache => default);
        }

        public virtual async Task<IPagedList<EducationLevel>> GetAllEducationLevelsAsync(
            string name = null,
            bool? published = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false)
        {
            var educationLevels = await _educationLevelRepository.GetAllPagedAsync(query =>
            {
                if (!string.IsNullOrWhiteSpace(name))
                    query = query.Where(el => el.Name.Contains(name));

                if (published.HasValue)
                    query = query.Where(el => el.Published == published.Value);

                query = query.Where(el => !el.Deleted);

                query = query.OrderBy(el => el.DisplayOrder).ThenBy(el => el.Name);

                return query;
            }, pageIndex, pageSize, getOnlyTotalCount);

            return educationLevels;
        }


        #endregion
    }

}
