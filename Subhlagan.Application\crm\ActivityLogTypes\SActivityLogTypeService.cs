﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain.Logging;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Localization;
using Subhlagan.Application.Logging;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Subhlagan.Application.ActivityLogTypes
{
    public partial class SActivityLogTypeService : ISActivityLogTypeService
    {
        private readonly EnhancedEntityRepository<ActivityLogType> _activityLogTypeRepository;
        private readonly ILocalizationService _localizationService;
        private readonly ICustomerActivityService _customerActivityService;
        private readonly EnhancedEntityRepository<ActivityLog> _activityLogRepository;

        public SActivityLogTypeService(EnhancedEntityRepository<ActivityLogType> activityLogTypeRepository, ILocalizationService localizationService, ICustomerActivityService customerActivityService, EnhancedEntityRepository<ActivityLog> activityLogRepository)
        {
            _activityLogTypeRepository = activityLogTypeRepository;
            _localizationService = localizationService;
            _customerActivityService = customerActivityService;
            _activityLogRepository = activityLogRepository;
        }

        public virtual async Task PrepareActivityLogTypesAsync(IList<SelectListItem> items, bool withSpecialDefaultItem = true, string defaultItemText = null)
        {
            if (items == null)
                throw new ArgumentNullException(nameof(items));

            //prepare available activity log types
            var availableActivityTypes = await _customerActivityService.GetAllActivityTypesAsync();
            var availablesActivityTypes = await GetAllActivityTypesAsync();
            foreach (var activityType in availableActivityTypes)
            {
                if (availablesActivityTypes.Any(x => x.SystemKeyword == activityType.SystemKeyword))
                {
                    items.Add(new SelectListItem { Value = activityType.Id.ToString(), Text = activityType.Name });
                }
            }

            //insert special item for the default value
            await PrepareDefaultItemAsync(items, withSpecialDefaultItem, defaultItemText);
        }


        public virtual async Task<IList<ActivityLogType>> GetAllActivityTypesAsync()
        {
            var activityTypes = GetAllActivityTypes();
            var activityLogTypes = await _activityLogTypeRepository.GetAllAsync(query =>
            {
                return from alt in query
                       orderby alt.Name
                       select alt;
            }, cache => default);

            // var types = activityLogTypes.Where(x => activityTypes.Any(a => a.SystemKeyword == x.SystemKeyword)).ToList();
            var types = activityLogTypes.Where(x => x.Enabled = true).ToList();
            return types;
        }

        public List<ActivityLogType> GetAllActivityTypes()
        {
            var activityLogTypes = new List<ActivityLogType>
            {
                //subhlagan admin area activities
                new ActivityLogType
                {
                     SystemKeyword = "PublicStore.Login",
                     Enabled = true,
                     Name = "Login"
                 },
                 new ActivityLogType
                 {
                     SystemKeyword = "PublicStore.Logout",
                     Enabled = true,
                     Name = "Logout"
                 },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewPackage",
                    Enabled = true,
                    Name = "Add a new Package"
                },

                new ActivityLogType
                {
                    SystemKeyword = "EditPackage",
                    Enabled = true,
                    Name = "Edit an Package"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeletePackage",
                    Enabled = true,
                    Name = "Delete an Package"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewCommunity",
                    Enabled = true,
                    Name = "Add a new Community"
                },

                new ActivityLogType
                {
                    SystemKeyword = "EditCommunity",
                    Enabled = true,
                    Name = "Edit a Community"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteCommunity",
                    Enabled = true,
                    Name = "Delete a Community"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewOccupation",
                    Enabled = true,
                    Name = "Add a new Occupation"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditOccupation",
                    Enabled = true,
                    Name = "Edit an Occupation"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteOccupation",
                    Enabled = true,
                    Name = "Delete an Occupation"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileCategory",
                    Enabled = true,
                    Name = "Add a new Profile Category"
                },

                new ActivityLogType
                {
                    SystemKeyword = "EditProfileCategory",
                    Enabled = true,
                    Name = "Edit a Profile Category"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileCategory",
                    Enabled = true,
                    Name = "Delete a Profile Category"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewHobby",
                    Enabled = true,
                    Name = "Add a new Hobby"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditHobby",
                    Enabled = true,
                    Name = "Edit a Hobby"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteHobby",
                    Enabled = true,
                    Name = "Delete a Hobby"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewActionStatus",
                    Enabled = true,
                    Name = "Add a new Action Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditActionStatus",
                    Enabled = true,
                    Name = "Edit an Action Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteActionStatus",
                    Enabled = true,
                    Name = "Delete an Action Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewGotra",
                    Enabled = true,
                    Name = "Add a new Gotra"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditGotra",
                    Enabled = true,
                    Name = "Edit a Gotra"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteGotra",
                    Enabled = true,
                    Name = "Delete a Gotra"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewRemark",
                    Enabled = true,
                    Name = "Add a new Remark"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditRemark",
                    Enabled = true,
                    Name = "Edit a Remark"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteRemark",
                    Enabled = true,
                    Name = "Delete a Remark"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewCity",
                    Enabled = true,
                    Name = "Add a new City"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditCity",
                    Enabled = true,
                    Name = "Edit a City"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteCity",
                    Enabled = true,
                    Name = "Delete a City"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewAstroCity",
                    Enabled = true,
                    Name = "Add a new Astro City"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditAstroCity",
                    Enabled = true,
                    Name = "Edit an Astro City"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteAstroCity",
                    Enabled = true,
                    Name = "Delete an Astro City"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewOffice",
                    Enabled = true,
                    Name = "Add a new Office"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditOffice",
                    Enabled = true,
                    Name = "Edit an Office"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteOffice",
                    Enabled = true,
                    Name = "Delete an Office"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewCard",
                    Enabled = true,
                    Name = "Add a new Card"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditCard",
                    Enabled = true,
                    Name = "Edit a Card"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteCard",
                    Enabled = true,
                    Name = "Delete a Card"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileAttributeGroup",
                    Enabled = true,
                    Name = "Add a new Profile Attribute Group"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfileAttributeGroup",
                    Enabled = true,
                    Name = "Edit a Profile Attribute Group"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileAttributeGroup",
                    Enabled = true,
                    Name = "Delete a Profile Attribute Group"
                },
                 new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileSpecificationAttribute",
                    Enabled = true,
                    Name = "Add a new Profile Specification Attribute"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfileSpecificationAttribute",
                    Enabled = true,
                    Name = "Edit a Profile Specification Attribute"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileSpecificationAttribute",
                    Enabled = true,
                    Name = "Delete a Profile Specification Attribute"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewOrderPaymentHistory",
                    Enabled = true,
                    Name = "Add a new Order Payment History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditOrderPaymentHistory",
                    Enabled = true,
                    Name = "Edit an Order Payment History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteOrderPaymentHistory",
                    Enabled = true,
                    Name = "Delete an Order Payment History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewFeedback",
                    Enabled = true,
                    Name = "Add a new Feedback"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditFeedback",
                    Enabled = true,
                    Name = "Edit a Feedback"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteFeedback",
                    Enabled = true,
                    Name = "Delete a Feedback"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewContactExchange",
                    Enabled = true,
                    Name = "Add a new Contact Exchange"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditContactExchange",
                    Enabled = true,
                    Name = "Edit a Contact Exchange"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteContactExchange",
                    Enabled = true,
                    Name = "Delete a Contact Exchange"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileMatchStatus",
                    Enabled = true,
                    Name = "Add a new Profile Match Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfileMatchStatus",
                    Enabled = true,
                    Name = "Edit a Profile Match Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileMatchStatus",
                    Enabled = true,
                    Name = "Delete a Profile Match Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewMeeting",
                    Enabled = true,
                    Name = "Add a new Meeting"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditMeeting",
                    Enabled = true,
                    Name = "Edit a Meeting"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteMeeting",
                    Enabled = true,
                    Name = "Delete a Meeting"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewMeetingStatus",
                    Enabled = true,
                    Name = "Add a new Meeting Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditMeetingStatus",
                    Enabled = true,
                    Name = "Edit a Meeting Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteMeetingStatus",
                    Enabled = true,
                    Name = "Delete a Meeting Status"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewMarriageConfirmation",
                    Enabled = true,
                    Name = "Add a new Marriage Confirmation"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditMarriageConfirmation",
                    Enabled = true,
                    Name = "Edit a Marriage Confirmation"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteMarriageConfirmation",
                    Enabled = true,
                    Name = "Delete a Marriage Confirmation"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileStatusHistory",
                    Enabled = true,
                    Name = "Add a new Profile Status History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfileStatusHistory",
                    Enabled = true,
                    Name = "Edit a Profile Status History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileStatusHistory",
                    Enabled = true,
                    Name = "Delete a Profile Status History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfilePauseRecord",
                    Enabled = true,
                    Name = "Add a new Profile Pause Record"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfilePauseRecord",
                    Enabled = true,
                    Name = "Edit a Profile Pause Record"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfilePauseRecord",
                    Enabled = true,
                    Name = "Delete a Profile Pause Record"
                },
                 new ActivityLogType
                {
                    SystemKeyword = "AddNewFollowUp",
                    Enabled = true,
                    Name = "Add a new Follow Up"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditFollowUp",
                    Enabled = true,
                    Name = "Edit a Follow Up"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteFollowUp",
                    Enabled = true,
                    Name = "Delete a Follow Up"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewReason",
                    Enabled = true,
                    Name = "Add a new Reason"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditReason",
                    Enabled = true,
                    Name = "Edit Reason"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteReason",
                    Enabled = true,
                    Name = "Delete Reason"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewEducation",
                    Enabled = true,
                    Name = "Add a new Education"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditEducation",
                    Enabled = true,
                    Name = "Edit an Education"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteEducation",
                    Enabled = true,
                    Name = "Delete an Education"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewEducationArea",
                    Enabled = true,
                    Name = "Add a new Education Area"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditEducationArea",
                    Enabled = true,
                    Name = "Edit an Education Area"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteEducationArea",
                    Enabled = true,
                    Name = "Delete an Education Area"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewEducationLevel",
                    Enabled = true,
                    Name = "Add a new Education Level"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditEducationLevel",
                    Enabled = true,
                    Name = "Edit an Education Level"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteEducationLevel",
                    Enabled = true,
                    Name = "Delete an Education Level"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewQualification",
                    Enabled = true,
                    Name = "Add a new Qualification"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditQualification",
                    Enabled = true,
                    Name = "Edit a Qualification"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteQualification",
                    Enabled = true,
                    Name = "Delete a Qualification"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewFamilyMember",
                    Enabled = true,
                    Name = "Add a new Family Member"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditFamilyMember",
                    Enabled = true,
                    Name = "Edit a Family Member"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteFamilyMember",
                    Enabled = true,
                    Name = "Delete a Family Member"
                },
                new ActivityLogType
                {
                    SystemKeyword = "ViewFamilyMemberDetails",
                    Enabled = true,
                    Name = "View Family Member Details"
                },

                new ActivityLogType
                {
                    SystemKeyword = "AddNewOccupationAndBusiness",
                    Enabled = true,
                    Name = "Add a new Occupation and Business"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditOccupationAndBusiness",
                    Enabled = true,
                    Name = "Edit an Occupation and Business"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteOccupationAndBusiness",
                    Enabled = true,
                    Name = "Delete an Occupation and Business"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewMedicalHistory",
                    Enabled = true,
                    Name = "Add a new Medical History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditMedicalHistory",
                    Enabled = true,
                    Name = "Edit a Medical History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteMedicalHistory",
                    Enabled = true,
                    Name = "Delete a Medical History"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileContact",
                    Enabled = true,
                    Name = "Add a new Profile Contact"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfileContact",
                    Enabled = true,
                    Name = "Edit a Profile Contact"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileContact",
                    Enabled = true,
                    Name = "Delete a Profile Contact"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileTransfer",
                    Enabled = true,
                    Name = "Add a new Profile Transfer"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfileTransfer",
                    Enabled = true,
                    Name = "Edit a Profile Transfer"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileTransfer",
                    Enabled = true,
                    Name = "Delete a Profile Transfer"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewUserEmailAccount",
                    Enabled = true,
                    Name = "Add a new User Email Account"
                },

                new ActivityLogType
                {
                    SystemKeyword = "EditUserEmailAccount",
                    Enabled = true,
                    Name = "Edit a User Email Account"
                },

                new ActivityLogType
                {
                    SystemKeyword = "DeleteUserEmailAccount",
                    Enabled = true,
                    Name = "Delete a User Email Account"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewMatchKundali",
                    Enabled = true,
                    Name = "Add a new Match Kundali"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditMatchKundali",
                    Enabled = true,
                    Name = "Edit a Match Kundali"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteMatchKundali",
                    Enabled = true,
                    Name = "Delete a Match Kundali"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewPartnerPreference",
                    Enabled = true,
                    Name = "Add a new Partner Preference"
                },

                new ActivityLogType
                {
                    SystemKeyword = "EditPartnerPreference",
                    Enabled = true,
                    Name = "Edit a Partner Preference"
                },

                new ActivityLogType
                {
                    SystemKeyword = "DeletePartnerPreference",
                    Enabled = true,
                    Name = "Delete a Partner Preference"
                },

                new ActivityLogType
                {
                    SystemKeyword = "ChangeCustomerPassword",
                    Enabled = true,
                    Name = "Change password of a customer"
                },

                new ActivityLogType
                {
                    SystemKeyword = "GenerateCustomerBioData",
                    Enabled = true,
                    Name = "Generate biodata of a customer"
                },

                 new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileEducation",
                    Enabled = true,
                    Name = "Add education for a profile"
                },
                 new ActivityLogType
                {
                    SystemKeyword = "EditProfileEducation",
                    Enabled = true,
                    Name = "Edit education for a profile"
                },
                 new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileEducation",
                    Enabled = true,
                    Name = "Delete education for a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileFamilyMember",
                    Enabled = true,
                    Name = "Add new family member for a profile"
                },
                 new ActivityLogType
                {
                    SystemKeyword = "EditProfileFamilyMember",
                    Enabled = true,
                    Name = "Edit family member for a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileFamilyMember",
                    Enabled = true,
                    Name = "Delete family member for a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileContact",
                    Enabled = true,
                    Name = "Add new contact for a profile"
                },
                 new ActivityLogType
                {
                    SystemKeyword = "EditProfileContact",
                    Enabled = true,
                    Name = "Edit contact for a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfileContact",
                    Enabled = true,
                    Name = "Delete contact for a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "SendProfileLoginDetails",
                    Enabled = true,
                    Name = "Send login details to a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "SendProfileRegistrationForm",
                    Enabled = true,
                    Name = "Send registration from to a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "SendMatchProfilesByEmail",
                    Enabled = true,
                    Name = "Send matched profiles by email"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewShortList",
                    Enabled = true,
                    Name = "Short Listed profiles"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditShortList",
                    Enabled = true,
                    Name = "Edit Short Listed profiles"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteShortList",
                    Enabled = true,
                    Name = "Delete Short Listed profiles"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewClientMeeting",
                    Enabled = true,
                    Name = "Add a new client meeting"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditClientMeeting",
                    Enabled = true,
                    Name = "Edit client meeting"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteClientMeeting",
                    Enabled = true,
                    Name = "Delete client meeting"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfilePackage",
                    Enabled = true,
                    Name = "Add a new profile package"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfilePackage",
                    Enabled = true,
                    Name = "Edit profile package"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfilePackage",
                    Enabled = true,
                    Name = "Delete profile package"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfilePicture",
                    Enabled = true,
                    Name = "Add a new Profile Picture"
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditProfilePicture",
                    Enabled = true,
                    Name = "Edit a Profile Picture"
                },
                new ActivityLogType
                {
                    SystemKeyword = "DeleteProfilePicture",
                    Enabled = true,
                    Name = "Delete a Profile Picture"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewProfileAuthorization",
                    Enabled = true,
                    Name = "Add a new Authorization for profile."
                },
                new ActivityLogType
                {
                    SystemKeyword = "EditedProfileAuthorization",
                    Enabled = true,
                    Name = "Add a new Authorization for profile."
                },
                new ActivityLogType
                {
                    SystemKeyword = "SendMatchKundaliDetailsByEmail",
                    Enabled = true,
                    Name = "Send match kundali details to profile by email"
                },
                new ActivityLogType
                {
                    SystemKeyword = "SendEmail",
                    Enabled = true,
                    Name = "Send email"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddProfileToMovedMember",
                    Enabled = true,
                    Name = "Add profile in moved member list"
                },
                new ActivityLogType
                {
                    SystemKeyword = "ProfileReActivate",
                    Enabled = true,
                    Name = "Reactivate a profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "ViewProfileBioData",
                    Enabled = true,
                    Name = "Viewed biodata of profile"
                },
                new ActivityLogType
                {
                    SystemKeyword = "ProfileExchange",
                    Enabled = true,
                    Name = "Profile Exchange"
                },
                
                // Mobile Number Security Activities
                new ActivityLogType
                {
                    SystemKeyword = "MobileNumber.Viewed",
                    Enabled = true,
                    Name = "Mobile number viewed (unmasked)"
                },
                new ActivityLogType
                {
                    SystemKeyword = "MobileNumber.Changed",
                    Enabled = true,
                    Name = "Mobile number changed"
                },
                new ActivityLogType
                {
                    SystemKeyword = "MobileNumber.Encrypted",
                    Enabled = true,
                    Name = "Mobile number encrypted"
                },
                new ActivityLogType
                {
                    SystemKeyword = "MobileNumber.Decrypted",
                    Enabled = true,
                    Name = "Mobile number decrypted"
                },
                
                // WhatsApp Verification Activities
                new ActivityLogType
                {
                    SystemKeyword = "WhatsApp.OtpSent",
                    Enabled = true,
                    Name = "WhatsApp OTP sent"
                },
                new ActivityLogType
                {
                    SystemKeyword = "WhatsApp.OtpVerified",
                    Enabled = true,
                    Name = "WhatsApp number verified successfully"
                },
                new ActivityLogType
                {
                    SystemKeyword = "WhatsApp.VerificationFailed",
                    Enabled = true,
                    Name = "WhatsApp verification failed"
                },
                new ActivityLogType
                {
                    SystemKeyword = "WhatsApp.VerificationReset",
                    Enabled = true,
                    Name = "WhatsApp verification status reset"
                },
                new ActivityLogType
                {
                    SystemKeyword = "WhatsApp.RateLimitExceeded",
                    Enabled = true,
                    Name = "WhatsApp OTP rate limit exceeded"
                },
                
                // Security Activities
                new ActivityLogType
                {
                    SystemKeyword = "Security.PasswordVerificationFailed",
                    Enabled = true,
                    Name = "Password verification failed for mobile number access"
                },
                new ActivityLogType
                {
                    SystemKeyword = "Security.UnauthorizedMobileAccess",
                    Enabled = true,
                    Name = "Unauthorized attempt to access mobile number"
                },
                new ActivityLogType
                {
                    SystemKeyword = "AddNewFeedbackForm",
                    Enabled = true,
                    Name = "Add a new Feedback Form"
                },

                new ActivityLogType
                {
                    SystemKeyword = "EditFeedbackForm",
                    Enabled = true,
                    Name = "Edit a Feedback Form"
                },

                new ActivityLogType
                {
                    SystemKeyword = "DeleteFeedbackForm",
                    Enabled = true,
                    Name = "Delete a Feedback Form"
                },

            };


            return activityLogTypes;
        }

        public async Task AddOrUpdateAsync()
        {
            var activityLogTypes = GetAllActivityTypes();

            await AddOrUpdateActivityLogTypesAsync(activityLogTypes);
        }

        private async Task AddOrUpdateActivityLogTypesAsync(IList<ActivityLogType> incomingActivityLogTypes)
        {
            var existingActivityLogTypes = (await _activityLogTypeRepository.GetAllAsync(query => { return query; }, null)).ToList();

            foreach (var incomingActivityLogType in incomingActivityLogTypes)
            {
                var existingActivityLogType = existingActivityLogTypes
                    .SingleOrDefault(x => x.SystemKeyword == incomingActivityLogType.SystemKeyword);

                if (existingActivityLogType == null)
                {
                    await InsertActivityLogTypeAsync(incomingActivityLogType);
                }
                else
                {
                    await UpdateActivityLogTypeAsync(existingActivityLogType, incomingActivityLogType);
                }
            }
        }

        private async Task InsertActivityLogTypeAsync(ActivityLogType activityLogType)
        {
            await _activityLogTypeRepository.InsertAsync(activityLogType);
        }

        private async Task UpdateActivityLogTypeAsync(ActivityLogType existingActivityLogType, ActivityLogType incomingActivityLogType)
        {
            // You may want to update only specific properties here, not the entire object.
            existingActivityLogType.Name = incomingActivityLogType.Name;
            existingActivityLogType.Enabled = incomingActivityLogType.Enabled;

            await _activityLogTypeRepository.UpdateAsync(existingActivityLogType);
        }

        protected virtual async Task PrepareDefaultItemAsync(IList<SelectListItem> items, bool withSpecialDefaultItem, string defaultItemText = null, string defaultItemValue = "0")
        {
            if (items == null)
                throw new ArgumentNullException(nameof(items));

            //whether to insert the first special item for the default value
            if (!withSpecialDefaultItem)
                return;

            //prepare item text
            defaultItemText ??= await _localizationService.GetResourceAsync("Admin.Common.All");

            //insert this default item at first
            items.Insert(0, new SelectListItem { Text = defaultItemText, Value = defaultItemValue });
        }



    }
}
