﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Text.RegularExpressions;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace Subhlagan.Application.Pdf
{
    public static class HtmlRenderer
    {
        /// <summary>
        /// Composes HTML content into QuestPDF elements.
        /// </summary>
        public static void ComposeHtmlContent(IContainer container, string htmlContent)
        {
            // Decode HTML entities
            var decodedHtml = WebUtility.HtmlDecode(htmlContent);

            // Remove outer <div> tags if present.
            decodedHtml = Regex.Replace(decodedHtml, @"</?div[^>]*>", "", RegexOptions.IgnoreCase);

            // Use regex to split into paragraphs.
            var paragraphMatches = Regex.Matches(decodedHtml, @"<p[^>]*>(.*?)</p>", RegexOptions.IgnoreCase | RegexOptions.Singleline);

            if (paragraphMatches.Count > 0)
            {
                container.Column(column =>
                {
                    foreach (Match pMatch in paragraphMatches)
                    {
                        var paragraphContent = pMatch.Groups[1].Value;

                        // Split paragraph content on <br> tags.
                        var lines = Regex.Split(paragraphContent, @"<br\s*/?>", RegexOptions.IgnoreCase);

                        foreach (var line in lines)
                        {
                            // For each line, create a text element that processes inline HTML.
                            column.Item().Text(t =>
                            {
                                ProcessInlineHtml(t, line);
                            });
                        }
                        // Add spacing after each paragraph.
                        column.Item().PaddingBottom(5);
                    }
                });
            }
            else
            {
                // If no <p> tags found, render the entire content as a single block.
                container.Text(t =>
                {
                    ProcessInlineHtml(t, decodedHtml);
                });
            }
        }

        /// <summary>
        /// Represents the current inline formatting state.
        /// </summary>
        class FormatState
        {
            public bool Bold { get; set; }
            public bool Underline { get; set; }
            public bool Italic { get; set; }
            public bool IsLink { get; set; }

            public FormatState Clone() => new FormatState
            {
                Bold = this.Bold,
                Underline = this.Underline,
                Italic = this.Italic,
                IsLink = this.IsLink
            };
        }

        /// <summary>
        /// Processes inline HTML tags within a text block.
        /// Supported tags: <strong>, <u>, <em>, and <a>.
        /// </summary>
        private static void ProcessInlineHtml(TextDescriptor text, string html)
        {
            // Pattern: either capture an HTML tag or text.
            var pattern = @"(?<tag><\/?(?<tagName>strong|u|em|a)[^>]*>)|(?<text>[^<]+)";
            var matches = Regex.Matches(html, pattern, RegexOptions.IgnoreCase);

            // Use a stack to track nested formatting states.
            var formatStack = new Stack<FormatState>();
            formatStack.Push(new FormatState()); // initial (default) state

            foreach (Match match in matches)
            {
                if (match.Groups["text"].Success)
                {
                    // Get current formatting state.
                    var currentState = formatStack.Peek();
                    var segment = match.Groups["text"].Value;
                    var span = text.Span(segment);

                    if (currentState.Bold)
                        span.Bold();
                    if (currentState.Underline)
                        span.Underline();
                    if (currentState.Italic)
                        span.Italic();
                    if (currentState.IsLink)
                        span.FontColor(Colors.Blue.Medium);
                }
                else if (match.Groups["tag"].Success)
                {
                    var tag = match.Groups["tag"].Value.ToLower();
                    var tagName = match.Groups["tagName"].Value.ToLower();
                    bool isClosing = tag.StartsWith("</");

                    if (!isClosing)
                    {
                        // Opening tag: push a new state cloned from the current state.
                        var newState = formatStack.Peek().Clone();

                        // Update state based on tag type.
                        switch (tagName)
                        {
                            case "strong":
                                newState.Bold = true;
                                break;
                            case "u":
                                newState.Underline = true;
                                break;
                            case "em":
                                newState.Italic = true;
                                break;
                            case "a":
                                newState.IsLink = true;
                                break;
                        }
                        formatStack.Push(newState);
                    }
                    else
                    {
                        // Closing tag: pop the state if the tag matches.
                        if (formatStack.Count > 1)
                            formatStack.Pop();
                    }
                }
            }
        }
    }

}
