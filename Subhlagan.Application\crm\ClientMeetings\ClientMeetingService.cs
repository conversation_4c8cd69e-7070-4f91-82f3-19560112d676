﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Profiles;

namespace Subhlagan.Application.Meetings
{
    public partial class ClientMeetingService : IClientMeetingService
    {
        #region Fields

        private readonly EnhancedEntityRepository<ClientMeeting> _clientMeetingRepository;
        private readonly EnhancedEntityRepository<Profile> _profileRepository;
        private readonly IProfileMatchService _profileMatchService;


        #endregion

        #region Ctor

        public ClientMeetingService(EnhancedEntityRepository<ClientMeeting> clinetMeetingRepository,
            EnhancedEntityRepository<Profile> profileRepository,
            IProfileMatchService profileMatchService)
        {
            _clientMeetingRepository = clinetMeetingRepository;
            _profileRepository = profileRepository;
            _profileMatchService = profileMatchService;
        }

        #endregion

        #region Methods

        public virtual async Task InsertClientMeetingAsync(ClientMeeting clientMeeting)
        {
            if (clientMeeting == null)
                throw new ArgumentNullException(nameof(clientMeeting));

            await _clientMeetingRepository.InsertAsync(clientMeeting);
        }

        public virtual async Task UpdateClientMeetingAsync(ClientMeeting clientMeeting)
        {
            if (clientMeeting == null)
                throw new ArgumentNullException(nameof(clientMeeting));

            //meeting.Venue = CommonHelper.EnsureNotNull(meeting.Venue);
            //meeting.Venue = meeting.Venue.Trim();
            //meeting.Venue = CommonHelper.EnsureMaximumLength(meeting.Venue, 255);

            await _clientMeetingRepository.UpdateAsync(clientMeeting);
        }

        public virtual async Task DeleteClientMeetingAsync(ClientMeeting clientMeeting)
        {
            if (clientMeeting == null)
                throw new ArgumentNullException(nameof(clientMeeting));

            await _clientMeetingRepository.DeleteAsync(clientMeeting);
        }

        public virtual async Task<ClientMeeting> GetClientMeetingByIdAsync(int clientMeetingId)
        {
            return await _clientMeetingRepository.GetByIdAsync(clientMeetingId, cache => default);
        }

        /// <summary>
        /// Get all meetings with optional filters
        /// </summary>
        /// <param name="meetingDateFrom">Filter by meeting start date (inclusive); pass null to ignore</param>
        /// <param name="meetingDateTo">Filter by meeting end date (inclusive); pass null to ignore</param>
        /// <param name="maleProfileId">Filter by male profile ID; pass 0 to ignore</param>
        /// <param name="femaleProfileId">Filter by female profile ID; pass 0 to ignore</param>
        /// <param name="userId">Filter by user ID; pass 0 to ignore</param>
        /// <param name="pageIndex">Page index for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <param name="getOnlyTotalCount">Indicates whether only the total count of records is needed</param>
        /// <returns>
        /// A task that represents the asynchronous operation
        /// The task result contains the paginated list of meetings
        /// </returns>
        public virtual async Task<IPagedList<ClientMeeting>> GetAllClientMeetingsAsync(
            DateTime? meetingDateFrom = null,
            DateTime? meetingDateTo = null,
            int profileId = 0,
            IList<int> userIds = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            string Name = null, IList<int> profileIds = null, IList<int> assignedUserIds = null,
            IList<int> meetingIds = null, List<int> matchResultIds = null, int venueId =0)
        {
            var query = _clientMeetingRepository.Table;

            // Filter by meeting start date
            if (meetingDateFrom.HasValue)
                query = query.Where(m => m.ScheduledDateTime >= meetingDateFrom.Value);

            // Filter by meeting end date
            if (meetingDateTo.HasValue)
                query = query.Where(m => m.ScheduledDateTime <= meetingDateTo.Value);

            // Filter by profile ID
            if (profileId > 0)
                query = query.Where(m => m.ProfileId == profileId);

            // Filter by user ID
            if (userIds?.Any() ?? false)
                query = query.Where(m => userIds.Contains(m.UserId));

            if (matchResultIds?.Any() ?? false)
            {
                query = from pm in query
                        join initiator in _profileRepository.Table on pm.ProfileId equals initiator.Id
                        where matchResultIds.Contains(initiator.MatchResultId)
                        select pm;
            }

            // filter by Profile Id
            if (profileIds !=null && profileIds.Any())
                query = query.Where(m => profileIds.Contains(m.ProfileId));           

            if (meetingIds != null)
            {
                query = query.Where(m => meetingIds.Contains(m.Id));
            }

            if (assignedUserIds != null && assignedUserIds.Any())
            {
                var relevantProfileIds = _profileRepository.Table
                    .Where(p => userIds.Contains(p.RelationshipManagerUserId)
                             || userIds.Contains(p.AssistantRelationshipManagerUserId))
                    .Select(p => p.Id);

                query = query.Where(m => relevantProfileIds.Contains(m.ProfileId));

            }

            if (venueId>0)
            {
                query = query.Where(m => m.VenueId == venueId);
            }

            // Order by creation date
            query = query.OrderByDescending(m => m.ScheduledDateTime).ThenBy(m => m.CreatedOnUtc);

            // Return paginated result
            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);
        }

        #endregion
    }
}
