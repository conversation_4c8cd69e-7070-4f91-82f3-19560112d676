-- =====================================================================
-- Comprehensive Database Analysis Summary for SubhLaganNew_v1
-- Purpose: Generate a complete report of potentially unused tables
-- =====================================================================

USE SubhLaganNew_v1;

PRINT '========================================';
PRINT 'DATABASE ANALYSIS SUMMARY REPORT';
PRINT 'Database: SubhLaganNew_v1';
PRINT 'Analysis Date: ' + CONVERT(varchar, GETDATE(), 120);
PRINT '========================================';
PRINT '';

-- Summary Statistics
DECLARE @TotalTables INT;
DECLARE @TablesWithData INT;
DECLARE @EmptyTables INT;

SELECT @TotalTables = COUNT(*)
FROM sys.tables 
WHERE is_ms_shipped = 0;

SELECT @TablesWithData = COUNT(*)
FROM sys.tables t
INNER JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 AND p.rows > 0;

SET @EmptyTables = @TotalTables - @TablesWithData;

PRINT 'SUMMARY STATISTICS:';
PRINT 'Total User Tables: ' + CAST(@TotalTables AS varchar);
PRINT 'Tables with Data: ' + CAST(@TablesWithData AS varchar);
PRINT 'Empty Tables: ' + CAST(@EmptyTables AS varchar);
PRINT '';

-- Get potentially unused tables based on multiple criteria
WITH TableAnalysis AS (
    SELECT 
        SCHEMA_NAME(t.schema_id) AS SchemaName,
        t.name AS TableName,
        t.create_date AS CreatedDate,
        ISNULL(p.rows, 0) AS RowCount,
        CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS TotalSizeMB,
        
        -- Check if table has foreign keys
        CASE WHEN fk_parent.parent_count > 0 OR fk_ref.ref_count > 0 THEN 1 ELSE 0 END AS HasForeignKeys,
        
        -- Check if table is referenced by database objects
        CASE WHEN obj_ref.ref_count > 0 THEN 1 ELSE 0 END AS IsReferencedByObjects,
        
        -- Check if table has usage statistics
        CASE WHEN usage_stats.total_activity > 0 THEN 1 ELSE 0 END AS HasUsageStats,
        
        ISNULL(usage_stats.total_activity, 0) AS TotalActivity,
        usage_stats.last_activity AS LastActivity
        
    FROM sys.tables t
    INNER JOIN sys.indexes i ON t.object_id = i.object_id
    INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
    INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
    
    -- Check for foreign key relationships
    LEFT JOIN (
        SELECT parent_object_id, COUNT(*) as parent_count
        FROM sys.foreign_keys 
        GROUP BY parent_object_id
    ) fk_parent ON t.object_id = fk_parent.parent_object_id
    
    LEFT JOIN (
        SELECT referenced_object_id, COUNT(*) as ref_count
        FROM sys.foreign_keys 
        GROUP BY referenced_object_id
    ) fk_ref ON t.object_id = fk_ref.referenced_object_id
    
    -- Check for object references
    LEFT JOIN (
        SELECT 
            t2.object_id,
            COUNT(DISTINCT d.referencing_id) as ref_count
        FROM sys.tables t2
        LEFT JOIN sys.sql_expression_dependencies d ON t2.name = d.referenced_entity_name
        GROUP BY t2.object_id
    ) obj_ref ON t.object_id = obj_ref.object_id
    
    -- Check for usage statistics
    LEFT JOIN (
        SELECT 
            us.object_id,
            SUM(ISNULL(us.user_seeks, 0) + ISNULL(us.user_scans, 0) + 
                ISNULL(us.user_lookups, 0) + ISNULL(us.user_updates, 0)) AS total_activity,
            MAX(CASE 
                WHEN us.last_user_seek > us.last_user_scan 
                     AND us.last_user_seek > us.last_user_lookup 
                     AND us.last_user_seek > us.last_user_update
                THEN us.last_user_seek
                WHEN us.last_user_scan > us.last_user_lookup 
                     AND us.last_user_scan > us.last_user_update
                THEN us.last_user_scan
                WHEN us.last_user_lookup > us.last_user_update
                THEN us.last_user_lookup
                ELSE us.last_user_update
            END) AS last_activity
        FROM sys.dm_db_index_usage_stats us
        WHERE us.database_id = DB_ID()
        GROUP BY us.object_id
    ) usage_stats ON t.object_id = usage_stats.object_id
    
    WHERE t.is_ms_shipped = 0
    GROUP BY 
        t.schema_id, t.name, t.object_id, t.create_date, p.rows,
        fk_parent.parent_count, fk_ref.ref_count, obj_ref.ref_count,
        usage_stats.total_activity, usage_stats.last_activity
)
SELECT 
    TableName,
    SchemaName,
    CreatedDate,
    RowCount,
    TotalSizeMB,
    HasForeignKeys,
    IsReferencedByObjects,
    HasUsageStats,
    TotalActivity,
    LastActivity,
    -- Calculate unused confidence score (0-100, higher = more likely unused)
    CASE 
        WHEN RowCount = 0 AND HasForeignKeys = 0 AND IsReferencedByObjects = 0 AND HasUsageStats = 0 THEN 95
        WHEN RowCount = 0 AND HasForeignKeys = 0 AND IsReferencedByObjects = 0 THEN 85
        WHEN RowCount = 0 AND HasForeignKeys = 0 THEN 75
        WHEN RowCount = 0 THEN 60
        WHEN HasForeignKeys = 0 AND IsReferencedByObjects = 0 AND HasUsageStats = 0 THEN 70
        WHEN HasForeignKeys = 0 AND IsReferencedByObjects = 0 THEN 50
        WHEN HasUsageStats = 0 AND IsReferencedByObjects = 0 THEN 40
        ELSE 10
    END AS UnusedConfidenceScore,
    
    -- Provide recommendation
    CASE 
        WHEN RowCount = 0 AND HasForeignKeys = 0 AND IsReferencedByObjects = 0 AND HasUsageStats = 0 
        THEN 'SAFE TO DELETE - No data, references, or usage'
        WHEN RowCount = 0 AND HasForeignKeys = 0 AND IsReferencedByObjects = 0 
        THEN 'LIKELY SAFE TO DELETE - No data or references'
        WHEN RowCount = 0 AND HasForeignKeys = 0 
        THEN 'REVIEW REQUIRED - Empty but may be referenced'
        WHEN RowCount = 0 
        THEN 'CAUTION - Empty but has relationships'
        WHEN HasForeignKeys = 0 AND IsReferencedByObjects = 0 AND HasUsageStats = 0 
        THEN 'REVIEW REQUIRED - Has data but no references'
        ELSE 'KEEP - Active table'
    END AS Recommendation
    
FROM TableAnalysis
ORDER BY UnusedConfidenceScore DESC, RowCount ASC, TableName;

PRINT '';
PRINT 'Analysis complete. Review the results above to identify unused tables.';
PRINT 'Tables with UnusedConfidenceScore >= 85 are the safest candidates for deletion.';
PRINT '';