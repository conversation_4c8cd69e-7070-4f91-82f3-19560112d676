﻿using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;
using Subhlagan.Application.Common;
using Subhlagan.Core;
using System.Threading.Tasks;

namespace Subhlagan.Application.PartnerPreferences.Caching
{
    public partial class PartnerPreferenceCacheEventConsumer : CacheEventConsumer<PartnerPreference>
    {
        protected override async Task ClearCacheAsync(PartnerPreference entity, EntityEventType entityEventType)
        {
            await RemoveByPrefixAsync(PageBaaSDefaults.PartnerPreferenceByProfileIdPrefix, entity.ProfileId);
            await base.ClearCacheAsync(entity, entityEventType);

        }
    }
}
