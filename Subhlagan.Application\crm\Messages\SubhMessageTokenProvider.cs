﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;

using Subhlagan.Core.Domain.Customers;
using Subhlagan.Core.Domain.Directory;
using Subhlagan.Core.Domain.Media;
using Subhlagan.Core.Domain.Messages;



using Subhlagan.Core.Events;
using Subhlagan.Core.Domain;
using Subhlagan.Core.Domain.Enum;
using Subhlagan.Application.MatchKundalis;
using Subhlagan.Application.Profiles;
using Subhlagan.Application;
using Subhlagan.Application.Common;
using Subhlagan.Application.Configuration;
using Subhlagan.Application.Customers;
using Subhlagan.Application.Directory;
using Subhlagan.Application.Helpers;
using Subhlagan.Application.Html;
using Subhlagan.Application.Localization;
using Subhlagan.Application.Media;
using Subhlagan.Application.Messages;


using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.Routing;
using System.Text;


namespace Subhlagan.Application.Messages
{
    public class SubhMessageTokenProvider : MessageTokenProvider
    {
        #region Fields

        private readonly IActionContextAccessor _actionContextAccessor;
        private readonly IAddressService _addressService;
        private readonly ICountryService _countryService;
        private readonly ICustomerService _customerService;
        private readonly IDateTimeHelper _dateTimeHelper;
        private readonly IEventPublisher _eventPublisher;
        private readonly IGenericAttributeService _genericAttributeService;
        private readonly IHtmlFormatter _htmlFormatter;
        private readonly ILanguageService _languageService;
        private readonly ILocalizationService _localizationService;
        private readonly IStateProvinceService _stateProvinceService;

        
        private readonly IUrlHelperFactory _urlHelperFactory;

        private readonly IWorkContext _workContext;
        private readonly MessageTemplatesSettings _templatesSettings;
        private readonly IProfileService _profileService;
        private readonly MediaSettings _mediaSettings;
        private readonly ISettingService _settingService;
        private readonly IPictureService _pictureService;
        private readonly IMatchKundaliService _matchKundaliService;
        private readonly IWebHelper _webHelper;

        #endregion

        #region Ctor

        public SubhMessageTokenProvider(
            IActionContextAccessor actionContextAccessor,
            IAddressService addressService,
            ICountryService countryService,
            ICustomerService customerService,
            IDateTimeHelper dateTimeHelper,
            IEventPublisher eventPublisher,
            IGenericAttributeService genericAttributeService,
            IHtmlFormatter htmlFormatter,
            ILanguageService languageService,
            ILocalizationService localizationService,
            IStateProvinceService stateProvinceService,

            
            IUrlHelperFactory urlHelperFactory,

            IWorkContext workContext,
            MessageTemplatesSettings templatesSettings,
            IProfileService profileService,
            MediaSettings mediaSettings,
            ISettingService settingService,
            IPictureService pictureService,
            IMatchKundaliService matchKundaliService,
            IWebHelper webHelper)
            : base(actionContextAccessor, addressService, countryService, customerService, dateTimeHelper, eventPublisher, genericAttributeService, htmlFormatter, languageService, localizationService, stateProvinceService, urlHelperFactory, workContext, templatesSettings)
        {
            _actionContextAccessor = actionContextAccessor;
            _addressService = addressService;
            _countryService = countryService;
            _customerService = customerService;
            _dateTimeHelper = dateTimeHelper;
            _eventPublisher = eventPublisher;
            _genericAttributeService = genericAttributeService;
            _htmlFormatter = htmlFormatter;
            _languageService = languageService;
            _localizationService = localizationService;
            _stateProvinceService = stateProvinceService;

            
            _urlHelperFactory = urlHelperFactory;

            _workContext = workContext;
            _templatesSettings = templatesSettings;
            _profileService = profileService;
            _mediaSettings = mediaSettings;
            _settingService = settingService;
            _pictureService = pictureService;
            _matchKundaliService = matchKundaliService;
            _webHelper = webHelper;
        }

        #endregion

        private async Task<string> GenerateProfileListHtmlAsync(IList<Profile> profiles, int pictureSize)
        {
            var profileListHtml = new StringBuilder();
            profileListHtml.AppendLine("<table border=\"0\" style=\"width:100%;\">");

            // Add table header
            profileListHtml.AppendLine("<tr style=\"background-color:#f4f4f4;text-align:center;\">");
            profileListHtml.AppendLine("<th>Photo</th>");
            profileListHtml.AppendLine("<th>Details</th>");
            profileListHtml.AppendLine("<th>Actions</th>");
            profileListHtml.AppendLine("</tr>");

            foreach (var profile in profiles)
            {
                // Get profile details
                var profileName = await _profileService.GetProfileFullNameAsync(profile);
                var picture = (await _profileService.GetPicturesByProfileIdAsync(profile.Id, 1)).FirstOrDefault();

                var (imageUrl, _) = await _pictureService.GetPictureUrlAsync(picture, pictureSize);
                var dob = profile.Customer?.DateOfBirth.HasValue ?? false
                    ? $"{profile.Customer.DateOfBirth.Value:dd MMM yyyy} ({profile.Customer.DateOfBirth.CalculateAge()} yrs)"
                    : "Not provided";

                var caste = await _profileService.GetCasteAsync(profile);
                var height = profile.Height.GetDisplayName(true);

                // Add profile row
                profileListHtml.AppendLine("<tr style=\"background-color:#ffffff;text-align:center;\">");

                // Photo column
                profileListHtml.AppendLine($"<td style=\"padding:10px;\"><img src=\"{imageUrl}\" alt=\"Profile Photo\" style=\"max-width:100px;\"/></td>");

                // Details column
                profileListHtml.AppendLine("<td style=\"padding:10px;text-align:left;\">");
                profileListHtml.AppendLine($"<b>{profileName} [{profile.Id}]</b><br />");
                profileListHtml.AppendLine($"<span style=\"color:#555;\">DOB:</span> {dob}<br />");
                profileListHtml.AppendLine($"<span style=\"color:#555;\">Height:</span> {height}<br />");
                profileListHtml.AppendLine($"<span style=\"color:#555;\">Community:</span> {caste}<br />");
                profileListHtml.AppendLine("</td>");

                var viewPdfUrl = await RouteUrlAsync(routeName: "ProfileViewPdf", routeValues: new { token = _profileService.GenerateCustomerGuidToken(profile.Customer.CustomerGuid, TokenType.Encrypted), type = (int)TokenType.Encrypted });
                // Actions column
                profileListHtml.AppendLine("<td style=\"padding:10px;text-align:center;\">");
                profileListHtml.AppendLine($"<a href=\"{viewPdfUrl}\" style=\"color:#007bff;text-decoration:none;\">");
                profileListHtml.AppendLine($"<img src=\"{_webHelper.GetApplicationBaseUrl()}images/email/pdficon.jpg\" alt=\"Download Biodata\"/><br />");
                profileListHtml.AppendLine("Download Biodata</a>");
                profileListHtml.AppendLine("</td>");

                profileListHtml.AppendLine("</tr>");
            }

            // Close the table
            profileListHtml.AppendLine("</table>");

            return profileListHtml.ToString();
        }

        public override IEnumerable<string> GetTokenGroups(MessageTemplate messageTemplate)
        {
            // Determine token groups based on the message template name
            var groups = messageTemplate.Name switch
            {
                SubhMessageTemplateSystemNames.MatchProfilesMessage
                    => new[] { TokenGroupNames.RelationshipManagerTokens },
                SubhMessageTemplateSystemNames.MatchAcceptedMessage
                    => new[]
                    {
                TokenGroupNames.MatchAcceptedTokens,
                TokenGroupNames.RelationshipManagerTokens
                    },
                _ => Array.Empty<string>(),
            };

            // If no specific token groups, fallback to the base implementation
            return groups.Any() ? groups : base.GetTokenGroups(messageTemplate);
        }

        public virtual async Task AddCustomerTokensAsync(IList<Token> tokens, Customer customer, string password)
        {
            tokens.Add(new Token("Customer.Password", password));
            await base.AddCustomerTokensAsync(tokens, customer);
        }

        public virtual async Task AddMeetingTokensAsync(List<Token> tokens, Profile initiatorProfile, Profile matchedProfile, Meeting meeting)
        {
            tokens.Add(new Token("Meeting.InitiatorProfileFullName", await _customerService.GetCustomerFullNameAsync(initiatorProfile.Customer)));
            tokens.Add(new Token("Meeting.MatchedProfileFullName", await _customerService.GetCustomerFullNameAsync(matchedProfile.Customer)));
            tokens.Add(new Token("Meeting.Venue", meeting.Venue));
            tokens.Add(new Token("Meeting.ScheduledDateTime", (await _dateTimeHelper.ConvertToUserTimeAsync(meeting.ScheduledDateTime.Value, DateTimeKind.Utc)).ToString("f")));
            tokens.Add(new Token("Meeting.Description", meeting.Description));
            tokens.Add(new Token("Meeting.GroomSideRepresentative", meeting.GroomSideRepresentative));
            tokens.Add(new Token("Meeting.BrideSideRepresentative", meeting.BrideSideRepresentative));
            tokens.Add(new Token("Meeting.Notes", meeting.Notes));
            await Task.CompletedTask;
        }

        public virtual async Task AddProfileMessageTokensAsync(IList<Token> tokens, string body)
        {
            tokens.Add(new Token("Message.Body", body, true));
            await Task.CompletedTask;
        }

        public virtual async Task AddRelationshipManagerTokensAsync(IList<Token> tokens, User rm, User arm = null)
        {
            if (rm?.Customer == null)
                throw new ArgumentNullException(nameof(rm), "Relationship Manager is required and cannot be null.");

            // Add Relationship Manager tokens
            tokens.Add(new Token("RelationshipManager.Name", await _customerService.GetCustomerFullNameAsync(rm.Customer)));
            tokens.Add(new Token("RelationshipManager.Number", rm.Customer.Phone ?? string.Empty));

            // Add Assistant Relationship Manager tokens (conditionally)
            if (arm?.Customer != null)
            {
                tokens.Add(new Token("AssistantRelationshipManager.Name", await _customerService.GetCustomerFullNameAsync(arm.Customer)));
                tokens.Add(new Token("AssistantRelationshipManager.Number", arm.Customer.Phone ?? string.Empty));
            }
            else
            {
                // If ARM is not provided, add empty tokens to ensure consistency
                tokens.Add(new Token("AssistantRelationshipManager.Name", string.Empty));
                tokens.Add(new Token("AssistantRelationshipManager.Number", string.Empty));
            }

            await Task.CompletedTask;
        }


        public virtual async Task AddMatchKundaliTokensAsync(IList<Token> tokens, MatchKundali matchKundali)
        {
            var downloadPdf = await _matchKundaliService.GetDownloadPdfUrlAsync(matchKundali);
            tokens.Add(new Token("MatchKundali.MaleProfileFullName", await _customerService.GetCustomerFullNameAsync(matchKundali.MaleProfile.Customer)));
            tokens.Add(new Token("MatchKundali.FemaleProfileFullName", await _customerService.GetCustomerFullNameAsync(matchKundali.FemaleProfile.Customer)));
            tokens.Add(new Token("MatchKundali.TotalGun", matchKundali.TotalGun.ToString("F2")));
            tokens.Add(new Token("MatchKundali.PdfUrl", downloadPdf, true));
            tokens.Add(new Token("MatchKundali.MaleManglikStatus", matchKundali.MaleManglikStatus.GetDisplayName()));
            tokens.Add(new Token("MatchKundali.FemaleManglikStatus", matchKundali.FemaleManglikStatus.GetDisplayName()));
            await Task.CompletedTask;
        }

        public virtual async Task AddProfileActivationTokensAsync(IList<Token> tokens, User rm, DateTime activationDate)
        {
            tokens.Add(new Token("Profile.ActivationDate", activationDate.ToString("f")));
            tokens.Add(new Token("RelationshipManager.Name", await _customerService.GetCustomerFullNameAsync(rm.Customer)));

            await Task.CompletedTask;
        }

        public virtual async Task AddContactExchangeTokensAsync(List<Token> tokens, ContactExchange contactExchange, Profile initiatorProfile, Profile matchedProfile)
        {
            tokens.Add(new Token("ContactExchange.InitiatorName", await _customerService.GetCustomerFullNameAsync(initiatorProfile.Customer)));
            tokens.Add(new Token("ContactExchange.InitiatorRelation", contactExchange.InitiatorRelation));
            tokens.Add(new Token("ContactExchange.InitiatorPhone", contactExchange.InitiatorPhone));
            tokens.Add(new Token("ContactExchange.MatchedName", await _customerService.GetCustomerFullNameAsync(matchedProfile.Customer)));
            tokens.Add(new Token("ContactExchange.MatchedRelation", contactExchange.MatchedRelation));
            tokens.Add(new Token("ContactExchange.MatchedPhone", contactExchange.MatchedPhone));
            tokens.Add(new Token("ContactExchange.Notes", contactExchange.Notes));
            await Task.CompletedTask;
        }

        public virtual async Task AddMatchProfileTokensAsync(
            IList<Token> tokens,
            Profile initiatorProfile,
            IList<Profile> matchedProfiles
            )
        {
            if (initiatorProfile == null)
                throw new ArgumentNullException(nameof(initiatorProfile));

            if (matchedProfiles == null || !matchedProfiles.Any())
                throw new ArgumentNullException(nameof(matchedProfiles));

            // Add initiator's full name token
            tokens.Add(new Token("InitiatorProfile.FullName", await _customerService.GetCustomerFullNameAsync(initiatorProfile.Customer)));

            var profileListHtml = await GenerateProfileListHtmlAsync(matchedProfiles, _mediaSettings.ProductThumbPictureSize);

            // Add the constructed HTML table as a token
            tokens.Add(new Token("Match.Profile(s)", profileListHtml, true));
        }

        public virtual async Task AddMatchAcceptedTokensAsync(
            List<Token> tokens,
            Profile initiatorProfile, Profile matchedProfile,
            IList<Profile> matchedProfiles)
        {
            if (initiatorProfile == null)
                throw new ArgumentNullException(nameof(initiatorProfile));

            if (matchedProfile == null)
                throw new ArgumentNullException(nameof(matchedProfile));

            tokens.Add(new Token("ProposalReceiver.FullName", await _customerService.GetCustomerFullNameAsync(initiatorProfile.Customer)));
            tokens.Add(new Token("ProposalReceiver.ProfileId", await _profileService.FormatProfileDetailsAsync(initiatorProfile.Id, false, false)));
            tokens.Add(new Token("MatchAccepter.FullName", await _customerService.GetCustomerFullNameAsync(matchedProfile.Customer)));
            tokens.Add(new Token("MatchAccepter.ProfileId", await _profileService.FormatProfileDetailsAsync(matchedProfile.Id, false, false)));

            var profileListHtml = await GenerateProfileListHtmlAsync(matchedProfiles, _mediaSettings.ProductThumbPictureSize);

            // Add the constructed HTML table as a token
            tokens.Add(new Token("Match.Profile(s)", profileListHtml, true));
        }

        public virtual async Task AddRegistrationIntimationTokensAsync(
        IList<Token> tokens,
        decimal registrationFee,
        decimal gstAmount,
        decimal totalAmount)
        //string bankAccountName,
        //string bankAccountNumber,
        //string bankName,
        //string bankIFSCCode)
        {
            tokens.Add(new Token("RegistrationFee", registrationFee.ToString("0.##")));
            tokens.Add(new Token("GSTAmount", gstAmount.ToString("0.##")));
            tokens.Add(new Token("TotalAmount", totalAmount.ToString("0.##")));
            //tokens.Add(new Token("BankAccountName", bankAccountName));
            //tokens.Add(new Token("BankAccountNumber", bankAccountNumber));
            //tokens.Add(new Token("BankName", bankName));
            //tokens.Add(new Token("BankIFSCCode", bankIFSCCode));

            // if you need to raise an event or do anything else, do it here
            await Task.CompletedTask;
        }

        public virtual async Task AddRegistrationReceiptTokensAsync(
            IList<Token> tokens,
            decimal paidAmount,
            decimal gstAmount,
            decimal totalAmount,
            string amountInWords)
        {
            tokens.Add(new Token("PaidAmount", paidAmount.ToString("0.##")));
            tokens.Add(new Token("GSTAmount", gstAmount.ToString("0.##")));
            tokens.Add(new Token("TotalAmount", totalAmount.ToString("0.##")));
            tokens.Add(new Token("AmountInWords", amountInWords));
            await Task.CompletedTask;
        }

        public virtual async Task AddBankAccountTokensAsync(IList<Token> tokens)
        {
            tokens.Add(new Token("SubhLagan.BankAccountName", CompanyInfo.BankAccountName));
            tokens.Add(new Token("SubhLagan.BankAccountNumber", CompanyInfo.BankAccountNumber));
            tokens.Add(new Token("SubhLagan.BankName", CompanyInfo.BankName));
            tokens.Add(new Token("SubhLagan.BankIFSCCode", CompanyInfo.BankIFSCCode));
            tokens.Add(new Token("SubhLagan.PANCardNumber", CompanyInfo.PANCardNumber));
            
            // For backward compatibility, also add tokens with Company prefix
            tokens.Add(new Token("Company.BankAccountName", CompanyInfo.BankAccountName));
            tokens.Add(new Token("Company.BankAccountNumber", CompanyInfo.BankAccountNumber));
            tokens.Add(new Token("Company.BankName", CompanyInfo.BankName));
            tokens.Add(new Token("Company.BankIFSCCode", CompanyInfo.BankIFSCCode));
            tokens.Add(new Token("Company.PANCardNumber", CompanyInfo.PANCardNumber));
            
            await Task.CompletedTask;
        }

        public virtual async Task AddCongratulationsTokensAsync(List<Token> tokens, Profile initiatorProfile, Profile matchedProfile)
        {
            tokens.Add(new Token("MatchedProfile.FullName", await _customerService.GetCustomerFullNameAsync(matchedProfile.Customer)));
            await Task.CompletedTask;
        }

        public virtual async Task AddRegistrationFormTokensAsync(IList<Token> tokens, Customer customer)
        {
            var registrationFormUrl = await RouteUrlAsync(routeName: "RegistrationForm", routeValues: new { guid = customer.CustomerGuid });

            tokens.Add(new Token("RegistrationForm.URL", registrationFormUrl));
            await Task.CompletedTask;
        }

        public virtual async Task AddSignatureTokensAsync(IList<Token> tokens, User rm = null)
        {
            var name = rm == null ? $"Team {CompanyInfo.Name}" : await _customerService.GetCustomerFullNameAsync(rm.Customer);
            var phoneNumber = rm == null ? CompanyInfo.PhoneNumber : rm.Customer.Phone ?? string.Empty;

            // Add Relationship Manager tokens
            tokens.Add(new Token("Signature.Name", name));
            tokens.Add(new Token("Signature.PhoneNumber", phoneNumber));
        }

        public virtual async Task AddStoreTokensAsync(IList<Token> tokens, Office office, EmailAccount emailAccount)
        {
            if (emailAccount == null)
                throw new ArgumentNullException(nameof(emailAccount));

            await base.AddStoreTokensAsync(tokens, emailAccount);

            for (int i = 0; i < tokens.Count; i++)
            {
                switch (tokens[i].Key)
                {
                    case "Store.CompanyAddress":
                        if (office != null)
                            tokens[i] = new Token("Store.CompanyAddress", $"{office.Address1} <br/> {office.Address2}", true);
                        break;
                    case "Store.CompanyPhoneNumber":
                        if (office != null)
                            tokens[i] = new Token("Store.CompanyPhoneNumber", office.PhoneNumber);
                        break;
                    case "Store.Email":
                        tokens[i] = new Token("Store.Email", emailAccount.Email);
                        break;
                }
            }

        }

        public virtual async Task AddSelfBioDataTokensAsync(IList<Token> tokens, Customer customer)
        {
            var selfBioDataPdfUrl = await RouteUrlAsync(routeName: "ProfileViewPdf", routeValues: new { token = _profileService.GenerateCustomerGuidToken(customer.CustomerGuid, TokenType.Encrypted), type = (int)TokenType.Encrypted });

            tokens.Add(new Token("SelfBioData.URL", selfBioDataPdfUrl));
            await Task.CompletedTask;
        }

        public virtual async Task AddClientMatchAcceptedNotificationRMTokensAsync(IList<Token> tokens, Profile profileReceiver, Profile profileAccepter)
        {
            if (profileReceiver == null)
                throw new ArgumentNullException(nameof(profileReceiver));

            if (profileAccepter == null)
                throw new ArgumentNullException(nameof(profileAccepter));

            tokens.Add(new Token("ProfileReceiver.FullName", await _customerService.GetCustomerFullNameAsync(profileReceiver.Customer)));
            tokens.Add(new Token("ProfileAccepter.FullName", await _customerService.GetCustomerFullNameAsync(profileAccepter.Customer)));
        }

        public virtual async Task AddDailyReportTokensAsync(IList<Token> tokens, User user, DateTime reportDate, string userType, string attachmentFileName)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            tokens.Add(new Token("ReportDate", reportDate.ToString("dd/MM/yyyy")));
            tokens.Add(new Token("UserType", userType));
            tokens.Add(new Token("User.FullName", await _customerService.GetCustomerFullNameAsync(user.Customer)));
            tokens.Add(new Token("AttachmentFileName", attachmentFileName));

            await Task.CompletedTask;
        }

        public virtual async Task AddFullNameTokensAsync(List<Token> tokens, Profile initiatorProfile, Profile matchedProfile)
        {
            tokens.Add(new Token("InitiatorProfile.FullName", await _customerService.GetCustomerFullNameAsync(initiatorProfile.Customer)));
            tokens.Add(new Token("MatchedProfile.FullName", await _customerService.GetCustomerFullNameAsync(matchedProfile.Customer)));
        }
    }
}
