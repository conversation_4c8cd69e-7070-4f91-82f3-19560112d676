{"analysis_metadata": {"analysis_date": "2025-01-31", "codebase_version": "ASP.NET Core 7.0", "database_name": "SubhLaganNew_v1", "analysis_method": "Static code analysis of domain entities and services"}, "expected_entities_from_codebase": {"active_entities_with_services": ["ActionStatus", "City", "ClientMeeting", "Community", "ContactExchange", "EducationArea", "EducationLevel", "Education", "FamilyMember", "<PERSON><PERSON><PERSON>", "FollowUp", "FreshCallSchedule", "<PERSON><PERSON>", "Heading", "<PERSON>bby", "MarriageConfirmation", "MatchKundali", "MedicalHistory", "MeetingStatus", "Meeting", "OccupationAndBusiness", "Occupation", "Office", "OrderPaymentHistory", "Package", "PartnerPreference", "ProfileAttributeGroup", "ProfileAttribute", "ProfileCategory", "ProfileContact", "ProfileMatchInteraction", "ProfileMatchStatus", "ProfilePauseRecord", "ProfileSpecificationAttribute", "ProfileStatusHistory", "ProfileTransfer", "Profile", "Qualification", "Reason", "Remark", "ShortList", "UserWorkingHoursHistory", "User"], "entities_with_commented_migrations": ["AstroCity", "Card", "ProfileAttribute", "ProfileAttributeValue", "ProfileAttributeGroup", "ProfileSpecificationAttribute", "ProfileSpecificationAttributeOption"], "mapping_entities": ["ProfileHobbyMapping", "ProfileProfileCategoryMapping", "ProfileRestrictedMapping", "ProfileTransferProfileMapping", "ProfileUserMapping", "RefineSearchProfileSpecificationAttributeMapping", "UserEmailAccountMapping"], "general_domain_entities": ["Address", "GenericAttribute", "Customer", "CustomerAddressMapping", "CustomerCustomerRoleMapping", "CustomerPassword", "CustomerRole", "Country", "StateProvince", "Language", "LocaleStringResource", "LocalizedProperty", "ActivityLog", "ActivityLogType", "Log", "Picture", "PictureBinary", "Download", "EmailA<PERSON>unt", "MessageTemplate", "QueuedEmail", "QueuedEmailAttachment", "MassEmailCampaign", "MassEmailLog", "MassEmailRecipient", "WhatsAppQueuedMessageEntity", "ScheduleTask", "AclRecord", "PermissionRecord", "PermissionRecordCustomerRoleMapping", "Setting"]}, "unused_entity_analysis": {"definitely_unused": [{"entity": "AstroCity", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:43)", "No service implementation found", "No controller implementation found", "Only commented-out permission references found"], "confidence": "95%", "recommendation": "SAFE_TO_DELETE"}], "disabled_features": [{"entity": "Card", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:61)", "Has full service implementation (ICardService, CardService)", "Has controller implementation (CardController)", "Has complete MVC implementation"], "confidence": "0%", "recommendation": "DISABLED_FEATURE_DO_NOT_DELETE"}, {"entity": "ProfileAttribute", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:55)", "Has full service implementation (IProfileAttributeService)", "Has controller implementation (ProfileAttributeController)", "Extensive codebase usage found"], "confidence": "0%", "recommendation": "DISABLED_FEATURE_DO_NOT_DELETE"}, {"entity": "ProfileAttributeValue", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:58)", "102+ code references found across services", "Has dedicated caching implementation", "Heavily used in profile attribute system"], "confidence": "0%", "recommendation": "HEAVILY_USED_DO_NOT_DELETE"}, {"entity": "ProfileAttributeGroup", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:67)", "Has service implementation (IProfileAttributeGroupService)", "Has controller implementation (ProfileAttributeGroupController)"], "confidence": "0%", "recommendation": "DISABLED_FEATURE_DO_NOT_DELETE"}, {"entity": "ProfileSpecificationAttribute", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:70)", "Has service implementation (IProfileSpecificationAttributeService)", "Has controller implementation (ProfileSpecificationAttributeController)"], "confidence": "0%", "recommendation": "DISABLED_FEATURE_DO_NOT_DELETE"}, {"entity": "ProfileSpecificationAttributeOption", "reasons": ["Commented out in migration (SchemaMigrationTableCreateOrAlter.cs:73)", "Extensive usage in profile attribute system", "Referenced in service implementations"], "confidence": "0%", "recommendation": "ACTIVE_FEATURE_DO_NOT_DELETE"}]}, "cross_reference_instructions": {"compare_with_database": "Cross-reference this analysis with actual database tables to identify:", "steps": ["1. Tables that exist in database but not in this codebase analysis (orphaned tables)", "2. Tables that exist in codebase but not in database (missing implementations)", "3. Tables with zero data and zero references (safe deletion candidates)", "4. Tables that appear unused in code but have data in database (requires investigation)"], "safety_checks": ["Always backup database before deletion", "Verify no dynamic SQL references exist", "Check for external application dependencies", "Confirm no data migration dependencies"]}}