﻿using Subhlagan.Core.Domain.Enum;

namespace Subhlagan.Application.Pdf
{
    public partial class FamilyMemberItem
    {
        public string Name { get; set; }
        public string Relation { get; set; }
        public RelationType RelationType { get; set; }

        public int? Age { get; set; }
        public string WorkStatus { get; set; }
        public string Occupation { get; set; }
        public string Employer { get; set; }
        public string Contact { get; set; }
        public string Address { get; set; }
        public string NativePlace { get; set; }
        public string Education { get; set; }
        public string MaritalStatus { get; set; }
        public decimal? AnnualIncome { get; set; }
        public string AdditionalInfo { get; set; }
        public string Caste { get; set; }
        public string SubCaste { get; set; }
        public string Gotra { get; set; }
        public string LanguagesSpoken { get; set; }
        public bool IsDeceased { get; set; }
        public bool IsPrimaryContact { get; set; }
        public string ProfilePictureUrl { get; set; }
        public string MarriedTo { get; set; }
        public string MarriedToSonOrDaughterOf { get; set; }
        public string SpouseAddress { get; set; }
        public string FamilyType { get; set; }
        public string RelativeOrder { get; set; }

        public string Prefix { get; set; }
    }

}
