﻿using AngleSharp;
using AngleSharp.Dom;
using AngleSharp.Html.Dom;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace Subhlagan.Application.Pdf
{
    public static class HtmlToPdfRenderer
    {
        /// <summary>
        /// Main method: parse the HTML and compose it into the given QuestPDF container.
        /// </summary>
        public static async Task ComposeHtmlAsync(IContainer container, string html)
        {
            // Step 1: Parse HTML with AngleSharp
            var context = BrowsingContext.New(AngleSharp.Configuration.Default);
            var doc = await context.OpenAsync(req => req.Content(html));

            // Step 2: Compose the top-level container. 
            // We'll put each paragraph or block-level element in a column item.
            container.Column(column =>
            {
                // We handle direct children of the <body> or the entire Document here.
                // doc.Body could be null if the HTML is very minimal, so we fallback to doc.
                var root = doc.Body ?? (AngleSharp.Dom.IElement)doc.DocumentElement;

                foreach (var child in root.ChildNodes)
                {
                    // Typically you might want to skip text nodes that are just whitespace.
                    if (IsMeaningfulNode(child))
                    {
                        column.Item().Element(block =>
                        {
                            // Now parse this node and add content
                            ParseNode(child, block, new FormatState());
                        });
                    }
                }
            });
        }

        /// <summary>
        /// Recursive method: interprets a DOM node (element or text) and renders it into the given container.
        /// </summary>
        private static void ParseNode(INode node, IContainer container, FormatState formatState)
        {
            switch (node)
            {
                case IText textNode:
                    // If it's a text node, we just render text as a span with the current formatting
                    var trimmed = textNode.Text.Trim();
                    if (!string.IsNullOrEmpty(trimmed))
                    {
                        container.Text(t => ApplyFormatting(t.Span(trimmed), formatState));
                    }
                    break;

                case AngleSharp.Dom.IElement element:
                    // It's an actual HTML element like <p>, <span>, <strong>, ...
                    var tagName = element.TagName.ToLower();

                    switch (tagName)
                    {
                        case "p":
                            // Treat <p> as a block. Let's do a sub-container for the paragraph text.
                            container.Column(col =>
                            {
                                // Let's parse the children of the <p> and treat each text run or inline child
                                // as a new line if we detect <br> or so. 
                                // Alternatively, we can do all inline in a single text block.
                                col.Item().Text(txt =>
                                {
                                    foreach (var child in element.ChildNodes)
                                    {
                                        if (child is IHtmlBreakRowElement)
                                        {
                                            // <br> => we can do a newline
                                            txt.Span("\n");
                                        }
                                        else
                                        {
                                            ParseInlineNode(child, txt, formatState);
                                        }
                                    }
                                });
                            });
                            break;

                        case "br":
                            // A <br> inside some inline context => we can do a new line in the container
                            container.Text(t => t.Span("\n"));
                            break;

                        default:
                            // For inline elements like <span>, <strong>, <u>, <em>, <a>, etc.,
                            // or any custom element, we can parse it as inline content. 
                            // For block-level elements (div, etc.), we might do a new "column item".
                            if (IsBlockElement(tagName))
                            {
                                // e.g. <div>, <section> => treat it as a block container
                                container.Column(col =>
                                {
                                    col.Item().Element(nestedBlock =>
                                    {
                                        var newState = ApplyElementStyle(element, formatState);

                                        // Fix: Nest children inside a column so multiple items can be added
                                        nestedBlock.Column(nestedCol =>
                                        {
                                            foreach (var child in element.ChildNodes)
                                            {
                                                nestedCol.Item().Element(inner =>
                                                {
                                                    ParseNode(child, inner, newState);
                                                });
                                            }
                                        });
                                    });


                                    //col.Item().Element(nestedBlock =>
                                    //{
                                    //    // We clone the state and apply this element’s styling
                                    //    var newState = ApplyElementStyle(element, formatState);

                                    //    // parse children
                                    //    foreach (var child in element.ChildNodes)
                                    //    {
                                    //        ParseNode(child, nestedBlock, newState);
                                    //    }
                                    //});
                                });
                            }
                            else
                            {
                                // Otherwise, treat it as inline: parse it with the text descriptor approach
                                container.Text(text =>
                                {
                                    ParseInlineNode(element, text, formatState);
                                });
                            }
                            break;
                    }
                    break;
            }
        }

        /// <summary>
        /// Parse a node (or subtree) in an inline context, adding text spans to a TextDescriptor.
        /// Used for things like <span>, <strong>, <u>, <em>, <a> within a line of text.
        /// </summary>
        private static void ParseInlineNode(INode node, TextDescriptor text, FormatState currentState)
        {
            switch (node)
            {
                case IText textNode:
                    var trimmed = textNode.Text.Replace("\r", "").Replace("\n", "");
                    if (!string.IsNullOrEmpty(trimmed))
                    {
                        ApplyFormatting(text.Span(trimmed), currentState);
                        //    text.Element(x =>
                        //    {
                        //        ApplyFormatting(x.Span(trimmed), currentState);
                        //    });
                    }
                    break;

                case AngleSharp.Dom.IElement element:
                    var tagName = element.TagName.ToLower();

                    // Clone the state so changes do not affect parent
                    var newState = ApplyElementStyle(element, currentState);

                    if (element.ChildNodes.Length == 0)
                    {
                        // If it's e.g. <br> or a self-closing element
                        if (tagName == "br")
                            text.Span("\n");
                    }
                    else
                    {
                        // For example <strong>some text</strong>
                        foreach (var child in element.ChildNodes)
                        {
                            if (child is IHtmlBreakRowElement)
                            {
                                text.Span("\n");
                            }
                            else
                            {
                                // Recursively parse children 
                                ParseInlineNode(child, text, newState);
                            }
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// Applies the FormatState to a newly created text span.
        /// </summary>
        private static void ApplyFormatting(TextSpanDescriptor span, FormatState state)
        {
            if (state.Bold)
                span.Bold();
            if (state.Italic)
                span.Italic();
            if (state.Underline)
                span.Underline();

            //if (!string.IsNullOrWhiteSpace(state.ColorHex))
            //{
            //    try
            //    {
            //        span.FontColor(state.ColorHex);
            //    }
            //    catch
            //    {
            //        span.FontColor(Colors.Black);
            //    }
            //}
            span.FontColor(Colors.Black);
            if (state.IsLink)
            {
                // Default link color or something
                span.FontColor(Colors.Blue.Medium);
                // You could also store an "Href" in FormatState and set up an Action 
                // or other link-like functionality if needed.
            }
        }

        /// <summary>
        /// Creates a new FormatState cloned from currentState and applies any 
        /// relevant styling from the element (tag name or style attributes).
        /// </summary>
        private static FormatState ApplyElementStyle(AngleSharp.Dom.IElement element, FormatState currentState)
        {
            // Clone state
            var newState = currentState.Clone();

            // 1) Tag-based styling
            var tagName = element.TagName.ToLower();
            switch (tagName)
            {
                case "strong":
                case "b":
                    newState.Bold = true;
                    break;
                case "em":
                case "i":
                    newState.Italic = true;
                    break;
                case "u":
                    newState.Underline = true;
                    break;
                case "a":
                    newState.IsLink = true;
                    // If you want the link URL, you can read element.GetAttribute("href") etc.
                    break;
            }

            // 2) Inline style-based styling
            var style = element.GetAttribute("style");
            if (!string.IsNullOrWhiteSpace(style))
            {
                ParseInlineStyle(style, newState);
            }

            return newState;
        }

        /// <summary>
        /// Parse the CSS in the style attribute (e.g. "color: #222; font-weight: bold;") 
        /// and update the FormatState accordingly.
        /// </summary>
        private static void ParseInlineStyle(string styleString, FormatState state)
        {
            // styleString might look like: "color: #222222; font-weight: bold; text-decoration: underline;"
            var declarations = styleString.Split(';', StringSplitOptions.RemoveEmptyEntries);
            foreach (var decl in declarations)
            {
                var parts = decl.Split(':', 2, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length != 2)
                    continue;

                var prop = parts[0].Trim().ToLower();
                var val = parts[1].Trim().ToLower();

                switch (prop)
                {
                    case "color":
                        //state.ColorHex = val; // e.g. "#222222"
                        //span.FontColor(Colors.Black);
                        break;
                    case "font-weight":
                        if (val.Contains("bold"))
                            state.Bold = true;
                        break;
                    case "text-decoration":
                        if (val.Contains("underline"))
                            state.Underline = true;
                        // if (val.Contains("line-through")) => we could add a strike-through if desired
                        break;
                    case "font-style":
                        if (val.Contains("italic"))
                            state.Italic = true;
                        break;
                        // you can parse font-size, background-color, etc., as needed
                }
            }
        }

        /// <summary>
        /// Simple logic to check if we should skip a node that is only whitespace.
        /// </summary>
        private static bool IsMeaningfulNode(INode node)
        {
            if (node is IText text && string.IsNullOrWhiteSpace(text.Text))
                return false;
            return true;
        }

        /// <summary>
        /// Detect if an element name is typically a block-level element (div, section, etc.).
        /// So we know whether to treat it as a separate item vs. inline content.
        /// </summary>
        private static bool IsBlockElement(string tagName)
        {
            // Extend this list to handle more block-level elements as needed
            var blockTags = new HashSet<string>
        {
            "div", "section", "article", "aside", "header", "footer", "nav", "ul", "ol", "li", "table", "tr", "td"
        };
            return blockTags.Contains(tagName);
        }

        /// <summary>
        /// Holds the active text styles. You can expand it with background color, font-size, etc.
        /// </summary>
        private class FormatState
        {
            public bool Bold { get; set; }
            public bool Italic { get; set; }
            public bool Underline { get; set; }
            public bool IsLink { get; set; }
            //public string? ColorHex { get; set; }

            public FormatState Clone()
            {
                return new FormatState
                {
                    Bold = this.Bold,
                    Italic = this.Italic,
                    Underline = this.Underline,
                    IsLink = this.IsLink,
                    //ColorHex = this.ColorHex
                };
            }
        }
    }

}
