﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain.Customers;
using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;
using Subhlagan.Application.Customers;
using Subhlagan.Core;

namespace Subhlagan.Application.ProfileAttributes.Caching
{
    public partial class ProfileAttributeValueCacheEventConsumer : CacheEventConsumer<ProfileAttributeValue>
    {
        /// <summary>
        /// Clear cache data
        /// </summary>
        /// <param name="entity">Entity</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        protected override async Task ClearCacheAsync(ProfileAttributeValue entity, EntityEventType entityEventType)
        {
            await RemoveAsync(PageBaaSDefaults.ProfileAttributeValuesByAttributeCacheKey, entity.ProfileAttributeId);
            await base.ClearCacheAsync(entity);

        }
    }
}
