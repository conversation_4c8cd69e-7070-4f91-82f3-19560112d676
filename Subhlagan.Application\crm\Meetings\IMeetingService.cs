﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Meetings
{
    public interface IMeetingService
    {
        Task InsertMeetingAsync(Meeting meeting);
        Task UpdateMeetingAsync(Meeting meeting);
        Task DeleteMeetingAsync(Meeting meeting);
        Task<Meeting> GetMeetingByIdAsync(int meetingId);
        Task<IPagedList<Meeting>> GetAllMeetingsAsync(
            DateTime? meetingDateFrom = null,
            DateTime? meetingDateTo = null,
            int maleProfileId = 0,
            int femaleProfileId = 0,
            IList<int> userIds = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false, 
            string Name = null,
            IList<int> maleProfileIds = null,
            IList<int> femaleProfileIds = null,
            IList<int> assignedUserIds = null, IList<int> meetingIds = null,
            List<int> matchResultIds = null, bool? IsClosed = null);

        //Task<IList<Meeting>> GetAllMeetingsAsync(DateTime? meetingDateFrom = null, DateTime? meetingDateTo = null, int maleProfileId = 0, int femaleProfileId = 0, int userId=0);
        Task<Meeting> GetMeetingByCriteriaAsync(int initiatorProfileId, int matchedProfileId);

        Task<IList<Meeting>> GetMeetingsAsync(int maleProfileId, int femaleProfileId);

        Task<IList<Meeting>> GetMeetingsByProfileIdAsync(int maleProfileId=0, int femaleProfileId=0);
    }

}
