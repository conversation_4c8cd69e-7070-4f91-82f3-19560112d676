﻿using Subhlagan.Application.Logging;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace Subhlagan.Application.MatchKundalis
{
    public class SubhlaganMarriageReportService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger _logger;

        public SubhlaganMarriageReportService(HttpClient httpClient, ILogger logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<SubhlaganMarriageReport> GetMarriageReportAsync(SubhlaganMarriageReportRequest request)
        {
            var url = string.Empty;
            try
            {
                if (request == null)
                    throw new ArgumentNullException(nameof(request));

                var queryString = request.ToQueryString();
                url = $"https://astrobix.com/api/subhlaganmarriagereport?{queryString}";

                var httpRequest = new HttpRequestMessage(HttpMethod.Get, url);
                var response = await _httpClient.SendAsync(httpRequest);

                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var marriageReport = JsonConvert.DeserializeObject<SubhlaganMarriageReport>(responseContent);

                if (marriageReport == null || !marriageReport.Result)
                {
                    var errorMessage = $"Failed to retrieve marriage report. The response content might be invalid. Request URL: {url}";
                    await _logger.WarningAsync(errorMessage);
                    throw new Exception("Failed to retrieve marriage report.");
                }

                return marriageReport;
            }
            catch (Exception ex)
            {
                var userMessage = "An unexpected error occurred while processing your request. Please contact the system administrator for assistance.";
                await _logger.ErrorAsync($"An error occurred in GetMarriageReportAsync. Request URL: {url}. Exception: {ex.Message}", ex);
                throw new ApplicationException(userMessage, ex);
            }
        }
    }

    public class SubhlaganMarriageReport
    {
        /// <summary>
        /// Indicates if the request was successful
        /// </summary>
        [JsonProperty("result")]
        public bool Result { get; set; }

        /// <summary>
        /// The download path for the marriage report
        /// </summary>
        [JsonProperty("downloadpath")]
        public string DownloadPath { get; set; }

        /// <summary>
        /// The total compatibility score
        /// </summary>
        [JsonProperty("total")]
        public decimal Total { get; set; }

        /// <summary>
        /// Manglik status of the boy
        /// </summary>
        [JsonProperty("boymanglik")]
        public int BoyManglik { get; set; }

        /// <summary>
        /// Manglik status of the girl
        /// </summary>
        [JsonProperty("girlmanglik")]
        public int GirlManglik { get; set; }
    }

    public class SubhlaganMarriageReportRequest
    {
        public string BoyName { get; set; }
        public string BoyDay { get; set; }
        public string BoyMonth { get; set; }
        public string BoyYear { get; set; }
        public string BoyHour { get; set; }
        public string BoyMinute { get; set; }
        public double BoyLatitude { get; set; }
        public double BoyLongitude { get; set; }
        public double BoyGmtDiff { get; set; }
        public string BoyPlace { get; set; }
        public string BoyCountry { get; set; }
        public string GirlName { get; set; }
        public string GirlDay { get; set; }
        public string GirlMonth { get; set; }
        public string GirlYear { get; set; }
        public string GirlHour { get; set; }
        public string GirlMinute { get; set; }
        public double GirlLatitude { get; set; }
        public double GirlLongitude { get; set; }
        public double GirlGmtDiff { get; set; }
        public string GirlPlace { get; set; }
        public string GirlCountry { get; set; }
        public string Language { get; set; } = "English";
        public string PartnerCode { get; set; } = "subhlagan";
        public string PartnerPassword { get; set; } = "subhmarryrep";

        public string ToQueryString()
        {
            return $"boyname={Uri.EscapeDataString(BoyName)}&boyday={BoyDay}&boymonth={BoyMonth}&boyyear={BoyYear}" +
                   $"&boyhour={BoyHour}&boymin={BoyMinute}&boylat={BoyLatitude}&boylong={BoyLongitude}" +
                   $"&boygmtdiff={BoyGmtDiff}&boyplace={Uri.EscapeDataString(BoyPlace)}&boycountry={Uri.EscapeDataString(BoyCountry)}" +
                   $"&galname={Uri.EscapeDataString(GirlName)}&galday={GirlDay}&galmonth={GirlMonth}&galyear={GirlYear}" +
                   $"&galhour={GirlHour}&galmin={GirlMinute}&gallat={GirlLatitude}&gallong={GirlLongitude}" +
                   $"&galgmtdiff={GirlGmtDiff}&galplace={Uri.EscapeDataString(GirlPlace)}&galcountry={Uri.EscapeDataString(GirlCountry)}" +
                   $"&language={Language}&partnercode={PartnerCode}&partnerpass={PartnerPassword}";
        }
    }

}
