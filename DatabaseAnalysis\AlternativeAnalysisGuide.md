# 🔧 Alternative Database Analysis Guide - Manual Approach

## 📋 Overview
If you can't install the PowerShell SqlServer module, you can run the analysis manually using SQL Server Management Studio (SSMS) or Azure Data Studio.

## 🚀 Quick Start - Manual Analysis

### **Option 1: Using SQL Server Management Studio (Recommended)**

1. **Open SQL Server Management Studio**
2. **Connect to your SQL Server instance**: `Localhost\SQLEXPRESS`
3. **Select database**: `SubhLaganNew_v1`
4. **Run each script individually** in the following order:

#### Step 1: Get All Tables
```sql
-- Copy and paste content from: 01_GetAllTables.sql
-- This will show you all tables with sizes and basic info
```

#### Step 2: Get Table Relationships  
```sql
-- Copy and paste content from: 02_GetTableRelationships.sql  
-- This shows foreign key dependencies
```

#### Step 3: Get Usage Statistics
```sql
-- Copy and paste content from: 03_GetTableUsageStats.sql
-- This shows which tables have been accessed recently
```

#### Step 4: Get Table References
```sql
-- Copy and paste content from: 04_GetTableReferences.sql
-- This finds tables referenced in stored procedures/views
```

#### Step 5: Comprehensive Analysis
```sql
-- Copy and paste content from: 05_DatabaseAnalysisSummary.sql
-- This provides the main analysis with confidence scores
```

### **Option 2: Using sqlcmd Command Line**

If you have sqlcmd installed, you can run:

```cmd
cd /d "C:\Workshop\SubhLagan\Projects\Subhlagan\src\DatabaseAnalysis"

sqlcmd -S "Localhost\SQLEXPRESS" -d "SubhLaganNew_v1" -i "01_GetAllTables.sql" -o "Results_01_AllTables.txt"
sqlcmd -S "Localhost\SQLEXPRESS" -d "SubhLaganNew_v1" -i "02_GetTableRelationships.sql" -o "Results_02_Relationships.txt"
sqlcmd -S "Localhost\SQLEXPRESS" -d "SubhLaganNew_v1" -i "03_GetTableUsageStats.sql" -o "Results_03_Usage.txt"
sqlcmd -S "Localhost\SQLEXPRESS" -d "SubhLaganNew_v1" -i "04_GetTableReferences.sql" -o "Results_04_References.txt"
sqlcmd -S "Localhost\SQLEXPRESS" -d "SubhLaganNew_v1" -i "05_DatabaseAnalysisSummary.sql" -o "Results_05_Summary.txt"
```

## 🔍 What to Look For in Results

### **Key Indicators of Unused Tables:**

1. **From Script 5 (DatabaseAnalysisSummary.sql):**
   - Look for `UnusedConfidenceScore >= 85`
   - Tables with `Recommendation = 'SAFE TO DELETE'`

2. **From Script 1 (GetAllTables.sql):**
   - Tables with `RowCount = 0` (empty tables)
   - Very small `TotalSizeMB` values

3. **From Script 3 (GetTableUsageStats.sql):**
   - Tables with `TotalUserActivity = 0`
   - Tables with `ActivityStatus = 'No Usage Statistics'`

4. **From Script 2 (GetTableRelationships.sql):**
   - Tables listed in the "Orphaned Tables" section
   - Tables with no foreign key relationships

## 📊 Expected Results Based on Codebase Analysis

You should see these patterns:

### **High Confidence Unused (Safe to Delete):**
- **AstroCity** - Should have high unused confidence score
- Possibly 1-2 other empty, unreferenced tables

### **Disabled Features (DO NOT DELETE):**
Tables that may appear unused but have full code implementations:
- Card
- ProfileAttribute
- ProfileAttributeValue  
- ProfileAttributeGroup
- ProfileSpecificationAttribute
- ProfileSpecificationAttributeOption

### **Active Tables (Keep):**
All core CRM tables like:
- Profile, User, Community, City, ActionStatus, Hobby, etc.

## 🛡️ Safety Checklist

Before deleting any table, verify:

- [ ] **UnusedConfidenceScore >= 85**
- [ ] **RowCount = 0** (empty table)
- [ ] **No foreign key references** (not a parent table)
- [ ] **Not referenced by stored procedures/views**
- [ ] **Not in the "disabled features" list above**
- [ ] **Database backup created**

## 📝 Manual Analysis Worksheet

Use this template to track your findings:

```
TABLE ANALYSIS WORKSHEET
========================

Table Name: ___________________
Row Count: ___________________
Has Foreign Keys: ___________________
Referenced by Objects: ___________________
Usage Statistics: ___________________
Confidence Score: ___________________
Codebase Status: ___________________
Decision: ___________________
Notes: ___________________

```

## 🔧 Troubleshooting

### **If Scripts Don't Run:**
1. Check your SQL Server connection
2. Ensure you have the correct database name
3. Verify you have appropriate permissions

### **If Results Look Strange:**
1. Cross-reference with the codebase analysis in `COMPREHENSIVE_ANALYSIS_REPORT.md`
2. Look for tables that exist in DB but not in migrations
3. Check for tables with data but no code references

### **If You Need PowerShell Module:**
Run in PowerShell as Administrator:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
Install-Module -Name SqlServer -Force
```

## 📞 Next Steps After Manual Analysis

1. **Compare your findings** with the codebase analysis report
2. **Create database backup** using `06_CreateBackupScripts.sql`
3. **Update the migration file** with confirmed unused tables
4. **Test in development** environment first

## ⚠️ Important Notes

- **Manual analysis requires more care** - double-check everything
- **When in doubt, don't delete** - it's safer to keep an unused table
- **Focus on tables with highest confidence scores** first
- **Always backup before making any changes**

Remember: The goal is **safe cleanup**, not aggressive deletion. Take your time and verify each decision.