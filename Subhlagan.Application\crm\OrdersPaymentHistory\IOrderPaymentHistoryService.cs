﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.OrdersPaymentHistory
{
    public interface IOrderPaymentHistoryService
    {
        Task<OrderPaymentHistory> InsertOrderPaymentHistoryAsync(OrderPaymentHistory orderPaymentHistory);
        Task UpdateOrderPaymentHistoryAsync(OrderPaymentHistory orderPaymentHistory);
        Task DeleteOrderPaymentHistoryAsync(OrderPaymentHistory orderPaymentHistory);
        Task<OrderPaymentHistory> GetOrderPaymentHistoryByIdAsync(int orderPaymentHistoryId);
        //Task<IList<OrderPaymentHistory>> GetAllOrderPaymentHistoriesAsync(int orderId, int profileId = 0);
        Task<IList<OrderPaymentHistory>> GetAllOrderPaymentHistoriesAsync(int profileId = 0);

        Task<IPagedList<OrderPaymentHistory>> GetAllOrderPaymentHistoriesAsync(IList<int> profileIds = null, DateTime? paymentUpdatedDateFrom = null,
            DateTime? paymentUpdatedDateTo = null, int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            int officeId = 0, IList<int> matchResultIds = null, IList<int> paymentStatusIds=null);

        Task<IList<OrderPaymentHistory>> GetOrderPaymentHistoryByIdsAsync(int[] orderPaymentHistoryIds);
    }
}
