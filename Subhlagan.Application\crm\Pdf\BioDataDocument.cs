﻿using Subhlagan.Application.Common.Pdf;
using Subhlagan.Application.Localization;
using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using IContainer = QuestPDF.Infrastructure.IContainer;
using Subhlagan.Core.Domain.Enum;
using Subhlagan.Core.Infrastructure;
using SkiaSharp;
using Subhlagan.Core;
using DocumentFormat.OpenXml.Spreadsheet;

namespace Subhlagan.Application.Pdf
{
    public class BioDataDocument : PdfDocument<BioDataSource>
    {
        private readonly IKsFileProvider _fileProvider;

        #region Ctor

        public BioDataDocument(BioDataSource source, ILocalizationService localizationService, IKsFileProvider fileProvider)
            : base(source, localizationService)
        {
            _fileProvider = fileProvider;
        }

        #endregion

        #region Utilities
        private bool HasFamilyMemberData(FamilyMemberItem member)
        {
            // Check for string-based data
            if (!string.IsNullOrWhiteSpace(member.Name)) return true;
            // if (!string.IsNullOrWhiteSpace(member.Relation)) return true;
            if (!string.IsNullOrWhiteSpace(member.WorkStatus)) return true;
            if (!string.IsNullOrWhiteSpace(member.Occupation)) return true;
            if (!string.IsNullOrWhiteSpace(member.Employer)) return true;
            if (!string.IsNullOrWhiteSpace(member.Contact)) return true;
            if (!string.IsNullOrWhiteSpace(member.Address)) return true;
            if (!string.IsNullOrWhiteSpace(member.NativePlace)) return true;
            if (!string.IsNullOrWhiteSpace(member.Education)) return true;
            if (!string.IsNullOrWhiteSpace(member.MaritalStatus)) return true;
            if (!string.IsNullOrWhiteSpace(member.AdditionalInfo)) return true;
            if (!string.IsNullOrWhiteSpace(member.Caste)) return true;
            if (!string.IsNullOrWhiteSpace(member.SubCaste)) return true;
            if (!string.IsNullOrWhiteSpace(member.Gotra)) return true;
            if (!string.IsNullOrWhiteSpace(member.LanguagesSpoken)) return true;
            if (!string.IsNullOrWhiteSpace(member.ProfilePictureUrl)) return true;
            if (!string.IsNullOrWhiteSpace(member.MarriedTo)) return true;
            if (!string.IsNullOrWhiteSpace(member.MarriedToSonOrDaughterOf)) return true;
            if (!string.IsNullOrWhiteSpace(member.SpouseAddress)) return true;
            if (!string.IsNullOrWhiteSpace(member.FamilyType)) return true;
            if (!string.IsNullOrWhiteSpace(member.RelativeOrder)) return true;

            // Check if Age is not null or zero
            if (member.Age.HasValue && member.Age.Value > 0) return true;

            // Check if AnnualIncome is not null or zero
            if (member.AnnualIncome.HasValue && member.AnnualIncome.Value > 0) return true;

            // Check boolean flags that might be relevant
            if (member.IsDeceased) return true;
            if (member.IsPrimaryContact) return true;

            // If we reach here, all fields are empty or default
            return false;
        }

        protected virtual string GetSubhLaganPdfImagesPath()
        {
            return _fileProvider.GetAbsolutePath(PageBaaSDefaults.SubhLaganPdfImages);
        }

        protected virtual void ComposeHeader(IContainer container)
        {
            // Add a bottom border, padding, and default styling (font color, size)
            container
                .BorderBottom(1)
                .BorderColor("#AC0C16") // matches "border-bottom:1px solid #AC0C16"
                .PaddingBottom(10)    // matches "padding:12px 0"
                .DefaultTextStyle(style => style
                    .FontColor("#95060a")
                    .FontSize(9))
                .Column(column =>
                {
                    column.Item().Row(row =>
                    {
                        // Left (empty) - ensures the middle item is truly centered
                        row.RelativeItem(1).Text(" ");

                        // Center: Logo
                        row.RelativeItem(1).AlignCenter().Element(imgContainer =>
                        {
                            // Path to the logo file
                            var fileName = "logo-subhlagan.png";
                            var filePath = Path.Combine(GetSubhLaganPdfImagesPath(), fileName);

                            // Set logo width/height as needed
                            imgContainer
                                .Width(120)
                                .Image(filePath, ImageScaling.FitWidth);
                        });

                        if (Source.Manager != null)
                        {
                            // Right: Manager Info
                            row.RelativeItem(1)
                                    .AlignRight()
                                    .DefaultTextStyle(s => s.FontSize(10))
                                    .Column(col =>
                                    {
                                        if (!string.IsNullOrEmpty(Source.Manager.Name))
                                        {
                                            col.Item().AlignRight().Text($"{Source.Manager.Name}");
                                        }
                                        if (!string.IsNullOrEmpty(Source.Manager.Phone))
                                        {
                                            col.Item().AlignRight().Text($"Mob: {Source.Manager.Phone}");
                                        }
                                        if (!string.IsNullOrEmpty(Source.Manager.Email))
                                        {
                                            col.Item().AlignRight().Text($"Email: {Source.Manager.Email}");
                                        }
                                    });
                        }
                        else
                        {
                            row.RelativeItem(1).Text(" ");
                        }
                    });

                    // Second row: Full Name, plus DOB/Height
                    column.Item().AlignCenter().Column(col =>
                    {
                        // Full Name in bold, slightly bigger
                        if (!string.IsNullOrEmpty(Source.FormatProfileId))
                        {
                            col.Item().AlignCenter().Text(t =>
                            {
                                t.DefaultTextStyle(s => s.FontSize(14).Bold());
                                t.Span(Source.FormatProfileId);
                            });
                        }

                        // DateOfBirth, Height, etc.
                        // Example: "DOB: 01 Jan 1990 (33 Yrs.), Height: 5'7"
                        var personalDetails = new List<string>();

                        if (!string.IsNullOrEmpty(Source.DateOfBirth))
                            personalDetails.Add($"DOB: {Source.DateOfBirth} ({Source.Age} yrs.)");

                        if (!string.IsNullOrEmpty(Source.Height))
                            personalDetails.Add($"Height: {Source.Height}");

                        // Join personal details with comma separation (customize as needed)
                        if (personalDetails.Any())
                        {
                            col.Item().AlignCenter().DefaultTextStyle(s => s.FontSize(12)).Text(string.Join(",  ", personalDetails));
                        }
                    });

                });
        }

        protected void ComposeContentPhoto(IContainer container)
        {
            container.PaddingVertical(20).Column(column =>
            {
                column.Spacing(10);

                // Section 1: Pictures (e.g., a grid)
                if (Source.PicturePaths?.Any() == true)
                {
                    //column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Photo Gallery");
                    //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                    column.Item().Element(x => ComposeProfileImages(x));
                }
            });
        }

        protected void ComposeContent(IContainer container)
        {
            container.PaddingVertical(20).Column(column =>
            {
                column.Spacing(2);

                // Section 1: Pictures (e.g., a grid)
                //if (Source.PicturePaths?.Any() == true)
                //{
                //    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Photo Gallery");
                //    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                //    column.Item().Element(x => ComposeProfileImages(x));
                //}

                // Section 2: Profile Overview
                column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Profile Overview");
                column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                column.Item().Element(x => ComposeProfileOverview(x, Source));

                //// Section 2: Profile Overview
                //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                //column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Profile Overview");
                //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1); // subtle divider

                //// Profile details
                //column.Item().Element(c => ComposeProfileDetailsTable(c, Source));

                // Section: Education
                if (Source.Educations?.Any() == true)
                {
                    // Divider
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Education");
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                    column.Item().Element(c => ComposeEducations(c, Source.Educations));
                }


                // Add the Occupation and Business section if available.
                if (Source.OccupationAndBusiness != null)
                {
                    // Divider
                    //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    //column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Occupation / Profession");
                    //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().Element(c => ComposeOccupationAndBusiness(c, "Occupation / Profession", Source.OccupationAndBusiness, column));
                }

                if (!string.IsNullOrWhiteSpace(Source.AboutMe))
                {
                    // Divider
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("About Me");
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().PaddingHorizontal(10).Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.AboutMe));
                }

                // Add the "About Me" section if content exists.
                //if (!string.IsNullOrWhiteSpace(Source.AboutMe))
                //{
                //    column.Item().PaddingTop(10).Column(about =>
                //    {
                //        about.Item()
                //             .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a"))
                //             .Text("About Me");

                //        about.Item().Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.AboutMe));
                //    });
                //}

                // Family
                if (Source.FamilyMembers?.Any() == true)
                {
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Family Details");
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                    column.Item().Element(c => ComposeFamilyInfo(c, Source.FamilyMembers));
                }

                if (!string.IsNullOrWhiteSpace(Source.AboutFamily))
                {
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("About Family");
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().PaddingHorizontal(10).Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.AboutFamily));
                    // Group the heading and text within a Row to ensure they stay together
                    //column.Item().Element(container =>
                    //{
                    //    container
                    //        .Row(row =>
                    //        {
                    //            row.RelativeItem().Column(col =>
                    //            {
                    //                // Heading
                    //                col.Item()
                    //                    .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a"))
                    //                    .Text("About Family");

                    //                col.Item().Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.AboutFamily));
                    //            });
                    //        });
                    //});
                }

                if (Source.FamilyOccupationAndBusiness != null)
                {
                    // Divider
                    //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    //column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Family Business");
                    //column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().Element(c => ComposeOccupationAndBusiness(c, "Family Business", Source.FamilyOccupationAndBusiness, column));
                }


                //if (!string.IsNullOrWhiteSpace(Source.FamilyOccupationAndBusiness.BusinessDetails))
                //{
                //    // Group the heading and text within a Row to ensure they stay together
                //    column.Item().Element(container =>
                //    {
                //        container
                //            .Row(row =>
                //            {
                //                row.RelativeItem().Column(col =>
                //                {
                //                    // Heading
                //                    col.Item()
                //                        .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a"))
                //                        .Text("Business Details");

                //                    col.Item().Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.FamilyOccupationAndBusiness.BusinessDetails));
                //                });
                //            });
                //    });
                //}

                //column.Item().Element(c => ComposeOccupationAndBusinessTable(c, "Family Business", Source.FamilyOccupationAndBusiness));

                //// Family Business
                //if (Source.FamilyBusiness != null)
                //{
                //    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                //    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Family Business");
                //    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                //    column.Item().Element(c => ComposeFamilyBusiness(c, Source.FamilyBusiness));
                //}

                if (!string.IsNullOrWhiteSpace(Source.Residence) || !string.IsNullOrWhiteSpace(Source.Address1) || !string.IsNullOrWhiteSpace(Source.Address2))
                {
                    // Divider
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    column.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text("Residence");
                    column.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                    // Group the heading and text within a Row to ensure they stay together
                    column.Item().Element(container =>
                    {
                        container
                            .Row(row =>
                            {
                                row.RelativeItem().Column(col =>
                                {
                                    // Heading
                                    //col.Item()
                                    //    .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a"))
                                    //    .Text("Residence");

                                    // Personal information
                                    col.Item()
                                        .DefaultTextStyle(s => s.FontSize(12))
                                        .PaddingHorizontal(10)
                                        //.Text(Source.Residence);
                                        .Text(text =>
                                            {
                                                if (!string.IsNullOrWhiteSpace(Source.Residence))
                                                {
                                                    text.Line(Source.Residence);
                                                }
                                                if (!string.IsNullOrWhiteSpace(Source.Address1))
                                                {
                                                    text.Line(Source.Address1);
                                                }
                                                if (!string.IsNullOrWhiteSpace(Source.Address2))
                                                {
                                                    text.Line(Source.Address2);
                                                }
                                               

                                            });


                                });
                                
                            });

                    });
                }

                column.Item().PageBreak();

                column.Item().Element(container =>
                {
                    container
                    .PaddingTop(30)
                    .Column(col =>
                    {
                        // Set the base font size
                        col.Item().DefaultTextStyle(s => s.FontSize(11)).Text(text =>
                        {
                            // DISCLAIMER header
                            text.Span("DISCLAIMER : ").Bold();
                            text.Line("");

                            // DISCLAIMER content
                            text.Span("THE DETAILS IN THIS PROFILE HAVE BEEN PROVIDED BY THE CLIENT. SUBHLAGAN IS NOT RESPONSIBLE FOR THE ACCURACY OR AUTHENTICITY OF ANY INFORMATION SHARED BY MEMBERS. PLEASE REVIEW THE PROFILE CAREFULLY BEFORE PROCEEDING.");
                            text.Line("");
                            text.Line("");

                            // IMPORTANT header
                            text.Span("IMPORTANT : ").Bold();
                            text.Line("");

                            // IMPORTANT content
                            text.Span("THE PROFILE SENT TO YOU ARE VERY PRIVATE, WE WOULD REQUEST YOU NOT TO SHARE THESE PROFILES WITH ANY OTHER PERSON AS IT WOULD LEAD TO A DATA BREACHING OF AN INDIVIDUAL. WE HOPE YOU UNDERSTAND AND RESPECT THE SENSITIVITY OF THIS MATTER.");
                        });
                    });
                });

            });
        }

        protected void ComposeProfileImages(IContainer container)
        {
            const float imageSize = 250;

            container
                .Padding(15)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                    });

                    for (int i = 0; i < Source.PicturePaths.Count; i++)
                    {
                        var path = Source.PicturePaths[i];

                        var cell = table.Cell();

                        // If there are exactly 2 images, make each cell full-width (forcing a new row)
                        if (Source.PicturePaths.Count == 2)
                        {
                            cell.ColumnSpan(2);
                        }
                        // Else, for odd number of images, make the last image full-width
                        else if (Source.PicturePaths.Count % 2 != 0 && i == Source.PicturePaths.Count - 1)
                        {
                            cell.ColumnSpan(2);
                        }

                        cell
                            .Padding(10)
                            .MinHeight(imageSize)
                            .MaxHeight(imageSize)
                            .AlignCenter()
                            .AlignMiddle()
                            .Border(1)
                            .ShowOnce()
                            .Element(img =>
                            {
                                // Image will scale within the cell without growing endlessly
                                img.Image(path, ImageScaling.FitArea);
                            });
                    }
                });
        }

        //protected void ComposeProfileImages_1(IContainer container)
        //{
        //    container
        //        .Padding(15)
        //        .Table(table =>
        //        {
        //            table.ColumnsDefinition(columns =>
        //            {
        //                columns.RelativeColumn();
        //                columns.RelativeColumn();
        //            });

        //            foreach (var path in Source.PicturePaths)
        //            {
        //                var cell = table.Cell();

        //                if (Source.PicturePaths.Count % 2 != 0 && Source.PicturePaths.Last() == path)
        //                    cell.ColumnSpan(2);

        //                cell.Padding(10).AlignCenter().AlignTop().Element(x => x.MaxHeight(200).Image(path, ImageScaling.FitHeight));
        //            }
        //        });
        //}

        protected virtual void ComposeFooter(IContainer container)
        {
            // Define your common style (font color, line height, etc.) at the container level
            container
                .ExtendHorizontal()
                .DefaultTextStyle(style => style
                    .FontColor("#95060a")   // Common text color
                    .FontSize(8))
                .Column(column =>
                {
                    // No extra spacing between rows
                    column.Spacing(0);

                    // Corporate Office Title (override font size and make it bold)
                    column.Item().AlignCenter().Text(text =>
                    {
                        text.DefaultTextStyle(style => style.Bold());
                        text.Span("Corporate Office");
                    });

                    // Address (use the common color & lineHeight, override font size)
                    column.Item().AlignCenter().Text(text =>
                    {
                        text.Span("Remi Bizcourt, F Wing, 2nd Floor, Plot No. 9, " +
                                  "Shah Industrial Estate, Near Yash Raj Studio, " +
                                  "Andheri(W), Mumbai - 400058, India.");
                    });

                    // Tel and Email in one line (still uses default color & lineHeight)
                    column.Item().AlignCenter().Text(text =>
                    {
                        text.Span("Tel: 022-4060 1111  Email: ");
                        text.Hyperlink("<EMAIL>", "<EMAIL>");
                    });

                    // Locations
                    column.Item().AlignCenter().Text(text =>
                    {
                        text.DefaultTextStyle(style => style.Bold());
                        text.Span("Wadala • Kolkata • Jaipur • Delhi");
                    });

                    // Logo
                    var fileName = "isologo.png";
                    var filePath = Path.Combine(GetSubhLaganPdfImagesPath(), fileName);
                    column.Item().AlignCenter().Element(x => x
                        .Width(30)
                        .Image(filePath));

                    // old style 
                    //column.Item().Background("#95060a").PaddingVertical(5).MaxHeight(30).AlignCenter().Text(text =>
                    //{
                    //    text.DefaultTextStyle(style => style.FontSize(7).FontColor("#ffffff").Bold());
                    //    text.Hyperlink("www.subhlagan.com", "https://www.subhlagan.com");
                    //    text.Span("   ");
                    //    text.CurrentPageNumber().Format(p => $"- {p} -");
                    //});

                    column
                        .Item()
                        .ExtendHorizontal()
                        .Padding(5)
                        .Row(row =>
                        {
                            // Left: subhlagan.com hyperlink
                            row.RelativeItem().AlignLeft().Text(txt =>
                            {
                                txt.DefaultTextStyle(s => s.FontSize(7).FontColor("#95060a").Bold());
                                txt.Hyperlink("www.subhlagan.com", "https://www.subhlagan.com");
                            });

                            // Right: page number
                            //row.RelativeItem().AlignRight().Text(t =>
                            //{
                            //    t.DefaultTextStyle(s => s.FontColor("#95060a").FontSize(7));
                            //    t.CurrentPageNumber().Format(p => $"- {p} -");
                            //});

                            // Right: User-friendly page numbering format
                            row.RelativeItem().AlignRight().Text(t =>
                            {
                                t.DefaultTextStyle(s => s.FontColor("#95060a").FontSize(8));

                                // New page format: "Page X of Y"
                                t.Span("Page ").SemiBold();
                                t.CurrentPageNumber().SemiBold();
                                t.Span(" of ").SemiBold();
                                t.TotalPages().SemiBold();
                            });
                        });
                });
        }

        protected void ComposeProfileOverview(IContainer container, BioDataSource profile)
        {
            if (profile == null)
                return;

            // Wrap all elements in a single column container to allow multiple children.
            container.Column(col =>
            {
                // Profile details table with a left cell for the label and a right cell for the value.
                col.Item().Table(table =>
                {
                    // Helper for cell styling
                    static IContainer CellStyle(IContainer cell, bool isLabel = false)
                    {
                        return cell.DefaultTextStyle(x => isLabel
                                ? x.FontSize(12).SemiBold()
                                : x.FontSize(12))
                            //.PaddingVertical(3)
                            .PaddingHorizontal(isLabel ? 10 : 15);
                    }

                    // Define the table columns: fixed for label, relative for value.
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(160);
                        columns.RelativeColumn();
                    });

                    // Helper to add a row if the value is not empty.
                    void AddRow(string label, string value)
                    {
                        if (!string.IsNullOrWhiteSpace(value))
                        {
                            table.Cell().Element(c => CellStyle(c, true)).Text(label);
                            table.Cell().Element(c => CellStyle(c)).Text(value);
                        }
                    }

                    // Add each profile field as a row in the table.
                    AddRow("Full Name", profile.FullName);
                    AddRow("Marital Status", profile.MaritalStatus);
                    AddRow("Date of Birth", profile.DateOfBirth);
                    AddRow("Time of Birth", profile.TimeOfBirth);
                    AddRow("Birth Place", profile.BirthPlace);
                    AddRow("Hobbies", profile.Hobbies);
                    AddRow("Height", profile.Height);
                    AddRow("Weight", profile.Weight);
                    AddRow("Blood Group", profile.MedicalHistory?.BloodGroup.GetDisplayName(true) ?? string.Empty);
                    AddRow("Skin Tone", profile.SkinTone);
                    AddRow("Body Type", profile.BodyType);
                    AddRow("Zodiac Sign", profile.ZodiacSign);
                    //AddRow("Complexion", profile.Complexion);
                    AddRow("Community", profile.Community);
                    AddRow("Gotra/Sakha", profile.GotraSakha);
                    AddRow("Food Habits", profile.FoodHabits);
                    AddRow("Present City", profile.PresentCity);
                    AddRow("Native Place", profile.NativePlace);
                });

            });
        }

        //protected void ComposeProfileOverview_1(IContainer container, BioDataSource profile)
        //{
        //    // A simple helper to add a row only if the value is not empty
        //    void AddRow(ColumnDescriptor col, string label, string value)
        //    {
        //        if (!string.IsNullOrWhiteSpace(value))
        //        {
        //            col.Item().Text(t =>
        //            {
        //                t.DefaultTextStyle(s => s.FontSize(10)); // base font size
        //                t.Span($"{label}: ").Bold();
        //                t.Span(value);
        //            });
        //        }
        //    }

        //    container.Column(column =>
        //    {
        //        // Create spacing between items in this column
        //        column.Spacing(2);

        //        // 3) A row with two columns for the actual data
        //        column.Item().Row(row =>
        //        {
        //            // Left Column
        //            row.RelativeItem().Column(col =>
        //            {
        //                col.Spacing(5);

        //                // Add whichever fields are most important to display on left
        //                AddRow(col, "Full Name", profile.FullName);
        //                AddRow(col, "Marital Status", profile.MaritalStatus);
        //                AddRow(col, "Date of Birth", profile.DateOfBirth);
        //                AddRow(col, "Time of Birth", profile.TimeOfBirth);
        //                AddRow(col, "Birth Place", profile.BirthPlace);
        //                AddRow(col, "Hobbies", profile.Hobbies);
        //                AddRow(col, "Height", profile.Height);
        //                AddRow(col, "Weight", profile.Weight);
        //                AddRow(col, "Blood Group", profile.MedicalHistory?.BloodGroup.GetDisplayName(true) ?? string.Empty);
        //                AddRow(col, "Skin Tone", profile.SkinTone);
        //                AddRow(col, "Body Type", profile.BodyType);
        //                AddRow(col, "Zodiac Sign", profile.ZodiacSign);
        //                //AddRow(col, "Complexion", profile.Complexion);
        //                AddRow(col, "Community", profile.Community);
        //                AddRow(col, "Gotra/Sakha", profile.GotraSakha);
        //                AddRow(col, "Food Habits", profile.FoodHabits);
        //                AddRow(col, "Present City", profile.PresentCity);
        //                AddRow(col, "Native Place", profile.NativePlace);
        //            });

        //            // Right Column
        //            //row.RelativeItem().Column(col =>
        //            //{
        //            //    col.Spacing(5);

        //            //    // Additional fields on right
        //            //    AddRow(col, "Height", profile.Height);
        //            //    AddRow(col, "Complexion", profile.Complexion);
        //            //    AddRow(col, "Community", profile.Community);
        //            //    AddRow(col, "Gotra/Sakha", profile.GotraSakha);
        //            //    AddRow(col, "Present City", profile.PresentCity);
        //            //});
        //        });

        //        if (Source.OccupationAndBusiness != null)
        //            column.Item().Element(c => ComposeOccupationAndBusiness(c, "Occupation", Source.OccupationAndBusiness, column));


        //        //if (!string.IsNullOrWhiteSpace(Source.OccupationAndBusiness.BusinessDetails))
        //        //{
        //        //    // Group the heading and text within a Row to ensure they stay together
        //        //    column.Item().Element(container =>
        //        //    {
        //        //        container
        //        //            .Row(row =>
        //        //            {
        //        //                row.RelativeItem().Column(col =>
        //        //                {
        //        //                    // Heading
        //        //                    col.Item()
        //        //                        .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a"))
        //        //                        .Text("Occupation");

        //        //                    col.Item().Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.OccupationAndBusiness.BusinessDetails));
        //        //                });
        //        //            });
        //        //    });
        //        //}
        //        // 4) Personal Information (optional big text block)
        //        //if (!string.IsNullOrWhiteSpace(profile.PersonalInformation))
        //        //{
        //        //    // A little spacing before a new “section”
        //        //    //column.Item().Height(10);

        //        //    // Another subtle heading
        //        //    column.Item().PaddingTop(10)
        //        //          .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a")).Text("About Me");

        //        //    column.Item()
        //        //          .DefaultTextStyle(s => s.FontSize(10)).Text(profile.PersonalInformation);
        //        //}

        //        if (!string.IsNullOrWhiteSpace(profile.AboutMe))
        //        {
        //            // Group the heading and text within a Row to ensure they stay together
        //            column.Item().Element(container =>
        //            {
        //                container
        //                    .Row(row =>
        //                    {
        //                        row.RelativeItem().Column(col =>
        //                        {
        //                            // Heading
        //                            col.Item()
        //                                .DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a"))
        //                                .Text("About Me");

        //                            // Personal information
        //                            //col.Item()
        //                            //    .DefaultTextStyle(s => s.FontSize(10))
        //                            //    .Text(profile.PersonalInformation);

        //                            col.Item().Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, profile.AboutMe));
        //                        });
        //                    });
        //            });
        //        }
        //    });
        //}

        protected void ComposeProfileDetailsTable(IContainer container, BioDataSource profile)
        {
            container.Table(table =>
            {
                static IContainer CellStyle(IContainer container, bool isLabel = false)
                {
                    return container
                        .DefaultTextStyle(x =>
                        {
                            return isLabel ? x.FontSize(10).SemiBold() : x.FontSize(10);
                        })
                        .PaddingVertical(3)
                        .PaddingHorizontal(isLabel ? 10 : 15);
                }

                static void AddRowWithDelimiter(TableDescriptor table, string label, string value)
                {
                    if (string.IsNullOrWhiteSpace(value)) return; // Skip rows with empty values

                    table.Cell().Element(c => CellStyle(c, true)).Text(label);
                    table.Cell().Element(c => CellStyle(c)).Text($"{value}");
                }

                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(150); // Label column
                    columns.RelativeColumn();   // Value column
                });

                // AddRowWithDelimiter(table, "Full Name", profile.FullName);
                AddRowWithDelimiter(table, "Date of Birth", profile.DateOfBirth);
                AddRowWithDelimiter(table, "Time of Birth", profile.TimeOfBirth);
                AddRowWithDelimiter(table, "Birth Place", profile.BirthPlace);
                AddRowWithDelimiter(table, "Height", profile.Height);
                AddRowWithDelimiter(table, "Weight", profile.Weight);
                AddRowWithDelimiter(table, "Skin Tone", profile.SkinTone);
                AddRowWithDelimiter(table, "Body Type", profile.BodyType);
                //AddRowWithDelimiter(table, "Complexion", profile.Complexion);
                AddRowWithDelimiter(table, "Zodiac Sign", profile.ZodiacSign);
                AddRowWithDelimiter(table, "Community", profile.Community);
                AddRowWithDelimiter(table, "Gotra/Sakha", profile.GotraSakha);
                AddRowWithDelimiter(table, "Food Habits", profile.FoodHabits);
                AddRowWithDelimiter(table, "Present City", profile.PresentCity);
                AddRowWithDelimiter(table, "Native Place", profile.NativePlace);
                AddRowWithDelimiter(table, "Hobbies", profile.Hobbies);
                AddRowWithDelimiter(table, "Personal Information", profile.AboutMe);

                //if (profile.Educations?.Any() == true)
                //{
                //    table.Cell().Element(c => CellStyle(c, true)).Text("Education");
                //    table.Cell().Element(c => CellStyle(c)).Element(c => ComposeEducations(c, profile.Educations));
                //}
                //if (profile.FamilyMembers?.Any() == true)
                //{
                //    foreach (var member in profile.FamilyMembers)
                //    {
                //        var hasData = HasFamilyMemberData(member);
                //        if (!hasData)
                //            continue;

                //        table.Cell().Element(c => CellStyle(c, true)).Text(member.Relation);
                //        table.Cell().Element(c => CellStyle(c)).Element(c => ComposeFamilyMember(c, member));
                //    }
                //}
            });
        }

        protected void ComposeEducations(IContainer container, List<EducationItem> educations)
        {
            // Helper function to add styled text
            void AddStyledText(ColumnDescriptor column, string text, bool isItalic = false, int fontSize = 12)
            {
                if (!string.IsNullOrEmpty(text))
                {
                    column.Item()
                          .DefaultTextStyle(style =>
                              isItalic ? style.FontSize(fontSize).Italic() : style.FontSize(fontSize))
                          .PaddingHorizontal(10)
                          .Text(text);
                }
            }

            container.Column(column =>
            {
                column.Spacing(2);

                foreach (var education in educations)
                {
                    // Skip if all fields are empty or whitespace
                    if (//string.IsNullOrWhiteSpace(education.Qualification) &&
                        string.IsNullOrWhiteSpace(education.Remarks) &&
                        string.IsNullOrWhiteSpace(education.College) &&
                        string.IsNullOrWhiteSpace(education.Location))
                    {
                        continue;
                    }

                    //var remarkAdded = false;
                    //// Combine Qualification and College with Year if available
                    var details = string.Empty;
                    //if (!string.IsNullOrWhiteSpace(education.Qualification))
                    //{
                    //    details += education.Qualification;
                    //}
                    //else
                    //{
                    //    details += education.Remarks;
                    //    remarkAdded = true;
                    //}

                    if (!string.IsNullOrWhiteSpace(education.Remarks))
                        details += education.Remarks;

                    if (!string.IsNullOrWhiteSpace(education.College))
                        details += string.IsNullOrEmpty(details) ? education.College : $", {education.College}";

                    if (!string.IsNullOrWhiteSpace(education.Year))
                        details += $" ({education.Year}) ";

                    //if(!remarkAdded && !string.IsNullOrWhiteSpace(education.Remarks))
                    //    details += education.Remarks;

                    AddStyledText(column, details);

                    // Location (in italic style)
                    if (!string.IsNullOrEmpty(education.Location))
                    {
                        AddStyledText(column, $"Location: {education.Location}", isItalic: false, fontSize: 12);
                    }
                }

                if (!string.IsNullOrWhiteSpace(Source.EducationAdditionalInfo))
                {
                    // Group the heading and text within a Row to ensure they stay together
                    column.Item().Element(container =>
                    {
                        container
                            .Row(row =>
                            {
                                row.RelativeItem().Column(col =>
                                {
                                    // Heading
                                    col.Item()
                                        .DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a"))
                                        .Text("Additional Info");

                                    col.Item().Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, Source.EducationAdditionalInfo));
                                });
                            });
                    });
                }
            });
        }

        protected void ComposeOccupationAndBusiness(IContainer container, string title, OccupationAndBusinessItem business, ColumnDescriptor column)
        {
            // Collect text lines in a list.
            var lines = new List<string>();

            // Firm Name and Nature of Business (inline)
            if (!string.IsNullOrWhiteSpace(business.FirmName) || !string.IsNullOrWhiteSpace(business.NatureOfBusiness))
            {
                var firmDetails = business.FirmName;
                if (!string.IsNullOrWhiteSpace(business.NatureOfBusiness))
                {
                    firmDetails += $" ({business.NatureOfBusiness})";
                }
                lines.Add(firmDetails);
            }

            // Occupation
            if (!string.IsNullOrWhiteSpace(business.Occupation))
            {
                lines.Add($"Occupation: {business.Occupation}");
            }

            // Annual Revenue
            if (business.AnnualRevenue > 0)
            {
                lines.Add($"Annual Revenue: ₹{business.AnnualRevenue:N2} Lacs");
            }

            // Contact Information
            if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite) ||
                !string.IsNullOrWhiteSpace(business.PhoneNumber) ||
                !string.IsNullOrWhiteSpace(business.Address))
            {
                // Add a blank line before contact info.
                lines.Add(string.Empty);
                lines.Add("Contact Information:");

                if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite))
                {
                    lines.Add($"Email/Website: {business.EmailOrWebsite}");
                }
                if (!string.IsNullOrWhiteSpace(business.PhoneNumber))
                {
                    lines.Add($"Phone: {business.PhoneNumber}");
                }
                if (!string.IsNullOrWhiteSpace(business.Address))
                {
                    lines.Add($"Address: {business.Address}");
                }
            }

            // Business Details
            //if (!string.IsNullOrWhiteSpace(business.BusinessDetails))
            //{
            //    lines.Add(string.Empty);
            //    lines.Add("Business Details:");
            //    lines.Add(business.BusinessDetails);
            //}

            // Only add the Row if there is at least one line.
            if (lines.Any() || !string.IsNullOrWhiteSpace(business.BusinessDetails))
            {
                container.Column(mainCol =>
                {
                    mainCol.Spacing(0);
                    mainCol.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);
                    mainCol.Item().DefaultTextStyle(s => s.FontSize(12).Bold().FontColor("#95060a")).Text(title);
                    mainCol.Item().BorderBottom(1).BorderColor("#DDDDDD").Height(1);

                    if (lines.Any())
                    {
                        mainCol.Item()
                               .DefaultTextStyle(s => s.FontSize(12).LineHeight(1f))
                               .Padding(0)
                               .PaddingBottom(0)
                               .Text(text =>
                               {
                                   foreach (var line in lines)
                                   {
                                       text.Line(line);
                                   }
                               });
                    }

                    if (!string.IsNullOrWhiteSpace(business.BusinessDetails))
                    {
                        mainCol.Item()
                               .PaddingTop(lines.Any() ? -15 : 0)
                               .PaddingHorizontal(0)
                               .Element(async c => await HtmlToPdfRenderer.ComposeHtmlAsync(c, business.BusinessDetails));
                    }
                });


            }
        }

        //protected void ComposeOccupationAndBusiness_1(IContainer container, string title, OccupationAndBusinessItem business)
        //{
        //    container.Row(row =>
        //    {
        //        row.RelativeItem().Column(col =>
        //        {
        //            // Heading
        //            col.Item()
        //                .DefaultTextStyle(s => s.FontSize(11).Bold().FontColor("#95060a"))
        //                .Text(title);

        //            // Personal information
        //            col.Item()
        //                .DefaultTextStyle(s => s.FontSize(10))
        //                .Text(text =>
        //                {
        //                    // Firm Name and Nature of Business (inline)
        //                    if (!string.IsNullOrWhiteSpace(business.FirmName) || !string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //                    {
        //                        var firmDetails = business.FirmName;

        //                        if (!string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //                        {
        //                            firmDetails += $" ({business.NatureOfBusiness})";
        //                        }

        //                        text.Line(firmDetails);
        //                    }

        //                    // Occupation
        //                    if (!string.IsNullOrWhiteSpace(business.Occupation))
        //                    {
        //                        text.Line($"Occupation: {business.Occupation}");
        //                    }

        //                    // Annual Revenue
        //                    if (business.AnnualRevenue > 0)
        //                    {
        //                        text.Line($"Annual Revenue: ₹{business.AnnualRevenue:N2} Lacs");
        //                    }

        //                    // Contact Information
        //                    if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite) ||
        //                        !string.IsNullOrWhiteSpace(business.PhoneNumber) ||
        //                        !string.IsNullOrWhiteSpace(business.Address))
        //                    {
        //                        text.Line("");
        //                        text.Line("Contact Information:");

        //                        if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite))
        //                        {
        //                            text.Line($"Email/Website: {business.EmailOrWebsite}");
        //                        }
        //                        if (!string.IsNullOrWhiteSpace(business.PhoneNumber))
        //                        {
        //                            text.Line($"Phone: {business.PhoneNumber}");
        //                        }
        //                        if (!string.IsNullOrWhiteSpace(business.Address))
        //                        {
        //                            text.Line($"Address: {business.Address}");
        //                        }
        //                    }


        //                    // Business Details
        //                    if (!string.IsNullOrWhiteSpace(business.BusinessDetails))
        //                    {
        //                        text.Line("");
        //                        text.Line("Business Details:");
        //                        text.Line(business.BusinessDetails);
        //                    }
        //                });
        //        });
        //    });

        //    //container.Table(table =>
        //    //{
        //    //    // Helper function to define cell styling
        //    //    static IContainer CellStyle(IContainer cell, bool isLabel = false)
        //    //    {
        //    //        return cell.DefaultTextStyle(x => isLabel
        //    //            ? x.FontSize(10).SemiBold()
        //    //            : x.FontSize(9))
        //    //            .PaddingVertical(3)
        //    //            .PaddingHorizontal(isLabel ? 10 : 15);
        //    //    }

        //    //    // Define table columns: one for labels and another for details
        //    //    table.ColumnsDefinition(columns =>
        //    //    {
        //    //        columns.ConstantColumn(120); // Fixed width for the label column
        //    //        columns.RelativeColumn();    // Flexible width for the details column
        //    //    });

        //    //    // Add Business (Family) label in the first column
        //    //    table.Cell().Element(c => CellStyle(c, true))
        //    //        .Text(title);

        //    //    // Add business details in the second column
        //    //    table.Cell().Element(c => CellStyle(c, false))
        //    //        .Text(text =>
        //    //        {
        //    //            // Firm Name and Nature of Business (inline)
        //    //            if (!string.IsNullOrWhiteSpace(business.FirmName) || !string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //    //            {
        //    //                var firmDetails = business.FirmName;

        //    //                if (!string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //    //                {
        //    //                    firmDetails += $" ({business.NatureOfBusiness})";
        //    //                }

        //    //                text.Line(firmDetails);
        //    //            }

        //    //            // Occupation
        //    //            if (!string.IsNullOrWhiteSpace(business.Occupation))
        //    //            {
        //    //                text.Line($"Occupation: {business.Occupation}");
        //    //            }

        //    //            // Annual Revenue
        //    //            if (business.AnnualRevenue > 0)
        //    //            {
        //    //                text.Line($"Annual Revenue: ₹{business.AnnualRevenue:N2} Lacs");
        //    //            }

        //    //            // Contact Information
        //    //            if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite) ||
        //    //                !string.IsNullOrWhiteSpace(business.PhoneNumber) ||
        //    //                !string.IsNullOrWhiteSpace(business.Address))
        //    //            {
        //    //                text.Line("");
        //    //                text.Line("Contact Information:");

        //    //                if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite))
        //    //                {
        //    //                    text.Line($"Email/Website: {business.EmailOrWebsite}");
        //    //                }
        //    //                if (!string.IsNullOrWhiteSpace(business.PhoneNumber))
        //    //                {
        //    //                    text.Line($"Phone: {business.PhoneNumber}");
        //    //                }
        //    //                if (!string.IsNullOrWhiteSpace(business.Address))
        //    //                {
        //    //                    text.Line($"Address: {business.Address}");
        //    //                }
        //    //            }


        //    //            // Business Details
        //    //            if (!string.IsNullOrWhiteSpace(business.BusinessDetails))
        //    //            {
        //    //                text.Line("");
        //    //                text.Line("Business Details:");
        //    //                text.Line(business.BusinessDetails);
        //    //            }
        //    //        });
        //    //});
        //}

        //protected void ComposeOccupationAndBusinessTable(IContainer container, string title, OccupationAndBusinessItem business)
        //{
        //    // Collect text lines in a list.
        //    var lines = new List<string>();

        //    // Firm Name and Nature of Business (inline)
        //    if (!string.IsNullOrWhiteSpace(business.FirmName) || !string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //    {
        //        var firmDetails = business.FirmName;
        //        if (!string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //        {
        //            firmDetails += $" ({business.NatureOfBusiness})";
        //        }
        //        lines.Add(firmDetails);
        //    }

        //    // Occupation
        //    if (!string.IsNullOrWhiteSpace(business.Occupation))
        //    {
        //        lines.Add($"Occupation: {business.Occupation}");
        //    }

        //    // Annual Revenue
        //    if (business.AnnualRevenue > 0)
        //    {
        //        lines.Add($"Annual Revenue: ₹{business.AnnualRevenue:N2} Lacs");
        //    }

        //    // Contact Information
        //    if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite) ||
        //        !string.IsNullOrWhiteSpace(business.PhoneNumber) ||
        //        !string.IsNullOrWhiteSpace(business.Address))
        //    {
        //        lines.Add(string.Empty);
        //        lines.Add("Contact Information:");

        //        if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite))
        //        {
        //            lines.Add($"Email/Website: {business.EmailOrWebsite}");
        //        }
        //        if (!string.IsNullOrWhiteSpace(business.PhoneNumber))
        //        {
        //            lines.Add($"Phone: {business.PhoneNumber}");
        //        }
        //        if (!string.IsNullOrWhiteSpace(business.Address))
        //        {
        //            lines.Add($"Address: {business.Address}");
        //        }
        //    }

        //    // Business Details
        //    //if (!string.IsNullOrWhiteSpace(business.BusinessDetails))
        //    //{
        //    //    lines.Add(string.Empty);
        //    //    lines.Add("Business Details:");
        //    //    lines.Add(business.BusinessDetails);
        //    //}

        //    // Only create the table if there is at least one text line.
        //    if (!lines.Any())
        //        return;

        //    container.Table(table =>
        //    {
        //        // Helper function to define cell styling
        //        static IContainer CellStyle(IContainer cell, bool isLabel = false)
        //        {
        //            return cell.DefaultTextStyle(x => isLabel
        //                ? x.FontSize(10).SemiBold()
        //                : x.FontSize(9))
        //                .PaddingVertical(3)
        //                .PaddingHorizontal(isLabel ? 10 : 15);
        //        }

        //        // Define table columns: one for labels and another for details
        //        table.ColumnsDefinition(columns =>
        //        {
        //            columns.ConstantColumn(120); // Fixed width for the label column
        //            columns.RelativeColumn();    // Flexible width for the details column
        //        });

        //        // Add Business label in the first column
        //        table.Cell().Element(c => CellStyle(c, true))
        //            .Text(title);

        //        // Add business details in the second column
        //        table.Cell().Element(c => CellStyle(c, false))
        //            .Text(text =>
        //            {
        //                foreach (var line in lines)
        //                {
        //                    text.Line(line);
        //                }
        //            });
        //    });
        //}

        //protected void ComposeOccupationAndBusinessTable_1(IContainer container, string title, OccupationAndBusinessItem business)
        //{
        //    container.Table(table =>
        //    {
        //        // Helper function to define cell styling
        //        static IContainer CellStyle(IContainer cell, bool isLabel = false)
        //        {
        //            return cell.DefaultTextStyle(x => isLabel
        //                ? x.FontSize(10).SemiBold()
        //                : x.FontSize(9))
        //                .PaddingVertical(3)
        //                .PaddingHorizontal(isLabel ? 10 : 15);
        //        }

        //        // Define table columns: one for labels and another for details
        //        table.ColumnsDefinition(columns =>
        //        {
        //            columns.ConstantColumn(120); // Fixed width for the label column
        //            columns.RelativeColumn();    // Flexible width for the details column
        //        });

        //        // Add Business (Family) label in the first column
        //        table.Cell().Element(c => CellStyle(c, true))
        //            .Text(title);

        //        // Add business details in the second column
        //        table.Cell().Element(c => CellStyle(c, false))
        //            .Text(text =>
        //            {
        //                // Firm Name and Nature of Business (inline)
        //                if (!string.IsNullOrWhiteSpace(business.FirmName) || !string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //                {
        //                    var firmDetails = business.FirmName;

        //                    if (!string.IsNullOrWhiteSpace(business.NatureOfBusiness))
        //                    {
        //                        firmDetails += $" ({business.NatureOfBusiness})";
        //                    }

        //                    text.Line(firmDetails);
        //                }

        //                // Occupation
        //                if (!string.IsNullOrWhiteSpace(business.Occupation))
        //                {
        //                    text.Line($"Occupation: {business.Occupation}");
        //                }

        //                // Annual Revenue
        //                if (business.AnnualRevenue > 0)
        //                {
        //                    text.Line($"Annual Revenue: ₹{business.AnnualRevenue:N2} Lacs");
        //                }

        //                // Contact Information
        //                if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite) ||
        //                    !string.IsNullOrWhiteSpace(business.PhoneNumber) ||
        //                    !string.IsNullOrWhiteSpace(business.Address))
        //                {
        //                    text.Line("");
        //                    text.Line("Contact Information:");

        //                    if (!string.IsNullOrWhiteSpace(business.EmailOrWebsite))
        //                    {
        //                        text.Line($"Email/Website: {business.EmailOrWebsite}");
        //                    }
        //                    if (!string.IsNullOrWhiteSpace(business.PhoneNumber))
        //                    {
        //                        text.Line($"Phone: {business.PhoneNumber}");
        //                    }
        //                    if (!string.IsNullOrWhiteSpace(business.Address))
        //                    {
        //                        text.Line($"Address: {business.Address}");
        //                    }
        //                }


        //                // Business Details
        //                if (!string.IsNullOrWhiteSpace(business.BusinessDetails))
        //                {
        //                    text.Line("");
        //                    text.Line("Business Details:");
        //                    text.Line(business.BusinessDetails);
        //                }
        //            });
        //    });
        //}

        protected void ComposeFamilyInfo(IContainer container, Dictionary<string, List<FamilyMemberItem>> familyMembers)
        {
            // Safety check
            if (familyMembers == null || !familyMembers.Any())
                return;

            container.Table(table =>
            {
                // Helper function to define cell styling
                static IContainer CellStyle(IContainer cell, bool isLabel = false)
                {
                    return cell.DefaultTextStyle(x => isLabel
                        ? x.FontSize(12).SemiBold()
                        : x.FontSize(12))
                        //.PaddingVertical(3)
                        .PaddingHorizontal(isLabel ? 10 : 15);
                }

                // Columns: one for "Relation" label, one for the family member details
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(160); // left column for the relation
                    columns.RelativeColumn();    // right column for details
                });

                foreach (var familyRelationGroup in familyMembers)
                {
                    var hasRelationNameBeenAdded = false;
                    // Loop over each family member
                    foreach (var member in familyRelationGroup.Value)
                    {
                        // Only render if the member has data
                        if (!HasFamilyMemberData(member))
                        {
                            continue;
                        }

                        if (!hasRelationNameBeenAdded)
                        {
                            table.Cell()
                                 .Element(c => CellStyle(c, true))
                                 .ShowOnce()                       // ← only render this cell on its first occurrence
                                 .Text(familyRelationGroup.Key);
                            hasRelationNameBeenAdded = true;
                        }
                        else
                        {
                            table.Cell()
                                 .Element(c => CellStyle(c, true))
                                 .Text(string.Empty);
                            hasRelationNameBeenAdded = true;
                        }

                        // Right cell: details from ComposeFamilyMember
                        table.Cell().Element(c => CellStyle(c)).Element(c => ComposeFamilyMember(c, member));
                    }
                }
            });
        }

        protected void ComposeFamilyMember(IContainer container, FamilyMemberItem member)
        {
            if (member == null) return; // Null safety check

            container.Column(column =>
            {
                column.Spacing(2);

                // Helper function to add styled text
                void AddStyledTextIfNotEmpty(string text, string prefix = "")
                {
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        column.Item()
                              .DefaultTextStyle(style => style.FontSize(12))
                              .Text($"{prefix}{text}");
                    }
                }

                // Helper function to ensure prefix is added
                string EnsurePrefix(string name, string prefix)
                {
                    if (string.IsNullOrWhiteSpace(name)) return string.Empty;

                    // Check if name already starts with the prefix
                    return name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) ? name : $"{prefix} {name}";
                }

                // Common logic for most relation types
                void ComposeCommonDetails(string prefix)
                {
                    var nameWithPrefix = EnsurePrefix(member.Name, prefix);
                    AddStyledTextIfNotEmpty(nameWithPrefix);

                    AddStyledTextIfNotEmpty(member.WorkStatus, "Work Status: ");
                    AddStyledTextIfNotEmpty(member.Occupation, "Occupation: ");
                    AddStyledTextIfNotEmpty(member.Education, "Education: ");
                    //AddStyledTextIfNotEmpty(member.AdditionalInfo, "Additional Info: ");
                    AddStyledTextIfNotEmpty(member.AdditionalInfo, "");

                }

                switch (member.RelationType)
                {
                    case RelationType.Father:
                        ComposeCommonDetails(member.Prefix!=""? member.Prefix : "Sri.");
                        break;

                    case RelationType.Mother:
                        ComposeCommonDetails(member.Prefix != "" ? member.Prefix : "Smt.");
                        break;

                    case RelationType.Brother:
                    case RelationType.Sister:
                        string siblingPrefix = member.RelationType switch
                        {
                            RelationType.Brother => member.Prefix != "" ? member.Prefix : "Sri.",
                            RelationType.Sister => member.Prefix != "" ? member.Prefix : string.IsNullOrWhiteSpace(member.MarriedTo) ? "Kumari" : "Smt.",
                            _ => string.Empty,
                        };

                        //if (member.RelationType == RelationType.Brother)
                        //{
                        //    siblingPrefix = "Sri.";
                        //}
                        //else
                        //{
                        //    siblingPrefix = string.IsNullOrWhiteSpace(member.MarriedTo) ? "Kumari" : "Smt.";
                        //}

                        var siblingNameWithPrefix = EnsurePrefix(member.Name, siblingPrefix);
                        if (!string.IsNullOrEmpty(member.RelativeOrder))
                        {
                            siblingNameWithPrefix += $" ({member.RelativeOrder})";
                        }
                        AddStyledTextIfNotEmpty(siblingNameWithPrefix);

                        AddStyledTextIfNotEmpty(member.Education, "Education: ");
                        AddStyledTextIfNotEmpty(member.Occupation, "Occupation: ");
                        AddStyledTextIfNotEmpty(member.MarriedTo, "Married To: ");
                        //AddStyledTextIfNotEmpty(member.MarriedToSonOrDaughterOf, "Son/Daughter Of: ");
                        AddStyledTextIfNotEmpty(member.MarriedToSonOrDaughterOf, member.RelationType == RelationType.Brother ? "Daughter Of: " : "Son Of: ");

                        AddStyledTextIfNotEmpty(member.SpouseAddress, "Spouse's Address: ");
                        AddStyledTextIfNotEmpty(member.AdditionalInfo, "Additional Info: ");
                        break;

                    case RelationType.PaternalUncle:
                    case RelationType.MaternalUncle:
                    case RelationType.PaternalAunty:
                    case RelationType.MaternalAunty:
                        var uncleAuntyPrefix = member.RelationType == RelationType.PaternalUncle || member.RelationType == RelationType.MaternalUncle ? (member.Prefix != "" ? member.Prefix : "Sri.") : (member.Prefix != "" ? member.Prefix : "Smt.");
                        var uncleAuntyNameWithPrefix = EnsurePrefix(member.Name, uncleAuntyPrefix);
                        AddStyledTextIfNotEmpty(uncleAuntyNameWithPrefix);

                        AddStyledTextIfNotEmpty(member.Occupation, "Occupation: ");
                        AddStyledTextIfNotEmpty(member.Address, "Address: ");
                        AddStyledTextIfNotEmpty(member.MarriedTo, "Married To: ");
                        //AddStyledTextIfNotEmpty(member.MarriedToSonOrDaughterOf, "Son/Daughter Of: ");
                        AddStyledTextIfNotEmpty(member.MarriedToSonOrDaughterOf, member.RelationType == RelationType.PaternalUncle || member.RelationType == RelationType.MaternalUncle ? "Daughter Of: " : "Son Of: ");
                        AddStyledTextIfNotEmpty(member.SpouseAddress, "Spouse's Address: ");
                        AddStyledTextIfNotEmpty(member.AdditionalInfo, "Additional Info: ");
                        break;

                    case RelationType.PaternalGrandfather:
                    case RelationType.MaternalGrandfather:
                    case RelationType.PaternalGrandmother:
                    case RelationType.MaternalGrandmother:
                        var grandparentPrefix = member.RelationType == RelationType.PaternalGrandfather || member.RelationType == RelationType.MaternalGrandfather ? (member.Prefix != "" ? member.Prefix : "Sri.") : (member.Prefix != "" ? member.Prefix : "Smt.");
                        var grandparentNameWithPrefix = EnsurePrefix(member.Name, grandparentPrefix);
                        AddStyledTextIfNotEmpty(grandparentNameWithPrefix);

                        AddStyledTextIfNotEmpty(member.AdditionalInfo, "Additional Info: ");
                        break;

                    default:
                        var defaultNameWithPrefix = EnsurePrefix(member.Name, "Sri./Smt.");
                        AddStyledTextIfNotEmpty(defaultNameWithPrefix);
                        AddStyledTextIfNotEmpty(member.AdditionalInfo, "Additional Info: ");
                        break;
                }
            });
        }


        //protected void ComposeFamilyMember_1(IContainer container, FamilyMemberItem member)
        //{
        //    // Helper function to apply default text style
        //    void AddStyledText(ColumnDescriptor column, string text)
        //    {
        //        if (!string.IsNullOrWhiteSpace(text))
        //        {
        //            column.Item()
        //                  .DefaultTextStyle(style => style.FontSize(9))
        //                  .Text(text);
        //        }
        //    }

        //    container.Column(column =>
        //    {
        //        // Add some spacing between rows
        //        column.Spacing(2);

        //        switch (member.RelationType)
        //        {
        //            case RelationType.Father:
        //            case RelationType.Mother:
        //                // Prefix for Father or Mother
        //                string parentPrefix = member.RelationType == RelationType.Father ? "Sri." : "Smt.";
        //                string parentSuffix = member.RelationType == RelationType.Father ? "S/O" : "D/O";

        //                // Name with prefix
        //                AddStyledText(column, $"{parentPrefix} {member.Name}");

        //                // Occupation and Education
        //                var occupationInfo = string.Empty;
        //                if (!string.IsNullOrEmpty(member.Occupation))
        //                    occupationInfo += member.Occupation;
        //                if (!string.IsNullOrEmpty(member.Education))
        //                    occupationInfo += $" ({member.Education})";

        //                AddStyledText(column, occupationInfo);

        //                // Additional Information
        //                AddStyledText(column, member.AdditionalInfo);
        //                break;

        //            case RelationType.Brother:
        //            case RelationType.Sister:
        //                // Prefix based on gender
        //                string siblingPrefix = member.RelationType == RelationType.Brother ? "Sri." : "Smt.";
        //                string siblingPosition = !string.IsNullOrEmpty(member.RelativeOrder) ? $"({member.RelativeOrder})" : string.Empty;

        //                // Name with prefix and position
        //                AddStyledText(column, $"{siblingPrefix} {member.Name} {siblingPosition}");

        //                // Education
        //                AddStyledText(column, $"Education: {member.Education}");

        //                // Occupation
        //                AddStyledText(column, $"Occupation: {member.Occupation}");

        //                // Marriage Details
        //                AddStyledText(column, $"Married To: {member.MarriedTo}");
        //                AddStyledText(column, $"Son/Daughter Of: {member.MarriedToSonOrDaughterOf}");

        //                // Spouse's Address
        //                AddStyledText(column, $"Spouse's Address: {member.SpouseAddress}");

        //                // Additional Info
        //                AddStyledText(column, $"Additional Info: {member.AdditionalInfo}");
        //                break;

        //            case RelationType.PaternalUncle:
        //            case RelationType.PaternalAunty:
        //            case RelationType.MaternalUncle:
        //            case RelationType.MaternalAunty:
        //                // Prefix for Uncle/Aunty
        //                string uncleAuntyPrefix = member.RelationType == RelationType.PaternalUncle || member.RelationType == RelationType.MaternalUncle
        //                    ? "Sri."
        //                    : "Smt.";

        //                // Name with prefix
        //                AddStyledText(column, $"{uncleAuntyPrefix} {member.Name}");

        //                // Occupation
        //                AddStyledText(column, $"Occupation: {member.Occupation}");

        //                // Address
        //                AddStyledText(column, $"Address: {member.Address}");

        //                // Marriage Details
        //                AddStyledText(column, $"Married To: {member.MarriedTo}");
        //                AddStyledText(column, $"Son/Daughter Of: {member.MarriedToSonOrDaughterOf}");

        //                // Spouse's Address
        //                AddStyledText(column, $"Spouse's Address: {member.SpouseAddress}");

        //                // Additional Info
        //                AddStyledText(column, $"Additional Info: {member.AdditionalInfo}");
        //                break;

        //            case RelationType.PaternalGrandfather:
        //            case RelationType.MaternalGrandfather:
        //            case RelationType.PaternalGrandmother:
        //            case RelationType.MaternalGrandmother:
        //                // Prefix for Grandparents
        //                string grandparentPrefix = member.RelationType == RelationType.PaternalGrandfather || member.RelationType == RelationType.MaternalGrandfather
        //                    ? "Sri."
        //                    : "Smt.";

        //                // Name with prefix
        //                AddStyledText(column, $"{grandparentPrefix} {member.Name}");

        //                // Additional Information
        //                AddStyledText(column, member.AdditionalInfo);
        //                break;

        //            default:
        //                // Default handling for other relations
        //                AddStyledText(column, member.Name);
        //                AddStyledText(column, member.AdditionalInfo);
        //                break;
        //        }
        //    });
        //}

        //private void AddWatermark(SKCanvas canvas, Size size, string watermarkText, float offsetX, float offsetY, float rotation = -45, SKPaint paint = null)
        //{
        //    // Ensure paint has default settings if not provided
        //    paint ??= new SKPaint
        //    {
        //        Color = new SKColor(128, 128, 128, 50), // Light gray with transparency
        //        TextSize = 64,
        //        IsAntialias = true,
        //        Typeface = SKTypeface.FromFamilyName("Arial", SKFontStyle.Bold)
        //    };

        //    canvas.Save(); // Save the initial canvas state
        //    canvas.Translate(size.Width / 2 + offsetX, size.Height / 2 + offsetY); // Apply center offset
        //    canvas.RotateDegrees(rotation); // Rotate for diagonal placement
        //    canvas.DrawText(watermarkText, 0, 0, paint); // Draw the watermark
        //    canvas.Restore(); // Restore to the original canvas state
        //}

        #endregion

        #region Methods

        public override void Compose(IDocumentContainer container)
        {
            var titleStyle = DefaultStyle.FontSize(12).NormalWeight().FontFamily("Calibri");

            container
                .Page(page =>
                {
                    page.DefaultTextStyle(titleStyle);

                    if (Source.IsRightToLeft)
                        page.ContentFromRightToLeft();

                    page.Size(Source.PageSize);
                    page.Margin(20);
                    page.MarginBottom(20);
                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContentPhoto);
                    page.Footer().AlignCenter().Element(ComposeFooter);
                })
                .Page(page =>
                {
                    //var titleStyle = DefaultStyle.FontSize(10).NormalWeight();
                    page.DefaultTextStyle(titleStyle);

                    if (Source.IsRightToLeft)
                        page.ContentFromRightToLeft();

                    page.Size(Source.PageSize);
                    page.Margin(20);
                    page.MarginBottom(20);

                    // Add a single watermark image in the background
                    page.Background().Canvas((canvas, size) =>
                    {
                        // 1) Load watermark image
                        var fileName = "ShubhLaganWaterMark.png";
                        var filePath = Path.Combine(GetSubhLaganPdfImagesPath(), fileName);
                        using var data = SKData.Create(filePath);
                        using var skImage = SKImage.FromEncodedData(data);

                        // 2) Scale the image (e.g., 50% of page width)
                        float targetWidth = size.Width * 0.5f;
                        float aspectRatio = (float)skImage.Height / skImage.Width;
                        float targetHeight = targetWidth * aspectRatio;

                        // 3) Transform the canvas to center and rotate
                        canvas.Save();

                        // Move origin to the center
                        canvas.Translate(size.Width / 2, size.Height / 2);
                        // Rotate by -45 degrees diagonally
                        canvas.RotateDegrees(0);

                        // 4) Define the rectangle to draw the image (centered around 0,0)
                        var rect = new SKRect(
                            -targetWidth / 2,
                            -targetHeight / 2,
                             targetWidth / 2,
                             targetHeight / 2
                        );

                        // 5) Set optional paint for transparency
                        using var paint = new SKPaint
                        {
                            Color = new SKColor(255, 255, 255, 32), //128 - 50% transparent white
                            IsAntialias = true
                        };

                        // 6) Draw the image
                        canvas.DrawImage(skImage, rect, paint);

                        // Restore state
                        canvas.Restore();
                    });

                    //Add watermark in the background
                    //page.Background().Canvas((canvas, size) =>
                    //{
                    //    var paint = new SKPaint
                    //    {
                    //        Color = new SKColor(128, 128, 128, 30), // Light gray, lighter transparency
                    //        TextSize = size.Width * 0.05f,          // Text size is 5% of page width
                    //        IsAntialias = true,
                    //        Typeface = SKTypeface.FromFamilyName("Montserrat", SKFontStyle.Bold)
                    //    };

                    //    var watermarkText = "SUBHLAGAN.COM PVT LTD";

                    //    // Increase spacing to 2x the watermark's approximate width
                    //    float textApproxWidth = paint.MeasureText(watermarkText);
                    //    float horizontalSpacing = textApproxWidth * 6.0f;
                    //    float verticalSpacing = 160 + (paint.TextSize * 4.0f); // 2x the text height

                    //    // Loop to place watermarks in a grid
                    //    for (float x = -horizontalSpacing; x < size.Width + horizontalSpacing; x += horizontalSpacing)
                    //    {
                    //        for (float y = -verticalSpacing; y < size.Height + verticalSpacing; y += verticalSpacing)
                    //        {
                    //            canvas.Save(); // Save the canvas state
                    //            canvas.Translate((size.Width * 0.3f) + x, y); // Move to the grid position
                    //            canvas.RotateDegrees(-45); // Rotate for diagonal placement
                    //            canvas.DrawText(watermarkText, 0, 0, paint); // Draw the text
                    //            canvas.Restore(); // Restore the canvas state
                    //        }
                    //    }

                    //});

                    page.Header().Element(ComposeHeader);
                    page.Content().Element(ComposeContent);
                    page.Footer().AlignCenter().Element(ComposeFooter);
                });
        }

        #endregion
    }

}
