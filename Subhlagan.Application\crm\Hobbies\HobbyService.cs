﻿using System;
using System.Collections.Generic;
using System.IO.Packaging;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Users;

namespace Subhlagan.Application.Hobbies
{
    public partial class HobbyService : IHobbyService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Hobby> _hobbyRepository;
        private readonly IUserService _userService;
        #endregion

        #region Ctor

        public HobbyService(EnhancedEntityRepository<Hobby> hobbyRepository, IUserService userService)
        {
            _hobbyRepository = hobbyRepository;
            _userService = userService;
        }

        #endregion

        #region Methods

        public virtual async Task InsertHobbyAsync(Hobby hobby)
        {
            if (hobby == null)
                throw new ArgumentNullException(nameof(hobby));

            hobby.Name = CommonHelper.EnsureNotNull(hobby.Name);
            hobby.Name = hobby.Name.Trim();
            hobby.Name = CommonHelper.EnsureMaximumLength(hobby.Name, 255);
            await _userService.SetCurrentUserIdAsync(hobby);

            await _hobbyRepository.InsertAsync(hobby);
        }

        public virtual async Task UpdateHobbyAsync(Hobby hobby)
        {
            if (hobby == null)
                throw new ArgumentNullException(nameof(hobby));

            hobby.Name = CommonHelper.EnsureNotNull(hobby.Name);
            hobby.Name = hobby.Name.Trim();
            hobby.Name = CommonHelper.EnsureMaximumLength(hobby.Name, 255);
            await _userService.SetCurrentUserIdAsync(hobby);

            await _hobbyRepository.UpdateAsync(hobby);
        }

        public virtual async Task DeleteHobbyAsync(Hobby hobby)
        {
            if (hobby == null)
                throw new ArgumentNullException(nameof(hobby));
            await _userService.SetCurrentUserIdAsync(hobby);

            await _hobbyRepository.DeleteAsync(hobby);
        }

        public virtual async Task<Hobby> GetHobbyByIdAsync(int hobbyId)
        {
            return await _hobbyRepository.GetByIdAsync(hobbyId, cache => default);
        }

        public virtual async Task<IList<Hobby>> GetAllHobbiesAsync(bool showHidden = false)
        {
            var hobbies = await _hobbyRepository.GetAllAsync(query =>
            {
                query = query.Where(h => !h.Deleted);
                query = query.OrderBy(h => h.Id);
                return query;

            }, cache => default);

            if (!showHidden)
                hobbies = hobbies.Where(h => h.Active).ToList();

            return hobbies;
        }

        public async Task<string> GetHobbyNameByIdAsync(int id)
        {
            return (await GetHobbyByIdAsync(id))?.Name ?? string.Empty;
        }

        #endregion
    }

}
