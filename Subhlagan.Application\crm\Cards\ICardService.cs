﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Cards
{
    public interface ICardService
    {
        Task InsertCardAsync(Card card);
        Task UpdateCardAsync(Card card);
        Task DeleteCardAsync(Card card);
        Task<Card> GetCardByIdAsync(int cardId);
        Task<IList<Card>> GetAllCardsAsync();
    }

}
