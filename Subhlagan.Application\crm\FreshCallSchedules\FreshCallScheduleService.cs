﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.WebApp.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.FreshCallSchedules
{
    public partial class FreshCallScheduleService : IFreshCallScheduleService
    {
        #region Fields

        private readonly EnhancedEntityRepository<FreshCallSchedule> _freshCallScheduleRepository;

        #endregion

        #region Ctor

        public FreshCallScheduleService(EnhancedEntityRepository<FreshCallSchedule> freshCallScheduleRepository)
        {
            _freshCallScheduleRepository = freshCallScheduleRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertFreshCallScheduleAsync(FreshCallSchedule schedule)
        {
            if (schedule == null)
                throw new ArgumentNullException(nameof(schedule));

            await _freshCallScheduleRepository.InsertAsync(schedule);
        }

        public virtual async Task UpdateFreshCallScheduleAsync(FreshCallSchedule schedule)
        {
            if (schedule == null)
                throw new ArgumentNullException(nameof(schedule));

            await _freshCallScheduleRepository.UpdateAsync(schedule);
        }

        public virtual async Task DeleteFreshCallScheduleAsync(FreshCallSchedule schedule)
        {
            if (schedule == null)
                throw new ArgumentNullException(nameof(schedule));

            await _freshCallScheduleRepository.DeleteAsync(schedule);
        }

        public virtual async Task<FreshCallSchedule> GetFreshCallScheduleByIdAsync(int id)
        {
            return await _freshCallScheduleRepository.GetByIdAsync(id, cache => default);
        }

        public virtual async Task<IPagedList<FreshCallSchedule>> GetAllFreshCallSchedulesAsync(
            int? initiatorProfileId = null,
            int? matchedProfileId = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false)
        {
            var schedules = await _freshCallScheduleRepository.GetAllPagedAsync(query =>
            {
                if (initiatorProfileId.HasValue)
                    query = query.Where(x => x.InitiatorProfileId == initiatorProfileId.Value);

                if (matchedProfileId.HasValue)
                    query = query.Where(x => x.MatchedProfileId == matchedProfileId.Value);

                query = query.OrderByDescending(x => x.CreatedOnUtc);

                return query;
            }, pageIndex, pageSize, getOnlyTotalCount);

            return schedules;
        }

        public virtual async Task InsertFreshCallScheduleAsync(Profile initiatorProfile, IList<Profile> matchedProfiles)
        {
            if (initiatorProfile == null)
                throw new ArgumentNullException(nameof(initiatorProfile));

            if (matchedProfiles == null || !matchedProfiles.Any())
                return;

            var now = DateTime.UtcNow;
            var scheduleTime = now.Date.AddDays(1); // Midnight tomorrow (00:00 UTC)
            var freshCallSchedules  = new List<FreshCallSchedule>();
            foreach (var matchedProfile in matchedProfiles)
            {
                var schedule = new FreshCallSchedule
                {
                    InitiatorProfileId = initiatorProfile.Id,
                    MatchedProfileId = matchedProfile.Id,
                    ScheduledOnUtc = scheduleTime,
                    CreatedOnUtc = now,
                    UpdatedOnUtc = now,
                    IsProcessed = false
                };
                freshCallSchedules.Add(schedule);
            }

            await _freshCallScheduleRepository.InsertAsync(freshCallSchedules);
        }

        public virtual async Task<IList<FreshCallSchedule>> GetDueSchedulesAsync()
        {
            var now = DateTime.UtcNow;
            return await _freshCallScheduleRepository.GetAllAsync(query =>
                query.Where(x => !x.IsProcessed && x.ScheduledOnUtc <= now)
                     .OrderBy(x => x.ScheduledOnUtc));
        }

        #endregion
    }

}
