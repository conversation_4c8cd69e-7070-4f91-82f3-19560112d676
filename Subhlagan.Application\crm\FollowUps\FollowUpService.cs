﻿using Subhlagan.Core;
using Subhlagan.Core.Caching;

using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;

namespace Subhlagan.Application.FollowUps
{
    public partial class FollowUpService : IFollowUpService
    {
        private readonly EnhancedEntityRepository<FollowUp> _followUpRepository;
        private readonly IStaticCacheManager _staticCacheManager;
        private readonly EnhancedEntityRepository<Profile> _profileRepository;

        public FollowUpService(EnhancedEntityRepository<FollowUp> followUpRepository, IStaticCacheManager staticCacheManager, EnhancedEntityRepository<Profile> profileRepository)
        {
            _followUpRepository = followUpRepository;
            _staticCacheManager = staticCacheManager;
            _profileRepository = profileRepository;
        }

        public async Task InsertFollowUpAsync(FollowUp followUp)
        {
            if (followUp == null)
                throw new ArgumentNullException(nameof(followUp));

            await _followUpRepository.InsertAsync(followUp);
        }

        public async Task UpdateFollowUpAsync(FollowUp followUp)
        {
            if (followUp == null)
                throw new ArgumentNullException(nameof(followUp));

            await _followUpRepository.UpdateAsync(followUp);
        }

        public async Task DeleteFollowUpAsync(FollowUp followUp)
        {
            if (followUp == null)
                throw new ArgumentNullException(nameof(followUp));

            await _followUpRepository.DeleteAsync(followUp);
        }

        public async Task<FollowUp> GetFollowUpByIdAsync(int followUpId)
        {
            return await _followUpRepository.GetByIdAsync(followUpId);
        }

        public virtual async Task<IPagedList<FollowUp>> GetAllFollowUpsAsync(int initiatorProfileId = 0, int matchedProfileId = 0, DateTime? nextFollowUpFromUtc = null,
            DateTime? nextFollowUpToUtc = null, DateTime? createdFromUtc = null, DateTime? createdToUtc = null, int pageIndex = 0, int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false, int? assignedUserId = null, int actionStatusId = 0,
            int reasonId = 0, int userId = 0, IList<int> profileIds = null, List<int> userIds = null)
        {
            return await _followUpRepository.GetAllPagedAsync(query =>
            {
                if (initiatorProfileId > 0)
                    query = query.Where(f => f.InitiatorProfileId == initiatorProfileId);
                if (matchedProfileId > 0)
                    query = query.Where(f => f.MatchedProfileId == matchedProfileId);
                if (nextFollowUpFromUtc.HasValue)
                    query = query.Where(c => nextFollowUpFromUtc.Value <= c.NextFollowUpDateTimeUtc);
                if (nextFollowUpToUtc.HasValue)
                    query = query.Where(c => nextFollowUpToUtc.Value >= c.NextFollowUpDateTimeUtc);

                if (assignedUserId > 0)
                {
                    var relevantProfileIds = _profileRepository.Table
                        .Where(p => p.RelationshipManagerUserId == assignedUserId
                                 || p.AssistantRelationshipManagerUserId == assignedUserId)
                        .Select(p => p.Id);
                    query = query.Where(f => relevantProfileIds.Contains(f.InitiatorProfileId)
                                          || relevantProfileIds.Contains(f.MatchedProfileId));

                }

                if (createdFromUtc.HasValue)
                    query = query.Where(f => f.CreatedOnUtc >= createdFromUtc.Value);

                if (createdToUtc.HasValue)
                    query = query.Where(f => f.CreatedOnUtc <= createdToUtc.Value);

                if (actionStatusId > 0)
                    query = query.Where(f => f.ActionStatusId == actionStatusId);

                if (reasonId > 0)
                    query = query.Where(f => f.ReasonId == reasonId);

                if (userId > 0)
                    query = query.Where(f => f.UserId == userId);

                if (userIds?.Any() ?? false)
                    query = query.Where(f => userIds.Contains(f.UserId));

                // filter by Male Profile Id
                if (profileIds != null && profileIds.Any())
                {
                    query = query.Where(f => profileIds.Contains(f.InitiatorProfileId)
                                        || profileIds.Contains(f.MatchedProfileId));
                }

                return query.OrderByDescending(f => f.CreatedOnUtc);//TODO change to Id
            }, pageIndex, pageSize, getOnlyTotalCount);
        }

        public async Task<IList<FollowUp>> GetFollowUpsAsync(int initiatorProfileId, int matchedProfileId)
        {
            if (initiatorProfileId <= 0 || matchedProfileId <= 0)
                return new List<FollowUp>();

            var cacheKey = _staticCacheManager.PrepareKeyForDefaultCache(PageBaaSDefaults.FollowUpsByProfileIdsCacheKey,
            initiatorProfileId, matchedProfileId);

            var followUps = await _staticCacheManager.GetAsync(cacheKey, async () =>
            {
                var query = _followUpRepository.Table;
                if (initiatorProfileId > 0)
                    query = query.Where(f => f.InitiatorProfileId == initiatorProfileId || f.MatchedProfileId == initiatorProfileId);

                if (matchedProfileId > 0)
                    query = query.Where(f => f.InitiatorProfileId == matchedProfileId || f.MatchedProfileId == matchedProfileId);

                query = query.OrderByDescending(f => f.CreatedOnUtc); //TODO update ID
                return await query.ToListAsync();
            });

            return followUps;
        }

        public Task<IPagedList<FollowUp>> GetAllFollowUpsAsync(int initiatorProfileId = 0, int matchedProfileId = 0, DateTime? nextFollowUpFromUtc = null, DateTime? nextFollowUpToUtc = null, DateTime? createdFromUtc = null, DateTime? createdToUtc = null, int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false, int? assignedUserId = null, int actionStatusId = 0, int reasonId = 0)
        {
            throw new NotImplementedException();
        }

        public virtual async Task<IList<FollowUp>> GetFollowUpsByIdsAsync(int[] ids)
        {
            return await _followUpRepository.GetByIdsAsync(ids);
        }
    }
}
