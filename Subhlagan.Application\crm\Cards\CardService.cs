﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;

namespace Subhlagan.Application.Cards
{
    public class CardService : ICardService
    {
        private readonly EnhancedEntityRepository<Card> _cardRepository;

        public CardService(EnhancedEntityRepository<Card> cardRepository)
        {
            _cardRepository = cardRepository;
        }

        public async Task InsertCardAsync(Card card)
        {
            if (card == null)
                throw new ArgumentNullException(nameof(card));

            card.Name = CommonHelper.EnsureNotNull(card.Name);
            card.Name = card.Name.Trim();
            card.Name = CommonHelper.EnsureMaximumLength(card.Name, 255);

            await _cardRepository.InsertAsync(card);
        }

        public async Task UpdateCardAsync(Card card)
        {
            if (card == null)
                throw new ArgumentNullException(nameof(card));

            card.Name = CommonHelper.EnsureNotNull(card.Name);
            card.Name = card.Name.Trim();
            card.Name = CommonHelper.EnsureMaximumLength(card.Name, 255);

            await _cardRepository.UpdateAsync(card);
        }

        public async Task DeleteCardAsync(Card card)
        {
            if (card == null)
                throw new ArgumentNullException(nameof(card));

            await _cardRepository.DeleteAsync(card);
        }

        public async Task<Card> GetCardByIdAsync(int cardId)
        {
            return await _cardRepository.GetByIdAsync(cardId);
        }

        public async Task<IList<Card>> GetAllCardsAsync()
        {
            var cards = await _cardRepository.GetAllAsync(query =>
            {
                return from c in query
                       orderby c.DisplayOrder
                       select c;
            });

            return cards;
        }
    }

}
