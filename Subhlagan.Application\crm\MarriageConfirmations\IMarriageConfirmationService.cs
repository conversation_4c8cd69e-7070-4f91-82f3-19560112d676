﻿using Subhlagan.Core.Domain;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Subhlagan.Application.MarriageConfirmations
{
    public partial interface IMarriageConfirmationService
    {
        Task InsertMarriageConfirmationAsync(MarriageConfirmation marriageConfirmation);
        Task UpdateMarriageConfirmationAsync(MarriageConfirmation marriageConfirmation);
        Task DeleteMarriageConfirmationAsync(MarriageConfirmation marriageConfirmation);
        Task<MarriageConfirmation> GetMarriageConfirmationByIdAsync(int marriageConfirmationId);
        Task<IList<MarriageConfirmation>> GetAllMarriageConfirmationsAsync();
    }
}