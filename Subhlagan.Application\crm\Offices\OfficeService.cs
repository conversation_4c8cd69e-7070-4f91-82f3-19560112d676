﻿using System;
using System.Collections.Generic;
using System.IO.Packaging;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Cities;
using Subhlagan.Application.Users;
using Subhlagan.Application.Directory;

namespace Subhlagan.Application.Offices
{
    public partial class OfficeService : IOfficeService
    {
        private readonly EnhancedEntityRepository<Office> _officeRepository;
        private readonly IUserService _userService;
        private readonly ICountryService _countryService;
        private readonly IStateProvinceService _stateProvinceService;
        private readonly ICityService _cityService;

        public OfficeService(EnhancedEntityRepository<Office> officeRepository, IUserService userService, ICountryService countryService, IStateProvinceService stateProvinceService, ICityService cityService)
        {
            _officeRepository = officeRepository;
            _userService = userService;
            _countryService = countryService;
            _stateProvinceService = stateProvinceService;
            _cityService = cityService;
        }

        public async Task InsertOfficeAsync(Office office)
        {
            if (office == null)
                throw new ArgumentNullException(nameof(office));
            await _userService.SetCurrentUserIdAsync(office);

            await _officeRepository.InsertAsync(office);
        }

        public async Task UpdateOfficeAsync(Office office)
        {
            if (office == null)
                throw new ArgumentNullException(nameof(office));
            await _userService.SetCurrentUserIdAsync(office);

            await _officeRepository.UpdateAsync(office);
        }

        public async Task DeleteOfficeAsync(Office office)
        {
            if (office == null)
                throw new ArgumentNullException(nameof(office));
            await _userService.SetCurrentUserIdAsync(office);

            await _officeRepository.DeleteAsync(office);
        }

        public async Task<Office> GetOfficeByIdAsync(int officeId)
        {
            return await _officeRepository.GetByIdAsync(officeId, cache => default);
        }

        public async Task<IList<Office>> GetAllOfficesAsync(bool showHidden = false)
        {
            var offices = await _officeRepository.GetAllAsync(query =>
            {
               
                query = query.Where(o => !o.Deleted);
                query = query.OrderBy(o => o.Name).ThenBy(o=>o.DisplayOrder);
                return query;
                //return from o in query
                //       orderby o.DisplayOrder
                //       select o;
            }, cache => default);

            if (!showHidden)
                offices = offices.Where(h => h.Active).ToList();

            return offices;
        }

        public async Task<string> GetOfficeDetailsByIdAsync(int officeId)
        {
            // Fetch the office; if not found, return an empty string early
            var office = await GetOfficeByIdAsync(officeId);
            if (office == null)
                return string.Empty;

            // Use StringBuilder for better performance with string concatenation
            var officeDetailsBuilder = new StringBuilder();
            officeDetailsBuilder.Append(office.Name);

            // Append city details if available
            var city = await _cityService.GetCityByIdAsync(office.CityId ?? 0);
            if (city != null)
                officeDetailsBuilder.Append($", {city.Name}");

            // Append state details if available
            var stateProvince = await _stateProvinceService.GetStateProvinceByIdAsync(office.StateProvinceId ?? 0);
            if (stateProvince != null)
                officeDetailsBuilder.Append($", {stateProvince.Name}");

            // Append country details if available
            var country = await _countryService.GetCountryByIdAsync(office.CountryId ?? 0);
            if (country != null)
                officeDetailsBuilder.Append($", {country.Name}");

            return officeDetailsBuilder.ToString();
        }

    }

}
