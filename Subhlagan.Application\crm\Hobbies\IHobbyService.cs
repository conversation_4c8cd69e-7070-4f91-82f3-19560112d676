﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Hobbies
{
    public interface IHobbyService
    {
        Task InsertHobbyAsync(Hobby hobby);
        Task UpdateHobbyAsync(Hobby hobby);
        Task DeleteHobbyAsync(Hobby hobby);
        Task<Hobby> GetHobbyByIdAsync(int hobbyId);
        Task<IList<Hobby>> GetAllHobbiesAsync(bool showHidden = false);
        Task<string> GetHobbyNameByIdAsync(int id);
    }

}
