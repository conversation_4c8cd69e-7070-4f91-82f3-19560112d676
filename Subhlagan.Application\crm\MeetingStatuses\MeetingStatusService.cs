﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Subhlagan.Application.MeetingStatuses
{
    public partial class MeetingStatusService : IMeetingStatusService
    {
        private readonly EnhancedEntityRepository<MeetingStatus> _meetingStatusRepository;
        private readonly EnhancedEntityRepository<Meeting> _meetingRepository;

        public MeetingStatusService(EnhancedEntityRepository<MeetingStatus> meetingStatusRepository, EnhancedEntityRepository<Meeting> meetingRepository)
        {
            _meetingStatusRepository = meetingStatusRepository;
            _meetingRepository = meetingRepository;
        }

        public async Task InsertMeetingStatusAsync(MeetingStatus meetingStatus)
        {
            if (meetingStatus == null)
                throw new ArgumentNullException(nameof(meetingStatus));

            await _meetingStatusRepository.InsertAsync(meetingStatus);
        }

        public async Task UpdateMeetingStatusAsync(MeetingStatus meetingStatus)
        {
            if (meetingStatus == null)
                throw new ArgumentNullException(nameof(meetingStatus));

            await _meetingStatusRepository.UpdateAsync(meetingStatus);
        }

        public async Task DeleteMeetingStatusAsync(MeetingStatus meetingStatus)
        {
            if (meetingStatus == null)
                throw new ArgumentNullException(nameof(meetingStatus));

            await _meetingStatusRepository.DeleteAsync(meetingStatus);
        }

        public async Task<MeetingStatus> GetMeetingStatusByIdAsync(int meetingStatusId)
        {
            return await _meetingStatusRepository.GetByIdAsync(meetingStatusId, cache => default);
        }

        public async Task<IPagedList<MeetingStatus>> GetAllMeetingStatusAsync(DateTime? meetingStatusFromUtc = null, DateTime? meetingStatusToUtc = null,
            int? profileId = 0, int maleProfileId = 0, int femaleProfileId = 0,
            int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            IList<int> profileIds = null)
        {
            return await _meetingStatusRepository.GetAllPagedAsync(query =>
            {
                if (meetingStatusFromUtc.HasValue)
                    query = query.Where(ms => ms.CreatedOnUtc >= meetingStatusFromUtc.Value);

                if (meetingStatusToUtc.HasValue)
                    query = query.Where(ms => ms.CreatedOnUtc <= meetingStatusToUtc.Value);

                if (profileId.HasValue && profileId > 0)
                    query = query.Where(ms => ms.ProfileId == profileId.Value);

                if (maleProfileId > 0 && femaleProfileId > 0)
                {
                    var meetingQuery = _meetingRepository.Table;
                    var meetingIds = meetingQuery.Where(m => (m.MaleProfileId == maleProfileId && m.FemaleProfileId == femaleProfileId)).Select(i => i.Id);

                    query = query.Where(ms => meetingIds.Contains(ms.MeetingId));
                }

                if (profileIds != null && profileIds.Any())
                    query = query.Where(ms => profileIds.Contains(ms.ProfileId));

                //return query.OrderByDescending(ms => ms.MeetingId).ThenBy(ms => ms.CreatedOnUtc);
                return query.OrderByDescending(ms => ms.Id);

            }, pageIndex, pageSize, getOnlyTotalCount);
        }
    }
}
