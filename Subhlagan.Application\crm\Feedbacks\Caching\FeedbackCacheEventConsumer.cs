﻿using System.Threading.Tasks;

using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;
using Subhlagan.Application.Common;
using Subhlagan.Core;

namespace Subhlagan.Application.Feedbacks.Caching
{
    public partial class FeedbackCacheEventConsumer : CacheEventConsumer<Feedback>
    {
        protected override async Task ClearCacheAsync(Feedback entity, EntityEventType entityEventType)
        {
            await RemoveByPrefixAsync(PageBaaSDefaults.FeedbacksByProfileIdPrefix, entity.ProfileId);
            await base.ClearCacheAsync(entity);
        }
    }

}
