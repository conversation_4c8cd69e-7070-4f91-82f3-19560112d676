﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.FollowUps
{
    public interface IFollowUpService
    {
        Task InsertFollowUpAsync(FollowUp followUp);
        Task UpdateFollowUpAsync(FollowUp followUp);
        Task DeleteFollowUpAsync(FollowUp followUp);
        Task<FollowUp> GetFollowUpByIdAsync(int followUpId);


        Task<IPagedList<FollowUp>> GetAllFollowUpsAsync(int initiatorProfileId = 0, int matchedProfileId = 0, DateTime? nextFollowUpFromUtc = null, DateTime? nextFollowUpToUtc = null,
            DateTime? createdFromUtc = null, DateTime? createdToUtc = null, int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            int? assignedUserId = null, int actionStatusId = 0, int reasonId = 0,
            int userId = 0, IList<int> profileIds = null, List<int> userIds = null);

        Task<IList<FollowUp>> GetFollowUpsAsync(int initiatorProfileId, int matchedProfileId);

        Task<IList<FollowUp>> GetFollowUpsByIdsAsync(int[] ids);
    }

}
