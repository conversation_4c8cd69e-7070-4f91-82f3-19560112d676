﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Gotras
{
    public interface IGotraService
    {
        Task InsertGotraAsync(Gotra gotra);
        Task UpdateGotraAsync(Gotra gotra);
        Task DeleteGotraAsync(Gotra gotra);
        Task<Gotra> GetGotraByIdAsync(int gotraId);
        Task<IList<Gotra>> GetAllGotrasAsync(int communityId = 0, bool showHidden = false);
        Task<IList<Gotra>> GetGotrasByCommunityIdAsync(int communityId);
    }
}
