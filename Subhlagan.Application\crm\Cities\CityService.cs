﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Vml.Office;
using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Users;
using Subhlagan.Core;

namespace Subhlagan.Application.Cities
{
    public partial class CityService : ICityService
    {
        private readonly EnhancedEntityRepository<City> _cityRepository;
        private readonly IUserService _userService;
        private readonly IStaticCacheManager _staticCacheManager;

        public CityService(EnhancedEntityRepository<City> cityRepository, IUserService userService, IStaticCacheManager staticCacheManager)
        {
            _cityRepository = cityRepository;
            _userService = userService;
            _staticCacheManager = staticCacheManager;
        }

        public async Task InsertCityAsync(City city)
        {
            if (city == null)
                throw new ArgumentNullException(nameof(city));
            await _userService.SetCurrentUserIdAsync(city);
            await _cityRepository.InsertAsync(city);
        }

        public async Task UpdateCityAsync(City city)
        {
            if (city == null)
                throw new ArgumentNullException(nameof(city));
            await _userService.SetCurrentUserIdAsync(city);

            await _cityRepository.UpdateAsync(city);
        }

        public async Task DeleteCityAsync(City city)
        {
            if (city == null)
                throw new ArgumentNullException(nameof(city));
            await _userService.SetCurrentUserIdAsync(city);

            await _cityRepository.DeleteAsync(city);
        }

        public async Task<City> GetCityByIdAsync(int cityId)
        {
            return await _cityRepository.GetByIdAsync(cityId, cache => default);
        }

        /// <summary>
        /// Gets all cities with optional filters for country, state, and deleted status
        /// </summary>
        /// <param name="countryId">Country identifier (optional)</param>
        /// <param name="stateProvinceId">State/Province identifier (optional)</param>
        /// <param name="includeDeleted">Whether to include deleted records</param>
        /// <returns>A task that represents the asynchronous operation, containing a list of cities</returns>
        public virtual async Task<IList<City>> GetAllCitiesAsync(
            int countryId = 0,
            int stateProvinceId = 0,
            bool includeDeleted = false)
        {
            var cacheKey = _staticCacheManager.PrepareKeyForDefaultCache(
                PageBaaSDefaults.CitiesAllCacheKey, countryId, stateProvinceId, includeDeleted);

            return await _staticCacheManager.GetAsync(cacheKey, async () =>
            {
                var query = _cityRepository.Table;
                if (countryId > 0)
                    query = query.Where(c => c.CountryId == countryId);

                if (stateProvinceId > 0)
                    query = query.Where(c => c.StateProvinceId == stateProvinceId);

                // Filter by Deleted status if needed
                if (!includeDeleted)
                    query = query.Where(c => !c.Deleted);

                // Order by DisplayOrder and then by Name
                query = query.OrderBy(c => c.DisplayOrder).ThenBy(c => c.Name);

                return await query.ToListAsync();
            });
        }


        /// <summary>
        /// Gets cities by country identifier
        /// </summary>
        /// <param name="countryId">Country identifier</param>
        /// <returns>A task that represents the asynchronous operation, containing a list of cities</returns>
        public virtual async Task<IList<City>> GetCitiesByCountryIdAsync(int countryId)
        {
            var key = _staticCacheManager.PrepareKeyForDefaultCache(
                PageBaaSDefaults.CitiesByCountryCacheKey, countryId);

            return await _staticCacheManager.GetAsync(key, async () =>
            {
                return await _cityRepository.Table
                    .Where(c => c.CountryId == countryId && !c.Deleted)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToListAsync();
            });
        }

        /// <summary>
        /// Gets cities by state/province identifier
        /// </summary>
        /// <param name="stateProvinceId">State/Province identifier</param>
        /// <param name="showHidden">Whether to include hidden records</param>
        /// <returns>A task that represents the asynchronous operation, containing a list of cities</returns>
        public virtual async Task<IList<City>> GetCitiesByStateProvinceIdAsync(int stateProvinceId, bool showHidden = false)
        {
            var key = _staticCacheManager.PrepareKeyForDefaultCache(
                PageBaaSDefaults.CitiesByStateCacheKey, stateProvinceId, showHidden);

            return await _staticCacheManager.GetAsync(key, async () =>
            {
                return await _cityRepository.Table
                    .Where(c => c.StateProvinceId == stateProvinceId && (showHidden || !c.Deleted))
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToListAsync();
            });
        }


        public virtual async Task<City> GetCityByNameAsync(string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;

            var query = from c in _cityRepository.Table
                        where c.Name == name
                        select c;

            var city = await query.FirstOrDefaultAsync();

            return city;
        }

        public async Task<IPagedList<City>> GetAllCitiesAsync(
        string name = null,
        int? countryId = null,
        int? stateProvinceId = null,
        int pageIndex = 0,
        int pageSize = int.MaxValue,
        bool includeDeleted = false,
        IList<int> cityIds = null)
        {
            // Start with the base query
            var query = _cityRepository.Table;

            // Apply filters
            if (!string.IsNullOrWhiteSpace(name))
                query = query.Where(city => city.Name.ToLower().Contains(name.ToLower()));

            if (countryId.HasValue && countryId > 0)
                query = query.Where(city => city.CountryId == countryId);

            if (stateProvinceId.HasValue && stateProvinceId > 0)
                query = query.Where(city => city.StateProvinceId == stateProvinceId);

            if (!includeDeleted)
                query = query.Where(city => !city.Deleted);

            if (cityIds != null && cityIds.Any())
                query = query.Where(city => cityIds.Contains(city.Id));

            // Apply ordering (e.g., by DisplayOrder and Name)
            query = query.OrderBy(city => city.DisplayOrder).ThenBy(city => city.Name);

            // Execute the query with pagination
            return await query.ToPagedListAsync(pageIndex, pageSize);
        }

        public async Task<IList<City>> GetCityByIdsAsync(IList<int> cityIds)
        {
            return await _cityRepository.GetByIdsAsync(cityIds);
        }

        public async Task<IList<string>> GetDuplicateNameWarningsAsync(string name, int? stateProvinceId)
        {
            var warnings = new List<string>();

            // Validate the name
            if (string.IsNullOrWhiteSpace(name))
            {
                warnings.Add("Name cannot be null or empty.");
                return warnings; // No need to proceed if name is invalid
            }

            if (!stateProvinceId.HasValue)
            {
                stateProvinceId = 0;
            }

            // Check for duplicate names
            var isDuplicate = await _cityRepository.IsDuplicateNameAsync(
                name,
                additionalQuery: stateProvinceId > 0 ? entity => entity.StateProvinceId == stateProvinceId.Value : null
            );

            if (isDuplicate)
                warnings.Add($"The name '{name}' already exists in the selected state/province.");

            return warnings;
        }
    }
}
