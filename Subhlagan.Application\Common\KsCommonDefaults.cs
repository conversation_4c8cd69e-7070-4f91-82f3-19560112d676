﻿using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Common
{

    public static partial class KsCommonDefaults
    {
        public static string KeepAlivePath => "keepalive/index";

        public static int RestartTimeout => 3000;

        public static string DbBackupsPath => "db_backups\\";

        public static string DbBackupFileExtension => "bak";

        public static string LocalePatternPath => "lib_npm/cldr-data/main/{0}";

        public static string DefaultLocalePattern => "en";

        public static string DefaultLanguageCulture => "en-GB";

        public static CacheKey GenericAttributeCacheKey => new("sl.genericattribute.{0}-{1}");
    }
}
