# 🗄️ Database Unused Table Cleanup - Execution Guide

## 📋 Overview
This guide provides a comprehensive, step-by-step process to safely identify and delete unused tables from the `SubhLaganNew_v1` database. The approach combines **codebase analysis** with **actual database analysis** to ensure maximum safety.

## 🎯 Objectives
- Identify tables that exist in the database but are not used by the application
- Safely remove unused tables to optimize database performance
- Maintain complete rollback capability
- Follow existing project conventions and patterns

---

## 📁 Files Created
| File | Purpose |
|------|---------|
| `01_GetAllTables.sql` | Extract complete table inventory with sizes and metadata |
| `02_GetTableRelationships.sql` | Analyze foreign key relationships and dependencies |
| `03_GetTableUsageStats.sql` | Get table usage statistics and activity patterns |
| `04_GetTableReferences.sql` | Find table references in stored procedures, views, functions |
| `05_DatabaseAnalysisSummary.sql` | Comprehensive analysis with unused table confidence scoring |
| `06_CreateBackupScripts.sql` | Create database backup before cleanup |
| `RunDatabaseAnalysis.ps1` | PowerShell script to execute all analysis scripts |
| `CodebaseEntityMapping.json` | Cross-reference mapping between code and database |
| `UnusedTableCleanupMigration.cs` | FluentMigrator cleanup migration |

---

## 🚀 Step-by-Step Execution

### ⚠️ **CRITICAL: Prerequisites**
1. **SQL Server Management Studio** or **Azure Data Studio** installed
2. **PowerShell** with **SqlServer module**: `Install-Module -Name SqlServer`
3. **Database backup location** with sufficient space
4. **Development environment** for testing (recommended)

### 📊 **Phase 1: Database Analysis**

#### Step 1: Run Database Analysis
```powershell
# Navigate to the DatabaseAnalysis directory
cd /mnt/c/Workshop/SubhLagan/Projects/Subhlagan/src/DatabaseAnalysis

# Execute comprehensive database analysis
.\RunDatabaseAnalysis.ps1 -ServerName "Localhost\SQLEXPRESS" -DatabaseName "SubhLaganNew_v1"
```

#### Step 2: Review Analysis Results
The script will generate:
- `DatabaseAnalysisResults/DatabaseAnalysisReport.txt` - **Main summary report**
- `DatabaseAnalysisResults/05_AnalysisSummary.csv` - **Detailed table analysis**
- Individual CSV files with specific analysis data

#### Step 3: Identify High-Confidence Unused Tables
Look for tables with **UnusedConfidenceScore >= 85** in the summary report:
- ✅ **95+ Score**: Safe to delete (no data, no references, no usage)
- ✅ **85-94 Score**: Likely safe to delete (minimal or no activity)
- ⚠️ **60-84 Score**: Requires manual review
- ❌ **<60 Score**: Keep (active or has dependencies)

### 🛡️ **Phase 2: Safety Validation**

#### Step 4: Cross-Reference with Codebase Analysis
Compare database results with `CodebaseEntityMapping.json`:

**Known Safe Deletions from Codebase Analysis:**
- `AstroCity` - Commented out in migrations, no service/controller

**Known Disabled Features (DO NOT DELETE):**
- `Card` - Has full implementation but commented in migration
- `ProfileAttribute*` tables - Extensive code usage despite migration comments

#### Step 5: Manual Verification
For each table identified as potentially unused:
1. **Check recent application logs** for any references
2. **Search codebase** for dynamic SQL that might reference the table
3. **Verify with business stakeholders** that the functionality is truly unused
4. **Test in development environment** before production deletion

### 💾 **Phase 3: Safe Backup**

#### Step 6: Create Comprehensive Backup
```sql
-- Run the backup script
EXEC xp_cmdshell 'sqlcmd -S "Localhost\SQLEXPRESS" -d master -i "06_CreateBackupScripts.sql"'
```

Or manually:
```sql
BACKUP DATABASE SubhLaganNew_v1 
TO DISK = 'C:\DatabaseBackups\SubhLaganNew_v1_BeforeTableCleanup_YYYYMMDD_HHMMSS.bak'
WITH FORMAT, COMPRESSION, CHECKSUM, 
DESCRIPTION = 'Full backup before unused table cleanup';
```

#### Step 7: Verify Backup
```sql
RESTORE VERIFYONLY FROM DISK = 'C:\DatabaseBackups\SubhLaganNew_v1_BeforeTableCleanup_YYYYMMDD_HHMMSS.bak';
```

### 🔧 **Phase 4: Execute Cleanup**

#### Step 8: Update Migration File
Edit `UnusedTableCleanupMigration.cs` to include only the tables confirmed as unused through your analysis.

**Example:**
```csharp
// Add confirmed unused tables here
if (MigrationHelper.TableExists(this, "UnusedTableName") && 
    IsTableSafeToDelete("UnusedTableName"))
{
    MigrationHelper.DeleteTableIfExists(this, typeof(UnusedTableName));
}
```

#### Step 9: Test in Development
1. **Restore backup** to development database
2. **Run the migration** in development
3. **Test application functionality** thoroughly
4. **Verify no errors** or missing functionality

#### Step 10: Execute in Production
```bash
# Run the migration (adjust path as needed)
dotnet run --project Subhlagan.WebApp -- migrate
```

### ✅ **Phase 5: Validation & Monitoring**

#### Step 11: Post-Migration Validation
1. **Check application logs** for any errors
2. **Test critical application functions**
3. **Monitor database performance** (should improve)
4. **Verify migration was logged** in migration history table

#### Step 12: Keep Backup Safe
- **Retain backup file** for at least 30 days
- **Document what was deleted** for future reference
- **Update system documentation** to reflect changes

---

## 🚨 **Emergency Rollback Procedure**

If issues are discovered after cleanup:

### Option 1: Migration Rollback
```bash
# Roll back the migration (recreates empty tables)
dotnet run --project Subhlagan.WebApp -- migrate-down UnusedTableCleanupMigration
```

### Option 2: Full Database Restore
```sql
USE master;
ALTER DATABASE SubhLaganNew_v1 SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
RESTORE DATABASE SubhLaganNew_v1 FROM DISK = 'C:\DatabaseBackups\SubhLaganNew_v1_BeforeTableCleanup_YYYYMMDD_HHMMSS.bak' WITH REPLACE;
ALTER DATABASE SubhLaganNew_v1 SET MULTI_USER;
```

---

## 📈 **Expected Results**

### **Benefits of Cleanup:**
- ✅ **Reduced database size** and backup time
- ✅ **Improved query performance** (less metadata to process)
- ✅ **Cleaner database schema** for maintenance
- ✅ **Reduced complexity** for developers

### **Typical Findings:**
Based on codebase analysis, expect to find:
- **1-3 truly unused tables** (like AstroCity)
- **5-10 disabled feature tables** (should NOT be deleted)
- **50+ active tables** (definitely keep)

---

## ⚠️ **Important Safety Notes**

1. **NEVER** delete tables with `UnusedConfidenceScore < 85` without extensive manual review
2. **ALWAYS** test in development environment first
3. **MAINTAIN** backup files for at least 30 days after cleanup
4. **DOCUMENT** all deletions for future reference
5. **MONITOR** application after cleanup for any issues

---

## 🔍 **Troubleshooting**

### PowerShell Script Issues
```powershell
# If SqlServer module is missing:
Install-Module -Name SqlServer -Force

# If execution policy prevents script:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Migration Issues
```bash
# Check migration status
dotnet run --project Subhlagan.WebApp -- migration-status

# Force migration if needed
dotnet run --project Subhlagan.WebApp -- migrate --force
```

### Permission Issues
Ensure your SQL Server login has:
- `db_ddladmin` role for table deletion
- `db_backup` permissions for backup operations
- `VIEW SERVER STATE` for usage statistics

---

## 📞 **Support**

For issues or questions:
1. **Review the analysis reports** in `DatabaseAnalysisResults/`
2. **Check the migration logs** for specific error messages
3. **Verify backup integrity** before proceeding with any cleanup
4. **Test thoroughly** in development environment

---

## ✨ **Summary**

This comprehensive approach ensures **maximum safety** while identifying genuinely unused database tables. The combination of static code analysis and dynamic database analysis provides high confidence in cleanup decisions while maintaining complete rollback capability.

**Remember**: When in doubt, **don't delete**. It's better to keep an unused table than to accidentally delete one that's needed.