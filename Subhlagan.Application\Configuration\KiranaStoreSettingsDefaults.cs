﻿using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain.Configuration;

namespace Subhlagan.Application.Configuration
{
    /// <summary>
    /// Represents default values related to settings
    /// </summary>
    public static partial class KiranaStoreSettingsDefaults
    {
        #region Caching defaults

        /// <summary>
        /// Gets a key for caching
        /// </summary>
        public static CacheKey SettingsAllAsDictionaryCacheKey => new("sl.setting.all.dictionary.", KsEntityCacheDefaults<Setting>.Prefix);

        #endregion
    }
}