﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.ContactExchanges
{
    public partial interface IContactExchangeService
    {
        Task InsertContactExchangeAsync(ContactExchange contactExchange);

        Task UpdateContactExchangeAsync(ContactExchange contactExchange);

        Task DeleteContactExchangeAsync(ContactExchange contactExchange);

        Task<ContactExchange> GetContactExchangeByIdAsync(int contactExchangeId);

        Task<IPagedList<ContactExchange>> GetAllContactExchangesAsync(
            int initiatorProfileId = 0, int matchedProfileId = 0,
            DateTime? contactExchangeDateFrom = null,
            DateTime? contactExchangeDateTo = null, int userId = 0,
            int pageIndex = 0,
            int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            IList<int> initiatorProfileIds = null,
            IList<int> matchedProfileIds = null, IList<int> contactExchangeIds = null
        );
    }
}
