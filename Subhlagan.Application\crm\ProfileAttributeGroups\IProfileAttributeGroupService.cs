﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.ProfileAttributeGroups
{
    public interface IProfileAttributeGroupService
    {
        Task InsertProfileAttributeGroupAsync(ProfileAttributeGroup profileAttributeGroup);
        Task UpdateProfileAttributeGroupAsync(ProfileAttributeGroup profileAttributeGroup);
        Task DeleteProfileAttributeGroupAsync(ProfileAttributeGroup profileAttributeGroup);
        Task<ProfileAttributeGroup> GetProfileAttributeGroupByIdAsync(int profileAttributeGroupId);
        Task<IList<ProfileAttributeGroup>> GetAllProfileAttributeGroupsAsync();
    }

}
