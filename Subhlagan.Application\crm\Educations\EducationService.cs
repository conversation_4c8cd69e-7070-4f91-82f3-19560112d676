﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.Educations
{
    public partial class EducationService : IEducationService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Education> _educationRepository;

        #endregion

        #region Ctor

        public EducationService(EnhancedEntityRepository<Education> educationRepository)
        {
            _educationRepository = educationRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertEducationAsync(Education education)
        {
            if (education == null)
                throw new ArgumentNullException(nameof(education));

            education.Location = CommonHelper.EnsureNotNull(education.Location);
            education.College = CommonHelper.EnsureNotNull(education.College);
            education.Year = CommonHelper.EnsureNotNull(education.Year);

            education.Location = education.Location.Trim();
            education.College = education.College.Trim();
            education.Year = education.Year.Trim();
            education.CreatedOnUtc = DateTime.UtcNow;
            await _educationRepository.InsertAsync(education);
        }

        public virtual async Task UpdateEducationAsync(Education education)
        {
            if (education == null)
                throw new ArgumentNullException(nameof(education));

            education.Location = CommonHelper.EnsureNotNull(education.Location);
            education.College = CommonHelper.EnsureNotNull(education.College);
            education.Year = CommonHelper.EnsureNotNull(education.Year);

            education.Location = education.Location.Trim();
            education.College = education.College.Trim();
            education.Year = education.Year.Trim();

            await _educationRepository.UpdateAsync(education);
        }

        public virtual async Task DeleteEducationAsync(Education education)
        {
            if (education == null)
                throw new ArgumentNullException(nameof(education));

            await _educationRepository.DeleteAsync(education);
        }

        public virtual async Task<Education> GetEducationByIdAsync(int educationId)
        {
            return await _educationRepository.GetByIdAsync(educationId, cache => default);
        }

        public virtual async Task<IPagedList<Education>> GetAllEducationsAsync(
            int? profileId = null,
            string location = null,
            string college = null,
            string year = null,
            int? educationAreaId = null,
            int? qualificationId = null,
            DateTime? createdFromUtc = null,
            DateTime? createdToUtc = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false,
            IList<int> educationAreaIds = null,
            IList<int> qualificationIds = null)
        {
            var educations = await _educationRepository.GetAllPagedAsync(query =>
            {
                if (profileId.HasValue)
                    query = query.Where(e => e.ProfileId == profileId.Value);

                if (!string.IsNullOrWhiteSpace(location))
                    query = query.Where(e => e.Location.Contains(location));

                if (!string.IsNullOrWhiteSpace(college))
                    query = query.Where(e => e.College.Contains(college));

                if (!string.IsNullOrWhiteSpace(year))
                    query = query.Where(e => e.Year.Contains(year));

                if (educationAreaId.HasValue)
                    query = query.Where(e => e.EducationAreaId == educationAreaId.Value);

                if (qualificationId.HasValue)
                    query = query.Where(e => e.QualificationId == qualificationId.Value);

                if (createdFromUtc.HasValue)
                    query = query.Where(e => e.CreatedOnUtc >= createdFromUtc.Value);

                if (createdToUtc.HasValue)
                    query = query.Where(e => e.CreatedOnUtc <= createdToUtc.Value);

                if (educationAreaIds is not null)
                {
                    if (educationAreaIds.Contains(0))
                        educationAreaIds.Remove(0);

                    if (educationAreaIds.Any())
                    {
                        query = query.Where(e => educationAreaIds.Contains(e.EducationAreaId));
                    }
                }
                if (qualificationIds is not null)
                {
                    if (qualificationIds.Contains(0))
                        qualificationIds.Remove(0);

                    if (qualificationIds.Any())
                    {
                        query = query.Where(e => qualificationIds.Contains(e.QualificationId));
                    }
                }
                query = query.OrderBy(e => e.CreatedOnUtc).ThenBy(e => e.Id);

                return query;
            }, pageIndex, pageSize, getOnlyTotalCount);

            return educations;
        }


        #endregion

        public async Task<List<int>> GetProfileIdsByEducationAreaAsync(List<int> educationAreaIds)
        {
            return (await GetAllEducationsAsync(educationAreaIds: educationAreaIds))
                .Select(e => e.ProfileId)
                .Distinct()
                .ToList();
        }

        public async Task<List<int>> GetProfileIdsByQualificationLevelAsync(List<int> qualificationIds)
        {
            return (await GetAllEducationsAsync(qualificationIds: qualificationIds))
                .Select(e => e.ProfileId)
                .Distinct()
                .ToList();
        }
    }

}
