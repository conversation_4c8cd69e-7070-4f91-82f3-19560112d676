-- =====================================================================
-- Database Table Relationships Script for SubhLaganNew_v1
-- Purpose: Extract all foreign key relationships and dependencies
-- =====================================================================

USE SubhLaganNew_v1;

-- Get all foreign key relationships
SELECT 
    fk.name AS ForeignKeyName,
    SCHEMA_NAME(tp.schema_id) AS ParentSchema,
    tp.name AS ParentTable,
    cp.name AS ParentColumn,
    SCHEMA_NAME(tr.schema_id) AS ReferencedSchema,
    tr.name AS ReferencedTable,
    cr.name AS ReferencedColumn,
    fk.delete_referential_action_desc AS DeleteAction,
    fk.update_referential_action_desc AS UpdateAction,
    fk.is_disabled AS IsDisabled
FROM 
    sys.foreign_keys AS fk
    INNER JOIN sys.tables AS tp ON fk.parent_object_id = tp.object_id
    INNER JOIN sys.tables AS tr ON fk.referenced_object_id = tr.object_id
    INNER JOIN sys.foreign_key_columns AS fkc ON fk.object_id = fkc.constraint_object_id
    INNER JOIN sys.columns AS cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
    INNER JOIN sys.columns AS cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
ORDER BY 
    ParentSchema, ParentTable, ForeignKeyName;

-- Get tables with no foreign key relationships (potential orphans)
WITH TablesWithFKs AS (
    SELECT DISTINCT tp.name AS TableName
    FROM sys.foreign_keys AS fk
    INNER JOIN sys.tables AS tp ON fk.parent_object_id = tp.object_id
    UNION
    SELECT DISTINCT tr.name AS TableName
    FROM sys.foreign_keys AS fk
    INNER JOIN sys.tables AS tr ON fk.referenced_object_id = tr.object_id
)
SELECT 
    t.name AS OrphanedTable,
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.create_date AS CreatedDate,
    ISNULL(p.rows, 0) AS RowCount
FROM 
    sys.tables t
    LEFT JOIN TablesWithFKs fk ON t.name = fk.TableName
    LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE 
    fk.TableName IS NULL
    AND t.is_ms_shipped = 0
    AND t.name NOT LIKE 'sys%'
    AND t.name NOT LIKE 'MS%'
ORDER BY 
    t.name;

-- Get table dependency hierarchy (which tables depend on others)
WITH TableDependencies AS (
    SELECT 
        tr.name AS ReferencedTable,
        tp.name AS DependentTable,
        1 AS Level
    FROM 
        sys.foreign_keys AS fk
        INNER JOIN sys.tables AS tp ON fk.parent_object_id = tp.object_id
        INNER JOIN sys.tables AS tr ON fk.referenced_object_id = tr.object_id
)
SELECT 
    ReferencedTable,
    STRING_AGG(DependentTable, ', ') AS DependentTables,
    COUNT(*) AS DependencyCount
FROM TableDependencies
GROUP BY ReferencedTable
ORDER BY DependencyCount DESC, ReferencedTable;