-- =====================================================================
-- COMPLETE UNUSED TABLE ANALYSIS FOR SubhLaganNew_v1
-- =====================================================================
-- Purpose: Single comprehensive script to identify unused database tables
--          with cross-reference to codebase analysis for maximum safety
-- 
-- Database: SubhLaganNew_v1 (Matrimonial CRM System)
-- Framework: ASP.NET Core 7.0 with Clean Architecture
-- 
-- INSTRUCTIONS:
-- 1. Open SQL Server Management Studio
-- 2. Connect to: Localhost\SQLEXPRESS
-- 3. Select Database: SubhLaganNew_v1
-- 4. Execute this entire script
-- 5. Review results - focus on tables with ConfidenceScore >= 85
-- =====================================================================

USE SubhLaganNew_v1;

PRINT '========================================';
PRINT 'SUBHLAGAN CRM - UNUSED TABLE ANALYSIS';
PRINT 'Database: SubhLaganNew_v1';
PRINT 'Analysis Date: ' + CONVERT(varchar, GETDATE(), 120);
PRINT '========================================';
PRINT '';

-- Create temporary table to store codebase knowledge
IF OBJECT_ID('tempdb..#CodebaseKnowledge') IS NOT NULL DROP TABLE #CodebaseKnowledge;
CREATE TABLE #CodebaseKnowledge (
    TableName NVARCHAR(128) PRIMARY KEY,
    HasService BIT,
    HasController BIT,
    MigrationStatus NVARCHAR(50), -- 'ACTIVE', 'COMMENTED_OUT', 'NOT_IN_MIGRATION'
    CodeReferences INT,
    EntityType NVARCHAR(50), -- 'CORE_ENTITY', 'MAPPING', 'GENERAL', 'UNKNOWN'
    Notes NVARCHAR(500)
);

-- Insert codebase knowledge based on static analysis
INSERT INTO #CodebaseKnowledge (TableName, HasService, HasController, MigrationStatus, CodeReferences, EntityType, Notes) VALUES
-- DEFINITELY UNUSED (from codebase analysis)
('AstroCity', 0, 0, 'COMMENTED_OUT', 0, 'UNKNOWN', 'Commented out in migration, no service/controller found, only commented permission refs'),

-- DISABLED FEATURES (DO NOT DELETE - have full implementations)
('Card', 1, 1, 'COMMENTED_OUT', 50, 'CORE_ENTITY', 'Has CardService, CardController, full MVC implementation - DISABLED FEATURE'),
('ProfileAttribute', 1, 1, 'COMMENTED_OUT', 100, 'CORE_ENTITY', 'Has ProfileAttributeService, ProfileAttributeController - CORE FEATURE'),
('ProfileAttributeValue', 1, 0, 'COMMENTED_OUT', 102, 'CORE_ENTITY', '102+ code references, extensive usage, caching - HEAVILY USED'),
('ProfileAttributeGroup', 1, 1, 'COMMENTED_OUT', 30, 'CORE_ENTITY', 'Has service and controller implementation - DISABLED FEATURE'),
('ProfileSpecificationAttribute', 1, 1, 'COMMENTED_OUT', 40, 'CORE_ENTITY', 'Has service and controller implementation - DISABLED FEATURE'),
('ProfileSpecificationAttributeOption', 1, 0, 'COMMENTED_OUT', 25, 'CORE_ENTITY', 'Used in profile attribute system - ACTIVE FEATURE'),

-- ACTIVE CORE ENTITIES (definitely keep)
('ActionStatus', 1, 1, 'ACTIVE', 50, 'CORE_ENTITY', 'Full implementation with ActionStatusService'),
('City', 1, 1, 'ACTIVE', 80, 'CORE_ENTITY', 'Full implementation with CityService'),
('ClientMeeting', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with ClientMeetingService'),
('Community', 1, 1, 'ACTIVE', 60, 'CORE_ENTITY', 'Full implementation with CommunityService'),
('ContactExchange', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ContactExchangeService'),
('Education', 1, 1, 'ACTIVE', 45, 'CORE_ENTITY', 'Full implementation with EducationService'),
('EducationArea', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with EducationAreaService'),
('EducationLevel', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with EducationLevelService'),
('FamilyMember', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with FamilyMemberService'),
('Feedback', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with FeedbackService'),
('FollowUp', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with FollowUpService'),
('FreshCallSchedule', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with FreshCallScheduleService'),
('Gotra', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with GotraService'),
('Heading', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with HeadingService'),
('Hobby', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with HobbyService'),
('MarriageConfirmation', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with MarriageConfirmationService'),
('MatchKundali', 1, 0, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with MatchKundaliService'),
('MedicalHistory', 1, 0, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with MedicalHistoryService'),
('Meeting', 1, 1, 'ACTIVE', 50, 'CORE_ENTITY', 'Full implementation with MeetingService'),
('MeetingStatus', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with MeetingStatusService'),
('Occupation', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with OccupationService'),
('OccupationAndBusiness', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with OccupationAndBusinessService'),
('Office', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with OfficeService'),
('OrderPaymentHistory', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with OrderPaymentHistoryService'),
('Package', 1, 1, 'ACTIVE', 45, 'CORE_ENTITY', 'Full implementation with PackageService'),
('PartnerPreference', 1, 1, 'ACTIVE', 40, 'CORE_ENTITY', 'Full implementation with PartnerPreferenceService'),
('Profile', 1, 1, 'ACTIVE', 200, 'CORE_ENTITY', 'Core entity with ProfileService - CRITICAL TABLE'),
('ProfileCategory', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ProfileCategoryService'),
('ProfileContact', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ProfileContactService'),
('ProfileMatchInteraction', 1, 1, 'ACTIVE', 60, 'CORE_ENTITY', 'Full implementation with ProfileMatchInteractionService'),
('ProfileMatchStatus', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ProfileMatchStatusService'),
('ProfilePauseRecord', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ProfilePauseRecordService'),
('ProfileStatusHistory', 1, 1, 'ACTIVE', 35, 'CORE_ENTITY', 'Full implementation with ProfileStatusHistoryService'),
('ProfileTransfer', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ProfileTransferService'),
('Qualification', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with QualificationService'),
('Reason', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ReasonService'),
('Remark', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with RemarkService'),
('ShortList', 1, 1, 'ACTIVE', 30, 'CORE_ENTITY', 'Full implementation with ShortListService'),
('User', 1, 0, 'ACTIVE', 100, 'CORE_ENTITY', 'Core user entity with UserService - CRITICAL TABLE'),
('UserWorkingHoursHistory', 1, 1, 'ACTIVE', 25, 'CORE_ENTITY', 'Full implementation with UserWorkingHoursHistoryService'),

-- MAPPING TABLES (keep - used for relationships)
('ProfileHobbyMapping', 0, 0, 'ACTIVE', 15, 'MAPPING', 'Relationship mapping table'),
('ProfileProfileCategoryMapping', 0, 0, 'ACTIVE', 15, 'MAPPING', 'Relationship mapping table'),
('ProfileRestrictedMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),
('ProfileTransferProfileMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),
('ProfileUserMapping', 0, 0, 'ACTIVE', 20, 'MAPPING', 'Relationship mapping table'),
('RefineSearchProfileSpecificationAttributeMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),
('UserEmailAccountMapping', 0, 0, 'ACTIVE', 10, 'MAPPING', 'Relationship mapping table'),

-- GENERAL PLATFORM ENTITIES (keep - framework tables)
('Customer', 1, 0, 'ACTIVE', 80, 'GENERAL', 'Core customer entity with CustomerService'),
('CustomerAddressMapping', 0, 0, 'ACTIVE', 15, 'GENERAL', 'Customer address relationship'),
('CustomerCustomerRoleMapping', 0, 0, 'ACTIVE', 15, 'GENERAL', 'Customer role relationship'),
('CustomerPassword', 0, 0, 'ACTIVE', 20, 'GENERAL', 'Customer authentication'),
('CustomerRole', 0, 0, 'ACTIVE', 25, 'GENERAL', 'Customer role management'),
('Address', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Address management with AddressService'),
('Country', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Country management with CountryService'),
('StateProvince', 1, 0, 'ACTIVE', 25, 'GENERAL', 'State/Province management'),
('Language', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Localization with LanguageService'),
('LocaleStringResource', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Localization resources'),
('LocalizedProperty', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Localized entity properties'),
('ActivityLog', 1, 1, 'ACTIVE', 35, 'GENERAL', 'Activity logging with ActivityLogService'),
('ActivityLogType', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Activity log types'),
('Log', 1, 0, 'ACTIVE', 30, 'GENERAL', 'System logging'),
('Picture', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Picture management with PictureService'),
('PictureBinary', 0, 0, 'ACTIVE', 20, 'GENERAL', 'Picture binary data'),
('Download', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Download management with DownloadService'),
('EmailAccount', 1, 0, 'ACTIVE', 35, 'GENERAL', 'Email account management'),
('MessageTemplate', 1, 0, 'ACTIVE', 40, 'GENERAL', 'Message templates'),
('QueuedEmail', 1, 0, 'ACTIVE', 45, 'GENERAL', 'Email queue management'),
('QueuedEmailAttachment', 1, 0, 'ACTIVE', 20, 'GENERAL', 'Email attachments'),
('MassEmailCampaign', 0, 0, 'ACTIVE', 30, 'GENERAL', 'Mass email campaigns'),
('MassEmailLog', 0, 0, 'ACTIVE', 20, 'GENERAL', 'Mass email logging'),
('MassEmailRecipient', 0, 0, 'ACTIVE', 25, 'GENERAL', 'Mass email recipients'),
('WhatsAppQueuedMessageEntity', 0, 0, 'ACTIVE', 30, 'GENERAL', 'WhatsApp message queue'),
('ScheduleTask', 1, 0, 'ACTIVE', 25, 'GENERAL', 'Scheduled task management'),
('AclRecord', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Access control records'),
('PermissionRecord', 1, 0, 'ACTIVE', 35, 'GENERAL', 'Permission management'),
('PermissionRecordCustomerRoleMapping', 0, 0, 'ACTIVE', 15, 'GENERAL', 'Permission role mapping'),
('Setting', 1, 0, 'ACTIVE', 40, 'GENERAL', 'System settings management'),
('GenericAttribute', 1, 0, 'ACTIVE', 30, 'GENERAL', 'Generic attribute system');

PRINT 'Codebase knowledge loaded: ' + CAST((SELECT COUNT(*) FROM #CodebaseKnowledge) AS VARCHAR) + ' entities';
PRINT '';

-- Main analysis query
WITH DatabaseAnalysis AS (
    SELECT 
        SCHEMA_NAME(t.schema_id) AS SchemaName,
        t.name AS TableName,
        t.create_date AS CreatedDate,
        t.modify_date AS ModifiedDate,
        ISNULL(p.rows, 0) AS RowCount,
        CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS TotalSizeMB,
        
        -- Foreign key analysis
        ISNULL(fk_parent.parent_count, 0) AS ForeignKeysAsParent,
        ISNULL(fk_child.child_count, 0) AS ForeignKeysAsChild,
        
        -- Usage statistics
        ISNULL(usage_stats.total_activity, 0) AS TotalActivity,
        usage_stats.last_activity AS LastActivity,
        
        -- Object references (stored procedures, views, functions)
        ISNULL(obj_ref.reference_count, 0) AS ObjectReferences
        
    FROM sys.tables t
    INNER JOIN sys.indexes i ON t.object_id = i.object_id
    INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
    INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
    
    -- Check for foreign keys where this table is parent (referenced by others)
    LEFT JOIN (
        SELECT referenced_object_id, COUNT(*) as parent_count
        FROM sys.foreign_keys 
        GROUP BY referenced_object_id
    ) fk_parent ON t.object_id = fk_parent.referenced_object_id
    
    -- Check for foreign keys where this table is child (references others)
    LEFT JOIN (
        SELECT parent_object_id, COUNT(*) as child_count
        FROM sys.foreign_keys 
        GROUP BY parent_object_id
    ) fk_child ON t.object_id = fk_child.parent_object_id
    
    -- Usage statistics from DMVs
    LEFT JOIN (
        SELECT 
            us.object_id,
            SUM(ISNULL(us.user_seeks, 0) + ISNULL(us.user_scans, 0) + 
                ISNULL(us.user_lookups, 0) + ISNULL(us.user_updates, 0)) AS total_activity,
            MAX(CASE 
                WHEN us.last_user_seek > us.last_user_scan 
                     AND us.last_user_seek > us.last_user_lookup 
                     AND us.last_user_seek > us.last_user_update
                THEN us.last_user_seek
                WHEN us.last_user_scan > us.last_user_lookup 
                     AND us.last_user_scan > us.last_user_update
                THEN us.last_user_scan
                WHEN us.last_user_lookup > us.last_user_update
                THEN us.last_user_lookup
                ELSE us.last_user_update
            END) AS last_activity
        FROM sys.dm_db_index_usage_stats us
        WHERE us.database_id = DB_ID()
        GROUP BY us.object_id
    ) usage_stats ON t.object_id = usage_stats.object_id
    
    -- Object references (stored procedures, views, functions, triggers)
    LEFT JOIN (
        SELECT 
            t2.object_id,
            COUNT(DISTINCT d.referencing_id) as reference_count
        FROM sys.tables t2
        LEFT JOIN sys.sql_expression_dependencies d ON t2.name = d.referenced_entity_name
        WHERE d.referenced_entity_name IS NOT NULL
        GROUP BY t2.object_id
    ) obj_ref ON t.object_id = obj_ref.object_id
    
    WHERE t.is_ms_shipped = 0
    GROUP BY 
        t.schema_id, t.name, t.object_id, t.create_date, t.modify_date, p.rows,
        fk_parent.parent_count, fk_child.child_count, usage_stats.total_activity, 
        usage_stats.last_activity, obj_ref.reference_count
),
FinalAnalysis AS (
    SELECT 
        da.TableName,
        da.SchemaName,
        da.CreatedDate,
        da.ModifiedDate,
        da.RowCount,
        da.TotalSizeMB,
        da.ForeignKeysAsParent,
        da.ForeignKeysAsChild,
        da.TotalActivity,
        da.LastActivity,
        da.ObjectReferences,
        
        -- Codebase information
        ISNULL(ck.HasService, 0) AS HasService,
        ISNULL(ck.HasController, 0) AS HasController,
        ISNULL(ck.MigrationStatus, 'NOT_IN_CODEBASE') AS MigrationStatus,
        ISNULL(ck.CodeReferences, 0) AS CodeReferences,
        ISNULL(ck.EntityType, 'UNKNOWN') AS EntityType,
        ISNULL(ck.Notes, 'No codebase information available') AS Notes,
        
        -- Calculate confidence score (0-100, higher = safer to delete)
        CASE 
            -- Definitely unsafe to delete (active entities with implementations)
            WHEN ISNULL(ck.EntityType, 'UNKNOWN') IN ('CORE_ENTITY', 'GENERAL') AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'ACTIVE' THEN 0
            WHEN ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.HasController, 0) = 1 THEN 0
            WHEN ISNULL(ck.CodeReferences, 0) > 50 THEN 0
            WHEN da.ForeignKeysAsParent > 0 THEN 0  -- Other tables depend on this
            WHEN da.RowCount > 1000 THEN 0  -- Has significant data
            
            -- Disabled features (commented in migration but have implementations) - DO NOT DELETE
            WHEN ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' AND (ISNULL(ck.HasService, 0) = 1 OR ISNULL(ck.CodeReferences, 0) > 10) THEN 5
            
            -- Potential unused tables
            WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 AND ISNULL(ck.MigrationStatus, 'UNKNOWN') = 'COMMENTED_OUT' THEN 95
            WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 AND da.TotalActivity = 0 THEN 85
            WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 AND da.ObjectReferences = 0 THEN 75
            WHEN da.RowCount = 0 AND da.ForeignKeysAsParent = 0 THEN 65
            WHEN da.RowCount = 0 THEN 50
            WHEN da.TotalActivity = 0 AND da.ObjectReferences = 0 AND da.ForeignKeysAsParent = 0 THEN 40
            WHEN da.TotalActivity = 0 AND da.ObjectReferences = 0 THEN 30
            WHEN da.TotalActivity = 0 THEN 20
            ELSE 10
        END AS ConfidenceScore
        
    FROM DatabaseAnalysis da
    LEFT JOIN #CodebaseKnowledge ck ON da.TableName = ck.TableName
)
SELECT 
    TableName,
    RowCount,
    TotalSizeMB,
    MigrationStatus,
    HasService,
    HasController,
    CodeReferences,
    EntityType,
    ForeignKeysAsParent,
    ForeignKeysAsChild,
    TotalActivity,
    CONVERT(VARCHAR(19), LastActivity, 120) AS LastActivity,
    ObjectReferences,
    ConfidenceScore,
    
    -- Clear recommendation
    CASE 
        WHEN ConfidenceScore >= 95 THEN 'SAFE_TO_DELETE'
        WHEN ConfidenceScore >= 85 THEN 'LIKELY_SAFE_TO_DELETE'
        WHEN ConfidenceScore >= 60 THEN 'REVIEW_REQUIRED'
        WHEN ConfidenceScore >= 20 THEN 'PROBABLY_KEEP'
        ELSE 'DEFINITELY_KEEP'
    END AS Recommendation,
    
    -- Detailed reasoning
    CASE 
        WHEN ConfidenceScore >= 95 THEN 'Empty table, no relationships, no usage, commented in migration, no code implementation'
        WHEN ConfidenceScore >= 85 THEN 'Empty table, no relationships, no usage - but verify no dynamic references'
        WHEN ConfidenceScore >= 75 THEN 'Empty table, no relationships - but may have object references'
        WHEN ConfidenceScore >= 60 THEN 'Empty table but has some relationships or references'
        WHEN ConfidenceScore >= 20 THEN 'Has data or relationships - manual review needed'
        WHEN MigrationStatus = 'COMMENTED_OUT' AND (HasService = 1 OR CodeReferences > 10) THEN 'DISABLED FEATURE - Do not delete, has full implementation'
        WHEN HasService = 1 OR HasController = 1 THEN 'ACTIVE ENTITY - Has service/controller implementation'
        WHEN EntityType IN ('CORE_ENTITY', 'GENERAL') THEN 'CORE SYSTEM TABLE - Essential for application'
        WHEN ForeignKeysAsParent > 0 THEN 'REFERENCED BY OTHER TABLES - Cannot delete safely'
        WHEN RowCount > 1000 THEN 'CONTAINS SIGNIFICANT DATA - Do not delete'
        ELSE 'KEEP - Active or potentially needed table'
    END AS Reasoning,
    
    Notes AS CodebaseNotes,
    CreatedDate,
    ModifiedDate
    
FROM FinalAnalysis
ORDER BY ConfidenceScore DESC, RowCount ASC, TableName;

-- Summary statistics
PRINT '';
PRINT '========================================';
PRINT 'ANALYSIS SUMMARY';
PRINT '========================================';

DECLARE @TotalTables INT, @SafeToDelete INT, @LikelySafe INT, @ReviewRequired INT, @DefinitelyKeep INT;

SELECT @TotalTables = COUNT(*) FROM FinalAnalysis;
SELECT @SafeToDelete = COUNT(*) FROM FinalAnalysis WHERE ConfidenceScore >= 95;
SELECT @LikelySafe = COUNT(*) FROM FinalAnalysis WHERE ConfidenceScore >= 85 AND ConfidenceScore < 95;
SELECT @ReviewRequired = COUNT(*) FROM FinalAnalysis WHERE ConfidenceScore >= 60 AND ConfidenceScore < 85;
SELECT @DefinitelyKeep = COUNT(*) FROM FinalAnalysis WHERE ConfidenceScore < 60;

PRINT 'Total Tables Analyzed: ' + CAST(@TotalTables AS VARCHAR);
PRINT 'Safe to Delete (95-100%): ' + CAST(@SafeToDelete AS VARCHAR);
PRINT 'Likely Safe to Delete (85-94%): ' + CAST(@LikelySafe AS VARCHAR);
PRINT 'Review Required (60-84%): ' + CAST(@ReviewRequired AS VARCHAR);  
PRINT 'Definitely Keep (0-59%): ' + CAST(@DefinitelyKeep AS VARCHAR);
PRINT '';

IF @SafeToDelete > 0
BEGIN
    PRINT '⚠️  TABLES RECOMMENDED FOR DELETION:';
    SELECT TableName, ConfidenceScore, Reasoning 
    FROM FinalAnalysis 
    WHERE ConfidenceScore >= 95
    ORDER BY ConfidenceScore DESC;
    PRINT '';
END

IF @LikelySafe > 0
BEGIN
    PRINT '⚠️  TABLES LIKELY SAFE FOR DELETION (verify first):';
    SELECT TableName, ConfidenceScore, RowCount, Reasoning 
    FROM FinalAnalysis 
    WHERE ConfidenceScore >= 85 AND ConfidenceScore < 95
    ORDER BY ConfidenceScore DESC;
    PRINT '';
END

PRINT '========================================';
PRINT 'IMPORTANT SAFETY NOTES:';
PRINT '1. Only delete tables with ConfidenceScore >= 95';
PRINT '2. NEVER delete tables marked as DISABLED_FEATURE';
PRINT '3. Always backup database before deletion';
PRINT '4. Test in development environment first';
PRINT '5. Tables with CodeReferences > 0 likely have implementations';
PRINT '========================================';

-- Cleanup
DROP TABLE #CodebaseKnowledge;

PRINT '';
PRINT '✅ Analysis Complete!';
PRINT 'Review the results above to identify unused tables.';
PRINT 'Focus on tables with Recommendation = SAFE_TO_DELETE';