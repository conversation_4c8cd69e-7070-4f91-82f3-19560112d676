﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;

namespace Subhlagan.Application.Feedbacks
{
    public partial class FeedbackService : IFeedbackService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Feedback> _feedbackRepository;
        private readonly IStaticCacheManager _staticCacheManager;

        #endregion

        #region Ctor

        public FeedbackService(EnhancedEntityRepository<Feedback> feedbackRepository, IStaticCacheManager staticCacheManager)
        {
            _feedbackRepository = feedbackRepository;
            _staticCacheManager = staticCacheManager;
        }

        #endregion

        #region Methods

        public virtual async Task InsertFeedbackAsync(Feedback feedback)
        {
            if (feedback == null)
                throw new ArgumentNullException(nameof(feedback));

            feedback.Subject = CommonHelper.EnsureNotNull(feedback.Subject);
            feedback.Content = CommonHelper.EnsureNotNull(feedback.Content);

            feedback.Subject = feedback.Subject.Trim();
            feedback.Content = feedback.Content.Trim();

            await _feedbackRepository.InsertAsync(feedback);
        }

        public virtual async Task UpdateFeedbackAsync(Feedback feedback)
        {
            if (feedback == null)
                throw new ArgumentNullException(nameof(feedback));

            feedback.Subject = CommonHelper.EnsureNotNull(feedback.Subject);
            feedback.Content = CommonHelper.EnsureNotNull(feedback.Content);

            feedback.Subject = feedback.Subject.Trim();
            feedback.Content = feedback.Content.Trim();

            await _feedbackRepository.UpdateAsync(feedback);
        }

        public virtual async Task DeleteFeedbackAsync(Feedback feedback)
        {
            if (feedback == null)
                throw new ArgumentNullException(nameof(feedback));

            await _feedbackRepository.DeleteAsync(feedback);
        }

        public virtual async Task<Feedback> GetFeedbackByIdAsync(int feedbackId)
        {
            return await _feedbackRepository.GetByIdAsync(feedbackId, cache => default);
        }

        public virtual async Task<IList<Feedback>> GetAllFeedbacksAsync()
        {
            var feedbacks = await _feedbackRepository.GetAllAsync(query =>
            {
                return from f in query
                       orderby f.Id
                       select f;
            }, cache => default);
            return feedbacks;
        }

        public async Task<IList<Feedback>> GetAllFeedbacksByProfileIdAsync(int profileId)
        {
            if (profileId <= 0)
                return new List<Feedback>();

            var cacheKey = _staticCacheManager.PrepareKeyForDefaultCache(PageBaaSDefaults.FeedbacksByProfileIdCacheKey,
            profileId);

            var feedbacks = await _staticCacheManager.GetAsync(cacheKey, async () =>
            {
                var query = _feedbackRepository.Table;
                if (profileId > 0)
                    query = query.Where(f => f.ProfileId == profileId);

                query = query.OrderByDescending(f => f.Id);
                return await query.ToListAsync();
            });

            return feedbacks;
        }

        #endregion
    }

}
