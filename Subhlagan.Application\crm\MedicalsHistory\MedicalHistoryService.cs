﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Subhlagan.Application.MedicalsHistory
{
    public partial class MedicalHistoryService : IMedicalHistoryService
    {
        private readonly EnhancedEntityRepository<MedicalHistory> _medicalHistoryRepository;

        public MedicalHistoryService(EnhancedEntityRepository<MedicalHistory> medicalHistoryRepository)
        {
            _medicalHistoryRepository = medicalHistoryRepository;
        }

        public async Task InsertMedicalHistoryAsync(MedicalHistory medicalHistory)
        {
            if (medicalHistory == null)
                throw new ArgumentNullException(nameof(medicalHistory));

            await _medicalHistoryRepository.InsertAsync(medicalHistory);
        }

        public async Task UpdateMedicalHistoryAsync(MedicalHistory medicalHistory)
        {
            if (medicalHistory == null)
                throw new ArgumentNullException(nameof(medicalHistory));

            await _medicalHistoryRepository.UpdateAsync(medicalHistory);
        }

        public async Task DeleteMedicalHistoryAsync(MedicalHistory medicalHistory)
        {
            if (medicalHistory == null)
                throw new ArgumentNullException(nameof(medicalHistory));

            await _medicalHistoryRepository.DeleteAsync(medicalHistory);
        }

        public async Task<MedicalHistory> GetMedicalHistoryByIdAsync(int medicalHistoryId)
        {
            return await _medicalHistoryRepository.GetByIdAsync(medicalHistoryId, cache => default);
        }

        public async Task<IPagedList<MedicalHistory>> GetAllMedicalHistoriesAsync(
            int? bloodGroupId = null,
            bool? hasChronicIllness = null,
            bool? hasAllergies = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false)
        {
            return await _medicalHistoryRepository.GetAllPagedAsync(query =>
            {
                if (bloodGroupId.HasValue)
                    query = query.Where(m => m.BloodGroupId == bloodGroupId);

                if (hasChronicIllness.HasValue)
                    query = query.Where(m => m.HasChronicIllness == hasChronicIllness.Value);

                if (hasAllergies.HasValue)
                    query = query.Where(m => m.HasAllergies == hasAllergies.Value);

                return query.OrderBy(m => m.ProfileId);
            }, pageIndex, pageSize, getOnlyTotalCount);
        }
    }

}
