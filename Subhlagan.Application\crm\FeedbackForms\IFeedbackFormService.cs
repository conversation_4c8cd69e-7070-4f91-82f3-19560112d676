﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.FeedbackForms
{
    public partial interface IFeedbackFormService
    {
        Task InsertFeedbackFormAsync(FeedbackForm feedbackForm);
        Task UpdateFeedbackFormAsync(FeedbackForm feedbackForm);
        Task DeleteFeedbackFormAsync(FeedbackForm feedbackForm);
        Task<FeedbackForm> GetFeedbackFormByIdAsync(int feedbackFormId);
        Task<IPagedList<FeedbackForm>> GetAllFeedbackFormsAsync(
            int residencePropertyTypeId = 0, int officePropertyTypeId = 0,
            int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false);
        Task<FeedbackForm> GetFeedbackFormByProfileIdAsync(int profileId, bool getFromDatabase = false);
    }

}
