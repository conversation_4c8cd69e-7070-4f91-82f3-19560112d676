﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.Educations
{
    public partial interface IEducationService
    {
        Task InsertEducationAsync(Education education);
        Task UpdateEducationAsync(Education education);
        Task DeleteEducationAsync(Education education);
        Task<Education> GetEducationByIdAsync(int educationId);
        Task<IPagedList<Education>> GetAllEducationsAsync(
                int? profileId = null,
                string location = null,
                string college = null,
                string year = null,
                int? educationAreaId = null,
                int? qualificationId = null,
                DateTime? createdFromUtc = null,
                DateTime? createdToUtc = null,
                int pageIndex = 0,
                int pageSize = int.MaxValue,
                bool getOnlyTotalCount = false,
                IList<int> educationAreaIds = null,
                IList<int> qualificationIds = null);

        Task<List<int>> GetProfileIdsByEducationAreaAsync(List<int> educationAreaIds);
        Task<List<int>> GetProfileIdsByQualificationLevelAsync(List<int> qualificationIds);
    }

}
