﻿using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;
using Subhlagan.Application.Common;
using Subhlagan.Core;
using System.Threading.Tasks;

namespace Subhlagan.Application.Headings.Caching
{
    public partial class HeadingCacheEventConsumer : CacheEventConsumer<Heading>
    {
        protected override async Task ClearCacheAsync(Heading entity, EntityEventType entityEventType)
        {
            await RemoveByPrefixAsync(PageBaaSDefaults.HeadingsPrefix);
            await base.ClearCacheAsync(entity, entityEventType);
        }
    }
}
