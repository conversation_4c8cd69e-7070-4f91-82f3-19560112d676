﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain.Customers;
using Subhlagan.Core.Domain.Localization;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using LinqToDB;

namespace Subhlagan.Application.Matches
{
    public static class ProfileExtensions
    {
        public static IQueryable<Profile> OrderBy(this IQueryable<Profile> profilesQuery, EnhancedEntityRepository<LocalizedProperty> localizedPropertyRepository, Language currentLanguage, EnhancedEntityRepository<Customer> customerRepository, ProfileSortingEnum orderBy)
        {
            return profilesQuery = orderBy switch
            {
                ProfileSortingEnum.Latest => profilesQuery.OrderBy(p => p.MatchResultId).ThenByDescending(p => p.Id),
                ProfileSortingEnum.Oldest => profilesQuery.OrderBy(p => p.MatchResultId).ThenBy(p => p.Id),
                _ => profilesQuery.OrderByDescending(p => p.MatchResultId).ThenByDescending(p => p.Id)
            };

        }

    
    }
}
