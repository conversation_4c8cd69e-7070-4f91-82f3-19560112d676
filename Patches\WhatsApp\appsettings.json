﻿{
  "ConnectionStrings": {
    "ConnectionString": "Data Source=Localhost\\SQLEXPRESS;Initial Catalog=SubhLaganNew_v1;Integrated Security=True;Persist Security Info=False;Trust Server Certificate=True",
    "DataProvider": "sqlserver",
    "SQLCommandTimeout": null
  },
  "Company": {
    "DefaultTitle": "<PERSON>hlagan",
    "Name": "Subhlagan Com Private Limited",
    "Url": "https://localhost:5001/",
    "Email": "",
    "Address": "Remi Bizcourt, F Wing, 2nd Floor, Plot No.9, Shah Industrial Estate, Near Yash Raj Studio, Andheri (West), Mumbai-400 053.",
    "PhoneNumber": "+91-22-********",
    "SocialMedia": {
      "FacebookLink": "https://facebook.com/SubhLaganBondoftrust",
      "TwitterLink": "https://www.subhlagan.com/Registration#",
      "YoutubeLink": "https://www.subhlagan.com/Registration#",
      "InstagramLink": "https://www.instagram.com/subhlagan?igsh=MWhuOWM3NjN2N2NkZA=="
    },
    "BankAccount": {
      "Name": "SUBHLAGAN.COM PVT. LTD.",
      "Number": "**************",
      "BankName": "Bank of Baroda, Wadala Branch, Mumbai.",
      "IFSCCode": "BARB0WADALA (5th character is Zero) .",
      "PANCardNumber": "**********"
    },
    "ApiKeys": {
      "GoogleMaps": "your-google-maps-api-key"
    }
  },
  "WhatsApp": {
    "Enabled": true,
    "BaseUrl": "https://graph.facebook.com/v22.0/",
    "ApiKey": "EAAlTzNcaV08BO1ZCRHcuEUU7el1lZCqbh16ZAvc8ElHmVVmTtBntkTbJkfZAI8bQSTLPohVZB397FkSzD9YdAAEuU9L2dNj7tr9pLEYGUDlW27LDCHyhwGQOQiDxGuYU8jrw7phKVp0A5CGvvo0GzQ1Iuc3ubmT4TeRGq8D9FSw0dGXTcLZCLgVdNVbF4q9SFZCSwTTO0rMRhxnIxzcwdX37HGke1tagfaKtaFX",
    "PhoneNumberId": "***************",
    "DefaultLanguage": "en",
    "TimeoutSeconds": 60,
    "TemplateNamespace": "",
    "UseTemplateNamespace": false,
    "Templates": {
      "profile_match": {
        "Description": "Notification sent when a potential match is found",
        "Parameters": [
          "Matched profile name",
          "Age",
          "City/Location"
        ]
      },
      "welcome_message": {
        "Description": "Welcome message for new users",
        "Parameters": [
          "Customer first name"
        ]
      },
      "meeting_schedule": {
        "Description": "Notification for scheduled meetings",
        "Parameters": [
          "Matched profile name",
          "Meeting date",
          "Meeting time",
          "Meeting venue"
        ]
      }
    }
  },
  "CacheConfig": {
    "DefaultCacheTime": 60,
    "ShortTermCacheTime": 3,
    "BundledFilesCacheTime": 120
  },
  "CommonConfig": {
    "DisplayFullErrorStack": false,
    "UserAgentStringsPath": "~/App_Data/browscap.xml",
    "CrawlerOnlyUserAgentStringsPath": "~/App_Data/browscap.crawlersonly.xml",
    "UseSessionStateTempDataProvider": false,
    "MiniProfilerEnabled": false,
    "ScheduleTaskRunTimeout": null,
    "StaticFilesCacheControl": "public,max-age=31536000",
    "ServeUnknownFileTypes": false,
    "UseAutofac": true,
    "EnableEmailSending": true,
    "WhatsAppOtpValidityMinutes": 5,
    "WhatsAppOtpRateLimitSeconds":  60,
    "WhatsAppOtpMaxAttempts": 3
  },
  "HostingConfig": {
    "UseProxy": false,
    "ForwardedProtoHeaderName": "",
    "ForwardedForHeaderName": "",
    "KnownProxies": ""
  },
  "WebOptimizer": {
    "EnableJavaScriptBundling": true,
    "EnableCssBundling": true,
    "JavaScriptBundleSuffix": ".scripts",
    "CssBundleSuffix": ".styles",
    "EnableCaching": null,
    "EnableMemoryCache": true,
    "EnableDiskCache": true,
    "EnableTagHelperBundling": false,
    "CdnUrl": null,
    "CacheDirectory": null,
    "AllowEmptyBundle": null,
    "HttpsCompression": 2
  }
}