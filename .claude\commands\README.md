# Claude Commands Documentation

## Overview
This directory contains custom Claude commands for the Subhlagan matrimonial CRM system. These commands provide structured approaches to common development tasks while maintaining consistency with the codebase architecture.

## Available Commands

### 1. workflow-mode
**Purpose**: Activates workflow documentation mode for systematic workflow implementation and documentation.

**Usage**:
```bash
claude workflow-mode
```

**Features**:
- Automatic workflow file handling in `workflow/` directory
- Structured documentation generation
- Future-ready documentation standards
- Implementation plan generation

### 2. analyze-mode
**Purpose**: Activates comprehensive codebase analysis mode for systematic understanding of the Subhlagan system.

**Usage**:
```bash
claude analyze-mode
```

**Features**:
- Systematic architectural analysis
- Clean Architecture layer examination
- Pattern recognition and documentation
- Business logic analysis
- Integration point identification
- Code quality assessment

## Command Structure

### Standard Command Format
Each command follows this structure:
```markdown
# [Command Name] Documentation

## Description
[Brief description of command purpose]

## Usage
[Command syntax and examples]

## System Instructions
[Detailed instructions for <PERSON> to follow]

## Auto-Execute
[Automatic behavior when mode is active]

## Mode Activation
[Response when command is activated]

## Examples
[Usage examples and scenarios]

## Deactivation
[How to exit the mode]
```

### Command Integration
Commands are designed to:
- Follow established patterns in the codebase
- Maintain consistency with Clean Architecture principles
- Support the existing development workflow
- Integrate with project documentation standards

## Development Guidelines

### Creating New Commands
1. **Follow Existing Patterns**: Analyze existing commands for structure and style
2. **Maintain Consistency**: Use established naming conventions and format
3. **Document Thoroughly**: Include comprehensive usage examples and instructions
4. **Test Integration**: Ensure commands work with existing codebase patterns
5. **Update Documentation**: Add new commands to this README

### Command Naming Convention
- Use lowercase with hyphens: `command-name`
- Use descriptive, actionable names
- Include mode suffix for persistent commands: `analyze-mode`, `workflow-mode`

### Documentation Standards
- Follow markdown format with consistent headers
- Include usage examples and scenarios
- Document integration points with existing systems
- Provide clear activation/deactivation instructions

## Project Integration

### Subhlagan-Specific Features
Commands are tailored for the Subhlagan matrimonial CRM system:
- **Clean Architecture**: Commands understand the layered architecture
- **Domain Knowledge**: Commands include matrimonial-specific business logic understanding
- **Technology Stack**: Commands are aware of ASP.NET Core, Entity Framework, and integration patterns
- **Security Considerations**: Commands respect security and privacy requirements

### Workflow Integration
Commands integrate with existing development workflows:
- **Planning Phase**: `analyze-mode` for understanding existing systems
- **Implementation Phase**: `workflow-mode` for documenting new features
- **Maintenance Phase**: Both modes support ongoing development and documentation

## Usage Scenarios

### Before Implementation
```bash
# Understand existing system
claude analyze-mode
claude analyze the profile matching system

# Plan new feature implementation
claude workflow-mode
claude implement new notification system
```

### During Development
```bash
# Document workflow as you implement
claude workflow-mode
claude implement email automation workflow
```

### For Maintenance
```bash
# Analyze existing components
claude analyze-mode
claude analyze the WhatsApp messaging system
```

## Best Practices

### 1. Mode Selection
- Use `analyze-mode` for understanding and exploration
- Use `workflow-mode` for implementation and documentation
- Combine modes for comprehensive feature development

### 2. Documentation Quality
- Follow "Ultrathink" principles for thorough documentation
- Write for future developers and AI models
- Include implementation examples and patterns

### 3. Architectural Consistency
- Maintain Clean Architecture principles
- Follow existing patterns and conventions
- Respect business domain boundaries

### 4. Security and Privacy
- Consider security implications in all analysis
- Respect privacy requirements for matrimonial data
- Follow established security patterns

## Command Evolution

### Future Enhancements
- Additional specialized modes for specific domains
- Integration with CI/CD pipelines
- Automated documentation generation
- Performance analysis modes

### Feedback and Improvements
- Commands evolve based on development team feedback
- Regular updates to align with codebase changes
- Continuous improvement of documentation quality

---

*This documentation is maintained alongside the command implementations and should be updated when adding new commands or modifying existing ones.*