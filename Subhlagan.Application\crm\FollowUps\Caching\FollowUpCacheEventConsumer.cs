﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;
using Subhlagan.Core;

namespace Subhlagan.Application.FollowUps.Caching
{
    public partial class FollowUpCacheEventConsumer : CacheEventConsumer<FollowUp>
    {
        protected override async Task ClearCacheAsync(FollowUp entity, EntityEventType entityEventType)
        {
            await RemoveByPrefixAsync(PageBaaSDefaults.FollowUpsByProfileIdsPrefix, entity.InitiatorProfileId, entity.MatchedProfileId);
            await RemoveByPrefixAsync(PageBaaSDefaults.FollowUpsByProfileIdsPrefix, entity.MatchedProfileId, entity.InitiatorProfileId);
            await base.ClearCacheAsync(entity, entityEventType);
        }
    }
}
