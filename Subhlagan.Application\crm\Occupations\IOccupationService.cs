﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Occupations
{
    public interface IOccupationService
    {
        Task InsertOccupationAsync(Occupation occupation);
        Task UpdateOccupationAsync(Occupation occupation);
        Task DeleteOccupationAsync(Occupation occupation);
        Task<Occupation> GetOccupationByIdAsync(int occupationId);
        Task<IList<Occupation>> GetAllOccupationsAsync(bool showHidden = false);
        Task<string> GetOccupationNameByIdAsync(int occupationId);
    }

}
