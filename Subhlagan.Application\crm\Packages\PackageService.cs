﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Subhlagan.Core;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Users;

namespace Subhlagan.Application.Packages
{
    /// <summary>
    /// Package service
    /// </summary>
    public partial class PackageService : IPackageService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Package> _packageRepository;
        private readonly IUserService _userService;

        #endregion

        #region Ctor

        public PackageService(EnhancedEntityRepository<Package> packageRepository, IUserService userService)
        {
            _packageRepository = packageRepository;
            _userService = userService;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Inserts a package
        /// </summary>
        /// <param name="package">Package</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public virtual async Task InsertPackageAsync(Package package)
        {
            if (package == null)
                throw new ArgumentNullException(nameof(package));

            package.Name = CommonHelper.EnsureNotNull(package.Name);
            package.Code = CommonHelper.EnsureNotNull(package.Code);

            package.Name = package.Name.Trim();
            package.Code = package.Code.Trim();

            package.Name = CommonHelper.EnsureMaximumLength(package.Name, 255);
            package.Code = CommonHelper.EnsureMaximumLength(package.Code, 255);
            await _packageRepository.InsertAsync(package);
        }

        /// <summary>
        /// Updates a package
        /// </summary>
        /// <param name="package">Package</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public virtual async Task UpdatePackageAsync(Package package)
        {
            if (package == null)
                throw new ArgumentNullException(nameof(package));

            package.Name = CommonHelper.EnsureNotNull(package.Name);
            package.Code = CommonHelper.EnsureNotNull(package.Code);

            package.Name = package.Name.Trim();
            package.Code = package.Code.Trim();

            package.Name = CommonHelper.EnsureMaximumLength(package.Name, 255);
            package.Code = CommonHelper.EnsureMaximumLength(package.Code, 255);
            await _packageRepository.UpdateAsync(package);
        }

        /// <summary>
        /// Deletes a package
        /// </summary>
        /// <param name="package">Package</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        public virtual async Task DeletePackageAsync(Package package)
        {
            if (package == null)
                throw new ArgumentNullException(nameof(package));

            await _packageRepository.DeleteAsync(package);
        }

        /// <summary>
        /// Gets a package by identifier
        /// </summary>
        /// <param name="packageId">The package identifier</param>
        /// <returns>
        /// A task that represents the asynchronous operation
        /// The task result contains the package
        /// </returns>
        public virtual async Task<Package> GetPackageByIdAsync(int packageId)
        {
            return await _packageRepository.GetByIdAsync(packageId, cache => default);
        }

        public virtual async Task<IPagedList<Package>> GetAllPackagesAsync(
            string name = null,
            decimal? priceFrom = null,
            decimal? priceTo = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false)
        {
            var packages = await _packageRepository.GetAllPagedAsync(query =>
            {
                if (!string.IsNullOrWhiteSpace(name))
                    query = query.Where(p => p.Name.Contains(name));

                if (priceFrom.HasValue)
                    query = query.Where(p => p.Price >= priceFrom.Value);

                if (priceTo.HasValue)
                    query = query.Where(p => p.Price <= priceTo.Value);

                query = query.Where(c => !c.Deleted);

                query = query.OrderBy(p => p.Name).ThenBy(p => p.Price);

                return query;
            }, pageIndex, pageSize, getOnlyTotalCount);

            return packages;
        }

        public async Task<IList<Package>> GetAllPackagesAsync(bool showHidden = false)
        {
            var packages = await _packageRepository.GetAllAsync(query =>
            {
                query = query.Where(c => !c.Deleted);

                query = query.OrderBy(p => p.Name).ThenBy(p => p.Price);

                return query;

            }, cache => default);

            if (!showHidden)
                packages = packages.Where(p => p.Active).ToList();

            return packages;
        }

        #endregion
    }

}
