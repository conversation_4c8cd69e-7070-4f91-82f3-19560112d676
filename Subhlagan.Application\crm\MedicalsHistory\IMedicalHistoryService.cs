﻿using Subhlagan.Core;
using Subhlagan.Core.Domain;
using System.Threading.Tasks;

namespace Subhlagan.Application.MedicalsHistory
{
    public partial interface IMedicalHistoryService
    {
        Task InsertMedicalHistoryAsync(MedicalHistory medicalHistory);
        Task UpdateMedicalHistoryAsync(MedicalHistory medicalHistory);
        Task DeleteMedicalHistoryAsync(MedicalHistory medicalHistory);
        Task<MedicalHistory> GetMedicalHistoryByIdAsync(int medicalHistoryId);
        Task<IPagedList<MedicalHistory>> GetAllMedicalHistoriesAsync(
            int? bloodGroupId = null,
            bool? hasChronicIllness = null,
            bool? hasAllergies = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false);
    }

}
