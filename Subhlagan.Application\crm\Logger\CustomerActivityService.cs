﻿using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using static LinqToDB.SqlQuery.SqlPredicate;
using Subhlagan.Core.Domain.Customers;
using DocumentFormat.OpenXml.Office2010.Excel;

namespace Subhlagan.Application.Logger
{
    public class CustomerActivityService : Subhlagan.Application.Logging.CustomerActivityService
    {
        #region Fields

        private readonly IRepository<ActivityLog> _sactivityLogRepository;
        private readonly IRepository<Subhlagan.Core.Domain.Logging.ActivityLogType> _activityLogTypeRepository;
        private readonly IWebHelper _webHelper;
        private readonly IWorkContext _workContext;
        private readonly IRepository<CustomerCustomerRoleMapping> _customerCustomerRoleMapping;

        public CustomerActivityService(IRepository<Subhlagan.Core.Domain.Logging.ActivityLog> activityLogRepository, IRepository<ActivityLog> sactivityLogRepository, IRepository<Subhlagan.Core.Domain.Logging.ActivityLogType> activityLogTypeRepository, IWebHelper webHelper, IWorkContext workContext, IRepository<CustomerCustomerRoleMapping> customerCustomerRoleMapping) :
            base(activityLogRepository, activityLogTypeRepository, webHelper, workContext)

        {
            _sactivityLogRepository = sactivityLogRepository;
            _activityLogTypeRepository = activityLogTypeRepository;
            _webHelper = webHelper;
            _workContext = workContext;
            _customerCustomerRoleMapping = customerCustomerRoleMapping;
        }

        #endregion

        public virtual async Task<ActivityLog> InsertActivityAsync(string systemKeyword, string comment, BaseEntity entity = null, int profileId = 0, int matchedProfileId = 0)
        {
            var customer = await _workContext.GetCurrentCustomerAsync();
            if (customer == null)
                return null;

            //try to get activity log type by passed system keyword
            var activityLogType = (await GetAllActivityTypesAsync()).FirstOrDefault(type => type.SystemKeyword.Equals(systemKeyword));
            if (!activityLogType?.Enabled ?? true)
                return null;

            //insert log item
            var logItem = new ActivityLog
            {
                ActivityLogTypeId = activityLogType.Id,
                EntityId = entity?.Id,
                EntityName = entity?.GetType().Name,
                CustomerId = customer.Id,
                Comment = CommonHelper.EnsureMaximumLength(comment ?? string.Empty, 4000),
                CreatedOnUtc = DateTime.UtcNow,
                IpAddress = _webHelper.GetCurrentIpAddress(),
                ProfileId = profileId,
                MatchedProfileId = matchedProfileId,
            };
            await _sactivityLogRepository.InsertAsync(logItem);

            return logItem;
        }

        public virtual async Task<IPagedList<ActivityLog>> GetAllActivitiesAsync(DateTime? createdOnFrom = null, DateTime? createdOnTo = null,
            int? customerId = null, int? activityLogTypeId = null, string ipAddress = null, string entityName = null, int? entityId = null,
            int pageIndex = 0, int pageSize = int.MaxValue, IList<int> profileIds = null, IList<int> activityLogTypeIds = null,
            int customerRoleID = 0)
        {
            var activityLogTypes = await GetAllActivityTypesAsync();
            var query = _sactivityLogRepository.Table;
            if (activityLogTypes != null)
                query = query.Where(logItem => activityLogTypes.Any(x => x.Id == logItem.ActivityLogTypeId));

            //filter by IP
            if (!string.IsNullOrEmpty(ipAddress))
                query = query.Where(logItem => logItem.IpAddress.Contains(ipAddress));

            //filter by creation date
            if (createdOnFrom.HasValue)
                query = query.Where(logItem => createdOnFrom.Value <= logItem.CreatedOnUtc);
            if (createdOnTo.HasValue)
                query = query.Where(logItem => createdOnTo.Value >= logItem.CreatedOnUtc);

            //filter by log type
            if (activityLogTypeId.HasValue && activityLogTypeId.Value > 0)
                query = query.Where(logItem => activityLogTypeId == logItem.ActivityLogTypeId);

            //filter by customer
            if (customerId.HasValue && customerId.Value > 0)
                query = query.Where(logItem => customerId.Value == logItem.CustomerId);

            //filter by entity
            if (!string.IsNullOrEmpty(entityName))
                query = query.Where(logItem => logItem.EntityName.Equals(entityName));
            if (entityId.HasValue && entityId.Value > 0)
                query = query.Where(logItem => entityId.Value == logItem.EntityId);

            if (profileIds!=null && profileIds.Any())
                query = query.Where(logItem => profileIds.Contains(logItem.ProfileId) || profileIds.Contains(logItem.MatchedProfileId));

            //filter by multiple log type
            if (activityLogTypeIds != null && activityLogTypeIds.Any())
                query = query.Where(logItem => activityLogTypeIds.Contains(logItem.ActivityLogTypeId));

            query = query.OrderByDescending(logItem => logItem.CreatedOnUtc);//.ThenBy(logItem => logItem.Id);

            if (customerRoleID > 0)
            {
                var subQuery = from activityLog in query
                               join customerRoleMapping in _customerCustomerRoleMapping.Table
                                   on activityLog.CustomerId equals customerRoleMapping.CustomerId
                               where (customerRoleMapping.CustomerRoleId == customerRoleID)
                               group activityLog by activityLog.Id into grp
                               where grp.Count() > 0
                               select grp.Key;
                query = query.Where(p => subQuery.Contains(p.Id));
            }
            return await query.ToPagedListAsync(pageIndex, pageSize);

        }

        public virtual async Task UpdateActivitiesAsync(ActivityLog activityLog)
        {
            if (activityLog == null)
                throw new ArgumentNullException(nameof(activityLog));


            await _sactivityLogRepository.UpdateAsync(activityLog);
        }

        public virtual async Task<ActivityLog> GetActivityLogByIdAsync(int activityLogId)
        {
            return await _sactivityLogRepository.GetByIdAsync(activityLogId);
        }

        public virtual async Task<IList<ActivityLog>> GetActivityLogByIdsAsync(IList<int> ids)
        {
            return await _sactivityLogRepository.GetByIdsAsync(ids);
        }
    }
}
