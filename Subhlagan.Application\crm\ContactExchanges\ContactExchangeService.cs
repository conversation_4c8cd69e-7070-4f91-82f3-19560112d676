﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Profiles;

namespace Subhlagan.Application.ContactExchanges
{
    public partial class ContactExchangeService : IContactExchangeService
    {
        #region Fields

        private readonly EnhancedEntityRepository<ContactExchange> _contactExchangeRepository;

        #endregion

        #region Ctor

        public ContactExchangeService(EnhancedEntityRepository<ContactExchange> contactExchangeRepository)
        {
            _contactExchangeRepository = contactExchangeRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertContactExchangeAsync(ContactExchange contactExchange)
        {
            if (contactExchange == null)
                throw new ArgumentNullException(nameof(contactExchange));

            await _contactExchangeRepository.InsertAsync(contactExchange);
        }

        public virtual async Task UpdateContactExchangeAsync(ContactExchange contactExchange)
        {
            if (contactExchange == null)
                throw new ArgumentNullException(nameof(contactExchange));

            await _contactExchangeRepository.UpdateAsync(contactExchange);
        }

        public virtual async Task DeleteContactExchangeAsync(ContactExchange contactExchange)
        {
            if (contactExchange == null)
                throw new ArgumentNullException(nameof(contactExchange));

            await _contactExchangeRepository.DeleteAsync(contactExchange);
        }

        public virtual async Task<ContactExchange> GetContactExchangeByIdAsync(int contactExchangeId)
        {
            return await _contactExchangeRepository.GetByIdAsync(contactExchangeId);
        }

        public virtual async Task<IPagedList<ContactExchange>> GetAllContactExchangesAsync(int initiatorProfileId = 0,
            int matchedProfileId = 0, DateTime? contactExchangeDateFrom = null,
            DateTime? contactExchangeDateTo = null, int userId = 0, int pageIndex = 0,
            int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            IList<int> initiatorProfileIds = null,
            IList<int> matchedProfileIds = null, IList<int> contactExchangeIds = null)
        {
            var query = _contactExchangeRepository.Table;
            if (initiatorProfileId > 0)
                query = query.Where(ce => ce.InitiatorProfileId == initiatorProfileId);
            if (matchedProfileId > 0)
                query = query.Where(ce => ce.MatchedProfileId == matchedProfileId);

            if (userId > 0)
                query = query.Where(ce => ce.UserId == userId);

            if (contactExchangeDateFrom.HasValue)
                query = query.Where(m => m.CreatedOnUtc >= contactExchangeDateFrom.Value);

            // Filter by meeting end date
            if (contactExchangeDateTo.HasValue)
                query = query.Where(m => m.CreatedOnUtc <= contactExchangeDateTo.Value);

            // filter by initiator Profile Id
            if (initiatorProfileIds != null && initiatorProfileIds.Any())
                query = query.Where(m => initiatorProfileIds.Contains(m.InitiatorProfileId));

            // filter by matched Profile ID
            if (matchedProfileIds != null && matchedProfileIds.Any())
                query = query.Where(m => matchedProfileIds.Contains(m.MatchedProfileId));

            // filter by Contact Exchange Ids
            if (contactExchangeIds != null && contactExchangeIds.Any())
                query = query.Where(m => contactExchangeIds.Contains(m.Id));

            query = query.OrderByDescending(m => m.Id);

            // return query.OrderByDescending(ce => ce.CreatedOnUtc);
            return await query.ToPagedListAsync(pageIndex, pageSize, getOnlyTotalCount);

            //var query = from c in _contactExchangeRepository.Table
            //            orderby c.CreatedOnUtc
            //            select c;

            //return await query.ToListAsync();
        }


        #endregion
    }
}

