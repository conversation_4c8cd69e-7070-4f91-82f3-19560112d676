-- =====================================================================
-- Database Backup Scripts for SubhLaganNew_v1
-- Purpose: Create comprehensive backup before unused table deletion
-- =====================================================================

USE master;

DECLARE @BackupPath NVARCHAR(500) = 'C:\DatabaseBackups\'; -- Modify this path as needed
DECLARE @DatabaseName NVARCHAR(128) = 'SubhLaganNew_v1';
DECLARE @BackupFileName NVARCHAR(500);
DECLARE @BackupSQL NVARCHAR(MAX);

-- Create backup filename with timestamp
SET @BackupFileName = @BackupPath + @DatabaseName + '_BeforeTableCleanup_' + 
    REPLACE(REPLACE(REPLACE(CONVERT(VARCHAR(19), GETDATE(), 120), '-', ''), ':', ''), ' ', '_') + '.bak';

PRINT '========================================';
PRINT 'CREATING DATABASE BACKUP';
PRINT 'Database: ' + @DatabaseName;
PRINT 'Backup Location: ' + @BackupFileName;
PRINT '========================================';

-- Full database backup
SET @BackupSQL = 'BACKUP DATABASE [' + @DatabaseName + '] 
    TO DISK = ''' + @BackupFileName + '''
    WITH 
        FORMAT, 
        COMPRESSION,
        CHECKSUM,
        DESCRIPTION = ''Full backup before unused table cleanup - '' + CONVERT(VARCHAR(20), GETDATE()),
        NAME = ''' + @DatabaseName + '_BeforeTableCleanup'',
        STATS = 10';

PRINT 'Executing backup command...';
EXEC sp_executesql @BackupSQL;

PRINT 'Backup completed successfully!';
PRINT '';

-- Verify backup
PRINT 'Verifying backup integrity...';
DECLARE @VerifySQL NVARCHAR(MAX) = 'RESTORE VERIFYONLY FROM DISK = ''' + @BackupFileName + '''';
EXEC sp_executesql @VerifySQL;
PRINT 'Backup verification completed!';
PRINT '';

-- Generate restore script for reference
PRINT '========================================';
PRINT 'RESTORE SCRIPT (for reference):';
PRINT '========================================';
PRINT 'USE master;';
PRINT 'RESTORE DATABASE [' + @DatabaseName + '_Restored] FROM DISK = ''' + @BackupFileName + '''';
PRINT 'WITH REPLACE, STATS = 10;';
PRINT '';

-- Create table-specific backup scripts for critical tables
PRINT '========================================';
PRINT 'TABLE-SPECIFIC BACKUP SCRIPTS';
PRINT '========================================';

USE SubhLaganNew_v1;

-- Generate CREATE TABLE scripts for tables that might be deleted
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);

-- This would need to be customized based on actual analysis results
PRINT '-- Scripts to recreate potentially deleted tables:';
PRINT '';

-- Example for AstroCity (if it exists)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'AstroCity')
BEGIN
    PRINT '-- Backup data from AstroCity table';
    PRINT 'SELECT * INTO AstroCity_Backup_' + REPLACE(REPLACE(CONVERT(VARCHAR(10), GETDATE(), 120), '-', ''), ' ', '') + ' FROM AstroCity;';
    PRINT '';
END;

-- Generate scripts for other potentially unused tables
DECLARE table_cursor CURSOR FOR
SELECT t.name
FROM sys.tables t
LEFT JOIN sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
WHERE t.is_ms_shipped = 0 
    AND ISNULL(p.rows, 0) = 0  -- Empty tables
    AND t.name NOT IN (
        'Profile', 'User', 'Customer', 'Package', 'Community', 'City', 
        'ActionStatus', 'Hobby', 'Gotra', 'Office', 'Occupation'  -- Known active tables
    )
ORDER BY t.name;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '-- Backup script for ' + @TableName;
    PRINT 'IF EXISTS (SELECT * FROM sys.tables WHERE name = ''' + @TableName + ''')';
    PRINT 'BEGIN';
    PRINT '    SELECT * INTO ' + @TableName + '_Backup_' + REPLACE(REPLACE(CONVERT(VARCHAR(10), GETDATE(), 120), '-', ''), ' ', '') + ' FROM ' + @TableName + ';';
    PRINT 'END;';
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END;

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT 'BACKUP PROCESS COMPLETE';
PRINT 'Remember to:';
PRINT '1. Verify backup file exists and is accessible';
PRINT '2. Test restore process in development environment';
PRINT '3. Keep backup file safe until cleanup is verified';
PRINT '========================================';