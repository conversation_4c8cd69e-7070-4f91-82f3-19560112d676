﻿using System;
using System.Collections.Generic;
using System.IO.Packaging;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Users;

namespace Subhlagan.Application.Gotras
{
    public partial class GotraService : IGotraService
    {
        #region Fields

        private readonly EnhancedEntityRepository<Gotra> _gotraRepository;
        private readonly IUserService _userService;
        #endregion

        #region Ctor

        public GotraService(EnhancedEntityRepository<Gotra> gotraRepository, IUserService userService)
        {
            _gotraRepository = gotraRepository;
            _userService = userService;
        }

        #endregion

        #region Methods

        public virtual async Task InsertGotraAsync(Gotra gotra)
        {
            if (gotra == null)
                throw new ArgumentNullException(nameof(gotra));

            gotra.Name = CommonHelper.EnsureNotNull(gotra.Name);
            gotra.Name = gotra.Name.Trim();
            gotra.Name = CommonHelper.EnsureMaximumLength(gotra.Name, 255);
            await _userService.SetCurrentUserIdAsync(gotra);

            await _gotraRepository.InsertAsync(gotra);
        }

        public virtual async Task UpdateGotraAsync(Gotra gotra)
        {
            if (gotra == null)
                throw new ArgumentNullException(nameof(gotra));

            gotra.Name = CommonHelper.EnsureNotNull(gotra.Name);
            gotra.Name = gotra.Name.Trim();
            gotra.Name = CommonHelper.EnsureMaximumLength(gotra.Name, 255);
            await _userService.SetCurrentUserIdAsync(gotra);

            await _gotraRepository.UpdateAsync(gotra);
        }

        public virtual async Task DeleteGotraAsync(Gotra gotra)
        {
            if (gotra == null)
                throw new ArgumentNullException(nameof(gotra));
            await _userService.SetCurrentUserIdAsync(gotra);

            await _gotraRepository.DeleteAsync(gotra);
        }

        public virtual async Task<Gotra> GetGotraByIdAsync(int gotraId)
        {
            return await _gotraRepository.GetByIdAsync(gotraId, cache => default);
        }

        public virtual async Task<IList<Gotra>> GetAllGotrasAsync(int communityId = 0, bool showHidden = false)
        {
            var gotras = await _gotraRepository.GetAllAsync(query =>
            {
                query = query.Where(g => !g.Deleted);
                query = query.OrderBy(g => g.DisplayOrder);
                query = query.OrderBy(c => c.Name);

                return query;
            }, cache => default);

            if (communityId > 0)
                gotras = gotras.Where(c => c.CommunityId == communityId).ToList();

            if (!showHidden)
                gotras = gotras.Where(c => c.Active).ToList();


            return gotras;
        }

        public virtual async Task<IList<Gotra>> GetGotrasByCommunityIdAsync(int communityId)
        {
            return await GetAllGotrasAsync(communityId);
        }

        #endregion
    }
}

