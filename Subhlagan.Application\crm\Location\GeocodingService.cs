﻿using Subhlagan.Core;
using Subhlagan.Application.Configuration;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Location
{
    public class GeocodingService : IGeocodingService
    {
        
        private readonly ISettingService _settingService;

        public GeocodingService( ISettingService settingService)
        {
            
            _settingService = settingService;
        }

        public async Task<GeocodingResult> FetchGeocodingDataAsync(string locationName)
        {
            
            using var httpClient = new HttpClient();
            var url = $"https://maps.googleapis.com/maps/api/geocode/json?address={Uri.EscapeDataString(locationName)}&key={CompanyInfo.GoogleMapsApiKey}";
            var response = await httpClient.GetStringAsync(url);
            var json = JObject.Parse(response);

            if (json["status"]?.ToString() == "OK")
            {
                var result = json["results"]?.First;
                if (result != null)
                {
                    var latitude = result["geometry"]?["location"]?["lat"]?.Value<double>() ?? 0;
                    var longitude = result["geometry"]?["location"]?["lng"]?.Value<double>() ?? 0;
                    var latitudeDirection = latitude >= 0 ? "N" : "S";
                    var longitudeDirection = longitude >= 0 ? "E" : "W";

                    var addressComponents = result["address_components"];
                    string cityName = addressComponents
                        ?.FirstOrDefault(ac => ac["types"]?.ToObject<List<string>>()?.Contains("locality") == true)?["long_name"]
                        ?.ToString();
                    string villageName = addressComponents
                        ?.FirstOrDefault(ac => ac["types"]?.ToObject<List<string>>()?.Contains("sublocality_level_1") == true)?["long_name"]
                        ?.ToString();

                    var stateProvinceComponent = addressComponents
                        ?.FirstOrDefault(ac => ac["types"]?.ToObject<List<string>>()?.Contains("administrative_area_level_1") == true);
                    string stateProvince = stateProvinceComponent?["long_name"]?.ToString();
                    string stateProvinceAbbreviation = stateProvinceComponent?["short_name"]?.ToString();

                    var countryComponent = addressComponents
                        ?.FirstOrDefault(ac => ac["types"]?.ToObject<List<string>>()?.Contains("country") == true);
                    string country = countryComponent?["long_name"]?.ToString();
                    string countryAbbreviation = countryComponent?["short_name"]?.ToString();

                    return new GeocodingResult
                    {
                        CityName = cityName,
                        VillageName = villageName,
                        Latitude = latitude,
                        Longitude = longitude,
                        LatitudeDirection = latitudeDirection,
                        LongitudeDirection = longitudeDirection,
                        StateProvince = stateProvince,
                        StateProvinceAbbreviation = stateProvinceAbbreviation,
                        Country = country,
                        CountryAbbreviation = countryAbbreviation,
                    };
                }
            }

            return null;
        }

        public decimal CalculateGMTOffset(decimal longitude)
        {
            // 1. Validate input
            if (longitude < -180 || longitude > 180)
            {
                throw new ArgumentOutOfRangeException(nameof(longitude), "Longitude must be between -180 and 180 degrees.");
            }

            // 2. Convert longitude to offset in hours (15° per hour)
            decimal totalHours = longitude / 15;

            // 3. Return the decimal format
            return Math.Round(totalHours, 2); // Rounds to 2 decimal places
        }
    }

    public class GeocodingResult
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string LatitudeDirection { get; set; }
        public string LongitudeDirection { get; set; }
        public string VillageName { get; set; }
        public string CityName { get; set; }
        public string? StateProvince { get; set; }
        public string? Country { get; set; }
        public string StateProvinceAbbreviation { get; set; }
        public string CountryAbbreviation { get; set; }
    }
}
