﻿using Subhlagan.Core.Domain;
using Subhlagan.Application.Caching;
using Subhlagan.Application.Common;
using Subhlagan.Core;
using System.Threading.Tasks;

namespace Subhlagan.Application.FeedbackForms.Caching
{
    public partial class FeedbackFormCacheEventConsumer : CacheEventConsumer<FeedbackForm>
    {
        protected override async Task ClearCacheAsync(FeedbackForm entity, EntityEventType entityEventType)
        {
            await RemoveByPrefixAsync(PageBaaSDefaults.FeedbackFormByProfileIdPrefix, entity.ProfileId);
            await base.ClearCacheAsync(entity, entityEventType);

        }
    }
}
