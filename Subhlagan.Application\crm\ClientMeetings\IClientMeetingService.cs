﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.Meetings
{
    public interface IClientMeetingService
    {
        Task InsertClientMeetingAsync(ClientMeeting clientMeeting);
        Task UpdateClientMeetingAsync(ClientMeeting clientMeeting);
        Task DeleteClientMeetingAsync(ClientMeeting clientMeeting);
        Task<ClientMeeting> GetClientMeetingByIdAsync(int clientMeetingId);
        Task<IPagedList<ClientMeeting>> GetAllClientMeetingsAsync(
            DateTime? meetingDateFrom = null,
            DateTime? meetingDateTo = null,
            int profileId = 0,
            IList<int> userIds = null,
            int pageIndex = 0,
            int pageSize = int.MaxValue,
            bool getOnlyTotalCount = false, 
            string Name = null, IList<int> profileIds = null,
            IList<int> assignedUserIds = null, IList<int> meetingIds = null, List<int> matchResultIds = null,
            int venueId = 0);

    }

}
