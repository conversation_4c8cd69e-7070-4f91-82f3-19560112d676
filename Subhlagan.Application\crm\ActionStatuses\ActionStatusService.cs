﻿using Subhlagan.Core;

using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;

namespace Subhlagan.Application.ActionStatuses
{
    /// <summary>
    /// Action status service
    /// </summary>
    public partial class ActionStatusService : IActionStatusService
    {
        #region Fields

        private readonly EnhancedEntityRepository<ActionStatus> _actionStatusRepository;

        #endregion

        #region Ctor

        public ActionStatusService(EnhancedEntityRepository<ActionStatus> actionStatusRepository)
        {
            _actionStatusRepository = actionStatusRepository;
        }

        #endregion

        #region Methods

        public virtual async Task InsertActionStatusAsync(ActionStatus actionStatus)
        {
            if (actionStatus == null)
                throw new ArgumentNullException(nameof(actionStatus));

            actionStatus.Name = CommonHelper.EnsureNotNull(actionStatus.Name);
            actionStatus.Name = actionStatus.Name.Trim();
            actionStatus.Name = CommonHelper.EnsureMaximumLength(actionStatus.Name, 255);

            await _actionStatusRepository.InsertAsync(actionStatus);
        }

        public virtual async Task UpdateActionStatusAsync(ActionStatus actionStatus)
        {
            if (actionStatus == null)
                throw new ArgumentNullException(nameof(actionStatus));

            actionStatus.Name = CommonHelper.EnsureNotNull(actionStatus.Name);
            actionStatus.Name = actionStatus.Name.Trim();
            actionStatus.Name = CommonHelper.EnsureMaximumLength(actionStatus.Name, 255);

            await _actionStatusRepository.UpdateAsync(actionStatus);
        }

        public virtual async Task DeleteActionStatusAsync(ActionStatus actionStatus)
        {
            if (actionStatus == null)
                throw new ArgumentNullException(nameof(actionStatus));

            if (actionStatus.IsSystemStatus)
                throw new KsException("System status could not be deleted");

            await _actionStatusRepository.DeleteAsync(actionStatus);
        }

        public virtual async Task<ActionStatus> GetActionStatusByIdAsync(int actionStatusId)
        {
            return await _actionStatusRepository.GetByIdAsync(actionStatusId, cache => default);
        }

        public virtual async Task<IList<ActionStatus>> GetAllActionStatusesAsync(bool showHidden = false)
        {
            var actionStatuses = await _actionStatusRepository.GetAllAsync(query =>
            {
                return from a in query
                       orderby a.DisplayOrder
                       select a;
            });

            if (!showHidden)
                actionStatuses = actionStatuses.Where(a => a.Active).ToList();

            return actionStatuses;
        }

        public async Task<string> GetActionStatusNameByIdAsync(int actionStatusId)
        {
            return (await GetActionStatusByIdAsync(actionStatusId))?.Name;
        }

        public async Task<bool> IsNoAnswerActionStatus(int actionStatusId)
        {
            return (await GetActionStatusByIdAsync(actionStatusId)).SystemName == PageBaaSDefaults.ActionStatusNoAnswer;
        }

        public async Task<ActionStatus> GetActionStatusBySystemNameAsync(string systemName)
        {
            if (string.IsNullOrEmpty(systemName))
                return null;

            var actionStatuses = await _actionStatusRepository.GetAllAsync(query =>
            {
                query = query.Where(x => x.SystemName == systemName);
                return query;
            });

            return actionStatuses.FirstOrDefault();
        }

        #endregion
    }

}
