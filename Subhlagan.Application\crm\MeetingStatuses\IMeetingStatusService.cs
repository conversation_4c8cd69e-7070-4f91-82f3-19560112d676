﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.MeetingStatuses
{
    public interface IMeetingStatusService
    {
        Task InsertMeetingStatusAsync(MeetingStatus meetingStatus);
        Task UpdateMeetingStatusAsync(MeetingStatus meetingStatus);
        Task DeleteMeetingStatusAsync(MeetingStatus meetingStatus);
        Task<MeetingStatus> GetMeetingStatusByIdAsync(int meetingStatusId);
        Task<IPagedList<MeetingStatus>> GetAllMeetingStatusAsync(DateTime? meetingStatusFromUtc = null, DateTime? meetingStatusToUtc = null,
            int? profileId = 0, int maleProfileId = 0, int femaleProfileId = 0,
            int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false,
            IList<int> profileIds = null);
    }

}
