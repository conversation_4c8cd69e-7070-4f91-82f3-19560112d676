﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;

namespace Subhlagan.Application.MarriageConfirmations
{
    public partial class MarriageConfirmationService : IMarriageConfirmationService
    {
        private readonly EnhancedEntityRepository<MarriageConfirmation> _marriageConfirmationRepository;

        public MarriageConfirmationService(EnhancedEntityRepository<MarriageConfirmation> marriageConfirmationRepository)
        {
            _marriageConfirmationRepository = marriageConfirmationRepository;
        }

        public virtual async Task InsertMarriageConfirmationAsync(MarriageConfirmation marriageConfirmation)
        {
            if (marriageConfirmation == null)
                throw new ArgumentNullException(nameof(marriageConfirmation));

            await _marriageConfirmationRepository.InsertAsync(marriageConfirmation);
        }

        public virtual async Task UpdateMarriageConfirmationAsync(MarriageConfirmation marriageConfirmation)
        {
            if (marriageConfirmation == null)
                throw new ArgumentNullException(nameof(marriageConfirmation));

            await _marriageConfirmationRepository.UpdateAsync(marriageConfirmation);
        }

        public virtual async Task DeleteMarriageConfirmationAsync(MarriageConfirmation marriageConfirmation)
        {
            if (marriageConfirmation == null)
                throw new ArgumentNullException(nameof(marriageConfirmation));

            await _marriageConfirmationRepository.DeleteAsync(marriageConfirmation);
        }

        public virtual async Task<MarriageConfirmation> GetMarriageConfirmationByIdAsync(int marriageConfirmationId)
        {
            return await _marriageConfirmationRepository.GetByIdAsync(marriageConfirmationId, cache => default);
        }

        public virtual async Task<IList<MarriageConfirmation>> GetAllMarriageConfirmationsAsync()
        {
            return await _marriageConfirmationRepository.GetAllAsync(query =>
            {
                return from mc in query
                       orderby mc.Id
                       select mc;
            }, cache => default);
        }
    }
}
