﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Subhlagan.Core;
using Subhlagan.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Application.Caching;

namespace Subhlagan.Application.ProfileAttributeGroups
{
    public partial class ProfileAttributeGroupService : IProfileAttributeGroupService
    {
        private readonly EnhancedEntityRepository<ProfileAttributeGroup> _profileAttributeGroupRepository;

        public ProfileAttributeGroupService(EnhancedEntityRepository<ProfileAttributeGroup> profileAttributeGroupRepository)
        {
            _profileAttributeGroupRepository = profileAttributeGroupRepository;
        }

        public async Task InsertProfileAttributeGroupAsync(ProfileAttributeGroup profileAttributeGroup)
        {
            if (profileAttributeGroup == null)
                throw new ArgumentNullException(nameof(profileAttributeGroup));

            profileAttributeGroup.Name = CommonHelper.EnsureNotNull(profileAttributeGroup.Name);
            profileAttributeGroup.Name = profileAttributeGroup.Name.Trim();
            profileAttributeGroup.Name = CommonHelper.EnsureMaximumLength(profileAttributeGroup.Name, 255);

            await _profileAttributeGroupRepository.InsertAsync(profileAttributeGroup);
        }

        public async Task UpdateProfileAttributeGroupAsync(ProfileAttributeGroup profileAttributeGroup)
        {
            if (profileAttributeGroup == null)
                throw new ArgumentNullException(nameof(profileAttributeGroup));

            profileAttributeGroup.Name = CommonHelper.EnsureNotNull(profileAttributeGroup.Name);
            profileAttributeGroup.Name = profileAttributeGroup.Name.Trim();
            profileAttributeGroup.Name = CommonHelper.EnsureMaximumLength(profileAttributeGroup.Name, 255);

            await _profileAttributeGroupRepository.UpdateAsync(profileAttributeGroup);
        }

        public async Task DeleteProfileAttributeGroupAsync(ProfileAttributeGroup profileAttributeGroup)
        {
            if (profileAttributeGroup == null)
                throw new ArgumentNullException(nameof(profileAttributeGroup));

            await _profileAttributeGroupRepository.DeleteAsync(profileAttributeGroup);
        }

        public async Task<ProfileAttributeGroup> GetProfileAttributeGroupByIdAsync(int profileAttributeGroupId)
        {
            return await _profileAttributeGroupRepository.GetByIdAsync(profileAttributeGroupId, cache => default);
        }

        public async Task<IList<ProfileAttributeGroup>> GetAllProfileAttributeGroupsAsync()
        {
            var profileAttributeGroups = await _profileAttributeGroupRepository.GetAllAsync(query =>
            {
                return from p in query
                       orderby p.DisplayOrder, p.Id
                       select p;
            }, cache => default);

            return profileAttributeGroups;
        }
    }

}
