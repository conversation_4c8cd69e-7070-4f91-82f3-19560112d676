﻿using Subhlagan.Core;

using Subhlagan.Core.Domain.Common;
using Subhlagan.Core.Domain.Directory;



using Subhlagan.Core.Infrastructure;
using Subhlagan.Core.Domain;
using Subhlagan.Application.Common;
using Subhlagan.Application.Configuration;
using Subhlagan.Application.Directory;
using Subhlagan.Application.Helpers;
using Subhlagan.Application.Html;
using Subhlagan.Application.Localization;
using Subhlagan.Application.Media;

using QuestPDF.Helpers;
using Subhlagan.Core.Domain.Localization;
using QuestPDF.Fluent;
using Subhlagan.Application.Profiles;
using Subhlagan.Application.Cities;
using Subhlagan.Application.Communities;
using Subhlagan.Application.Gotras;
using Subhlagan.Application.Hobbies;
using Subhlagan.Application.Occupations;
using Subhlagan.Application.Qualifications;
using Subhlagan.Application.EducationAreas;
using Subhlagan.Application.Educations;
using Subhlagan.Core.Domain.Enum;
using Subhlagan.Application;
using Subhlagan.Application.FamilyMembers;
using Subhlagan.Application.Users;
using Subhlagan.Application.OccupationAndBusinesses;
using Subhlagan.Application.MedicalsHistory;

namespace Subhlagan.Application.Pdf
{
    public class SubhLaganPdfService 
    {
        private readonly AddressSettings _addressSettings;
        private readonly IAddressService _addressService;
        private readonly ICountryService _countryService;
        private readonly IDateTimeHelper _dateTimeHelper;
        private readonly IHtmlFormatter _htmlFormatter;
        private readonly ILanguageService _languageService;
        private readonly ILocalizationService _localizationService;
        private readonly IKsFileProvider _fileProvider;
        private readonly IPictureService _pictureService;
        private readonly ISettingService _settingService;
        private readonly IStateProvinceService _stateProvinceService;
        
        
        private readonly IWorkContext _workContext;
        private readonly IProfileService _profileService;
        private readonly ICityService _cityService;
        private readonly ICommunityService _communityService;
        private readonly IGotraService _gotraService;
        private readonly IHobbyService _hobbyService;
        private readonly IQualificationService _qualificationService;
        private readonly IOccupationService _occupationService;
        private readonly IEducationAreaService _educationAreaService;
        private readonly IEducationService _educationService;
        private readonly IFamilyMemberService _familyMemberService;
        private readonly IUserService _userService;
        private readonly IOccupationAndBusinessService _occupationAndBusinessService;
        private readonly IMedicalHistoryService _medicalHistoryService;

        public SubhLaganPdfService(AddressSettings addressSettings, IAddressService addressService, ICountryService countryService, IDateTimeHelper dateTimeHelper, IHtmlFormatter htmlFormatter, ILanguageService languageService, ILocalizationService localizationService, IKsFileProvider fileProvider, IPictureService pictureService,  ISettingService settingService,  IStateProvinceService stateProvinceService,    IWorkContext workContext, IProfileService profileService, ICityService cityService, ICommunityService communityService, IGotraService gotraService, IHobbyService hobbyService, IQualificationService qualificationService, IOccupationService occupationService, IEducationAreaService educationAreaService, IEducationService educationService, IFamilyMemberService familyMemberService, IUserService userService, IOccupationAndBusinessService occupationAndBusinessService, IMedicalHistoryService medicalHistoryService)
        {
            _addressSettings = addressSettings;
            _addressService = addressService;
            _countryService = countryService;
            _dateTimeHelper = dateTimeHelper;
            _htmlFormatter = htmlFormatter;
            _languageService = languageService;
            _localizationService = localizationService;
            _fileProvider = fileProvider;
            _pictureService = pictureService;
            _settingService = settingService;
            
            _stateProvinceService = stateProvinceService;
            
            _workContext = workContext;
            _profileService = profileService;
            _cityService = cityService;
            _communityService = communityService;
            _gotraService = gotraService;
            _hobbyService = hobbyService;
            _qualificationService = qualificationService;
            _occupationService = occupationService;
            _educationAreaService = educationAreaService;
            _educationService = educationService;
            _familyMemberService = familyMemberService;
            _userService = userService;
            _occupationAndBusinessService = occupationAndBusinessService;
            _medicalHistoryService = medicalHistoryService;
        }

        private async Task<string> GetFormattedCityAsync(int cityId)
        {
            var city = await _cityService.GetCityByIdAsync(cityId);
            var country = city != null ? await _countryService.GetCountryByIdAsync(city.CountryId) : null;
            var stateProvince = city != null ? await _stateProvinceService.GetStateProvinceByIdAsync(city.StateProvinceId ?? 0) : null;

            var addressParts = new List<string>
            {
                city?.Name,
                stateProvince?.Name,
                country?.Name
            };

            return string.Join(", ", addressParts.Where(part => !string.IsNullOrWhiteSpace(part)));
        }

        private async Task<string> GetFormattedResidenceAsync(Profile profile)
        {
            var city = await _cityService.GetCityByIdAsync(profile.CityId);
            var country = city != null ? await _countryService.GetCountryByIdAsync(city.CountryId) : null;
            var stateProvince = city != null ? await _stateProvinceService.GetStateProvinceByIdAsync(city.StateProvinceId ?? 0) : null;

            var addressParts = new List<string>
            {
                profile.Customer.StreetAddress,
                //profile.Customer.StreetAddress2,
                //city?.Name,
                //stateProvince?.Name,
                //profile.Customer.ZipPostalCode,
                //country?.Name
            };

            return string.Join(", ", addressParts.Where(part => !string.IsNullOrWhiteSpace(part)));
        }


        private async Task<OccupationAndBusinessItem> MapOccupationAndBusinessAsync(OccupationAndBusiness occupationAndBusiness)
        {
            if (occupationAndBusiness == null)
                return null;

            return new OccupationAndBusinessItem
            {
                ProfileId = occupationAndBusiness.ProfileId,

                // Business Details
                NatureOfBusinessId = occupationAndBusiness.NatureOfBusinessId,
                NatureOfBusiness = occupationAndBusiness.NatureOfBusiness.GetDisplayName(true), // Enum to string
                FirmName = occupationAndBusiness.FirmName,
                AnnualRevenue = occupationAndBusiness.AnnualRevenue,
                EmailOrWebsite = occupationAndBusiness.EmailOrWebsite,
                NumberOfEmployees = occupationAndBusiness.NumberOfEmployees,
                EstablishedDate = occupationAndBusiness.EstablishedDate,
                IndustrySector = occupationAndBusiness.IndustrySector,
                BusinessDetails = occupationAndBusiness.BusinessDetails,//_htmlFormatter.StripTags(_htmlFormatter.ConvertHtmlToPlainText(occupationAndBusiness.BusinessDetails, decode: true)),
                Address = occupationAndBusiness.Address,

                // Professional Details
                Occupation = await _occupationService.GetOccupationNameByIdAsync(occupationAndBusiness.OccupationId), // Fetch Occupation Name
                PhoneNumber = occupationAndBusiness.PhoneNumber
            };
        }

        public virtual async Task PrintBioDataToPdfAsync(Stream stream, Profile profile, Language language = null)
        {
            if (profile == null)
                throw new ArgumentNullException(nameof(profile));

            //language info
            language ??= await _languageService.GetLanguageByIdAsync(profile.Customer?.LanguageId ?? 0);
            if (language?.Published != true)
                language = await _workContext.GetWorkingLanguageAsync();

            var pictures = await _profileService.GetPicturesByProfileIdAsync(profile.Id);
            var picturePaths = new List<string>();

            if (pictures != null && pictures.Count > 0)
            {
                int maxPictures = Math.Min(pictures.Count, 4);
                for (int i = 0; i < maxPictures; i++)
                {
                    var picPath = await _pictureService.GetThumbLocalPathAsync(pictures[i]);
                    if (!string.IsNullOrEmpty(picPath))
                    {
                        picturePaths.Add(picPath);
                    }
                }
            }

            //if (pictures.Any())
            //{
            //    foreach (var pic in pictures)
            //    {
            //        var picPath = await _pictureService.GetThumbLocalPathAsync(pic);
            //        if (!string.IsNullOrEmpty(picPath))
            //        {
            //            picturePaths.Add(picPath);
            //        }
            //    }
            //}

            var createdOn = profile.Customer.CreatedOnUtc;

            // Fetch family members
            var familyMembers = await _familyMemberService.GetAllFamilyMembersAsync(profileId: profile.Id);

            // Prepare individual family members
            var familyMemberItems = new Dictionary<string, List<FamilyMemberItem>>();

            var relations = new (string Relation, Func<FamilyMember, bool> Selector)[]
                {
                    ("Father", f => f.Relation == RelationType.Father),
                    ("Mother", f => f.Relation == RelationType.Mother),
                    ("Brother", f => f.Relation == RelationType.Brother),
                    ("Sister", f => f.Relation == RelationType.Sister),
                    ("Paternal Grandfather", f => f.Relation == RelationType.PaternalGrandfather),
                    ("Paternal Grandmother", f => f.Relation == RelationType.PaternalGrandmother),
                    ("Paternal Uncle", f => f.Relation == RelationType.PaternalUncle),
                    ("Paternal Aunty / Bua", f => f.Relation == RelationType.PaternalAunty),
                    ("Maternal Grandfather", f => f.Relation == RelationType.MaternalGrandfather),
                    ("Maternal Grandmother", f => f.Relation == RelationType.MaternalGrandmother),
                    ("Maternal Uncle / Mama", f => f.Relation == RelationType.MaternalUncle),
                    ("Maternal Aunty / Mausi", f => f.Relation == RelationType.MaternalAunty),
                    //("Son", f => f.Relation == RelationType.Son),
                    //("Daughter", f => f.Relation == RelationType.Daughter),
                    //("Generic Uncle", f => f.Relation == RelationType.Uncle),
                    //("Generic Aunty", f => f.Relation == RelationType.Aunt),
                    //("Grandfather", f => f.Relation == RelationType.Grandfather),
                    //("Grandmother", f => f.Relation == RelationType.Grandmother)
                };

            foreach (var relation in relations)
            {
                var item = new List<FamilyMemberItem>();
                foreach (var familyMember in familyMembers.Where(relation.Selector))
                {
                    if (familyMember != null)
                    {
                        item.Add(new FamilyMemberItem
                        {
                            Name = familyMember.Name.Trim(),
                            Relation = familyMember.Relation.GetDisplayName(true),
                            RelationType = familyMember.Relation,
                            Age = familyMember.Age,
                            WorkStatus = familyMember.WorkStatus.GetDisplayName(true),
                            Occupation = familyMember.Occupation,
                            Employer = familyMember.Employer,
                            Contact = familyMember.Contact,
                            Address = familyMember.Address,
                            NativePlace = familyMember.NativePlace,
                            Education = familyMember.Education,
                            MaritalStatus = familyMember.MaritalStatus.GetDisplayName(true),
                            AnnualIncome = familyMember.AnnualIncome,
                            AdditionalInfo = familyMember.AdditionalInfo,
                            Caste = familyMember.Caste,
                            SubCaste = familyMember.SubCaste,
                            Gotra = familyMember.Gotra,
                            LanguagesSpoken = familyMember.LanguagesSpoken,
                            IsDeceased = familyMember.IsDeceased,
                            IsPrimaryContact = familyMember.IsPrimaryContact,
                            ProfilePictureUrl = familyMember.ProfilePictureUrl,
                            MarriedTo = familyMember.MarriedTo,
                            MarriedToSonOrDaughterOf = familyMember.MarriedToSonOrDaughterOf,
                            SpouseAddress = familyMember.SpouseAddress,
                            FamilyType = familyMember.FamilyType.GetDisplayName(true),
                            RelativeOrder = familyMember.RelativeOrder.GetDisplayName(true),
                            Prefix = familyMember.NamePrefixId > 0 ? familyMember.NamePrefix.GetDisplayName() : ""
                        });
                    }
                }

                familyMemberItems.Add(relation.Relation, item);
            }

            var user = await _userService.GetUserByIdAsync(profile.RelationshipManagerUserId);
            var relationshipManagerUser = user == null
                ? null
                : new RelationshipManagerUserItem()
                {
                    Name = await _userService.GetUserFullNameAsync(user),
                    Phone = user.Customer.Phone,
                    Email = user.Customer.Email,
                };

            var occupationAndBusiness = await _occupationAndBusinessService.GetOccupationAndBusinessByIdAsync(profile.OccupationAndBusinessId);
            var occupationAndBusinessItem = await MapOccupationAndBusinessAsync(occupationAndBusiness);

            var familyOccupationAndBusiness = await _occupationAndBusinessService.GetOccupationAndBusinessByIdAsync(profile.FamilyOccupationAndBusinessId);
            var familyOccupationAndBusinessItem = await MapOccupationAndBusinessAsync(familyOccupationAndBusiness);
            var source = new BioDataSource
            {
                ProfileId = profile.Id.ToString(),
                FormatProfileId = await _profileService.FormatProfileDetailsAsync(profileId: profile.Id, includeProfileCategoryName: false),
                PicturePaths = picturePaths,
                Manager = relationshipManagerUser,
                FullName = await _profileService.GetProfileFullNameAsync(profile),
                DateOfBirth = profile.Customer.DateOfBirth?.ToString("dd MMM yyyy"), // Formatting as per example
                TimeOfBirth = profile.TimeOfBirth?.ToString("hh:mm:ss tt"),
                Age = profile.Customer.DateOfBirth.CalculateAge(),
                BirthPlace = await GetFormattedCityAsync(profile.BirthPlaceId),// (await _cityService.GetCityByIdAsync(profile.BirthPlaceId))?.Name,
                Height = profile.Height.GetDisplayName(true),
                Weight = profile.Weight > 0 ? $"{profile.Weight} kg" : string.Empty,
                MaritalStatus = profile.ProfileMaritalStatus == ProfileMaritalStatus.Divorced || profile.ProfileMaritalStatus == ProfileMaritalStatus.Widowed ? profile.ProfileMaritalStatus.GetDisplayName(true) : string.Empty,
                SkinTone = profile.SkinTone.GetDisplayName(true),
                BodyType = profile.BodyType.GetDisplayName(true),
                //Complexion = profile.SkinTone.GetDisplayName(true), // Assuming Complexion is derived from SkinTone
                ZodiacSign = profile.ZodiacSign.GetDisplayName(true),
                Community = (await _communityService.GetCommunityByIdAsync(profile.CommunityId))?.Name,
                GotraSakha = (await _gotraService.GetGotraByIdAsync(profile.GotraId))?.Name,
                FoodHabits = profile.FoodHabit.GetDisplayName(true),
                PresentCity = (await _cityService.GetCityByIdAsync(profile.CityId))?.Name,
                NativePlace = profile.NativePlace,
                Hobbies = string.Join(", ", (await _profileService.GetProfileHobbyIdsAsync(profile))
                    .Select(id => _hobbyService.GetHobbyNameByIdAsync(id).Result)), // Fetching and joining hobby names
                //AboutMe = _htmlFormatter.StripTags(_htmlFormatter.ConvertHtmlToPlainText(profile.AboutMe, decode: true)),
                AboutMe = profile.AboutMe,
                OccupationAndBusiness = occupationAndBusinessItem,
                AdditionalInformation = string.Empty,// profile.AdditionalInformation, // Custom field if exists
                FirmOccupation = (await _occupationService.GetOccupationByIdAsync(profile.OccupationId))?.Name,
                Mobile = string.Empty,// profile.MobileNumber,
                Email = profile.Customer.Email,
                FamilyMembers = familyMemberItems,
                AboutFamily = profile.AboutFamily,
                FamilyOccupationAndBusiness = familyOccupationAndBusinessItem,
                EducationAdditionalInfo = profile.EducationAdditionalInfo,
                Residence = await GetFormattedResidenceAsync(profile),
                MedicalHistory = await _medicalHistoryService.GetMedicalHistoryByIdAsync(profile.MedicalHistoryId),
                Language = language,
                PageSize = PageSizes.A4,
                Address1 = profile.Address1,
                Address2 = profile.Address2
            };

            var educations = await _educationService.GetAllEducationsAsync(profileId: profile.Id);

            source.Educations = await educations.SelectAwait(async e => new EducationItem
            {
                Qualification = (await _qualificationService.GetQualificationByIdAsync(e.QualificationId))?.Name,
                Location = e.Location,
                College = e.College,
                Year = e.Year,
                Remarks = e.Remarks,
            }).ToListAsync();


            //generate PDF
            await using var pdfStream = new MemoryStream();
            new BioDataDocument(source, _localizationService, _fileProvider)
                .GeneratePdf(pdfStream);

            //copy final PDF to provided stream
            pdfStream.Position = 0;
            await pdfStream.CopyToAsync(stream);
        }

        protected virtual string GetBioDataPdfFilePath()
        {
            return _fileProvider.MapPath(PageBaaSDefaults.BioDataPdfFilePath);
        }

        public virtual async Task<string> SaveBioDataPdfToDiskAsync(Profile profile, Language language = null)
        {
            if (profile == null)
                throw new ArgumentNullException(nameof(profile));

            //language info
            language ??= await _languageService.GetLanguageByIdAsync(profile.Customer?.LanguageId ?? 0);
            if (language?.Published != true)
                language = await _workContext.GetWorkingLanguageAsync();

            // Prepare the storage path and file name
            var storagePath = GetBioDataPdfFilePath();

            //prepare PDF file name and path
            var fileName = $"profile_{profile.Customer.CustomerGuid}_{CommonHelper.GenerateRandomDigitCode(4)}.pdf";
            var filePath = _fileProvider.Combine(storagePath, fileName);

            //create or overwrite the file
            await using var fileStream = new FileStream(filePath, FileMode.Create);

            //generate the PDF
            await PrintBioDataToPdfAsync(fileStream, profile, language);

            // Validate the saved file
            if (!_fileProvider.FileExists(filePath))
                throw new FileNotFoundException("The BioData PDF could not be saved properly.", filePath);

            // Check for an existing file and delete it if it exists
            if (!string.IsNullOrEmpty(profile.BioDataFileName))
            {
                var oldFilePath = _fileProvider.Combine(storagePath, profile.BioDataFileName);
                if (_fileProvider.FileExists(oldFilePath))
                {
                    _fileProvider.DeleteFile(oldFilePath); // Delete the old file
                }
            }

            // Save the file name to the profile table
            profile.BioDataFileName = fileName;
            await _profileService.UpdateProfileAsync(profile);

            //return path to generated PDF file
            return filePath;
        }


        public async Task<string> GetBioDataPdfFromDiskAsync(Profile profile)
        {
            if (profile == null)
                throw new ArgumentNullException(nameof(profile));

            // Ensure the file storage path
            var storagePath = GetBioDataPdfFilePath();

            // Check if a file name is already saved in the profile
            if (!string.IsNullOrEmpty(profile.BioDataFileName))
            {
                var existingFilePath = _fileProvider.Combine(storagePath, profile.BioDataFileName);

                // Return the existing file path if it exists
                if (_fileProvider.FileExists(existingFilePath))
                    return existingFilePath;
            }

            // Generate or retrieve the PDF path
            var filePath = await SaveBioDataPdfToDiskAsync(profile);

            // Validate the generated file
            if (!_fileProvider.FileExists(filePath))
                throw new FileNotFoundException("The generated BioData PDF file could not be found.", filePath);

            return filePath;
        }
    }
}