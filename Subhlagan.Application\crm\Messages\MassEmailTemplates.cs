using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Subhlagan.Application.crm.Messages
{

    public class MassEmailTemplatesData
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public bool IsActive { get; set; }
        public string Category { get; set; }
    }
    public class MassEmailTemplates
    {
        private static readonly object _lock = new object();
        private static List<MassEmailTemplatesData> _massEmailTemplateList;
        private const string TemplatesFolderPath = "App_Data/MassEmailTemplates";

        public static List<MassEmailTemplatesData> MassEmailTemplateList
        {
            get => GetMassEmailTemplates();
            set => _massEmailTemplateList = value;
        }

        public static List<MassEmailTemplatesData> GetMassEmailTemplates()
        {
            if (_massEmailTemplateList == null)
            {
                lock (_lock)
                {
                    if (_massEmailTemplateList == null)
                    {
                        _massEmailTemplateList = LoadTemplatesFromFiles();
                    }
                }
            }
            return _massEmailTemplateList;
        }

        private static List<MassEmailTemplatesData> LoadTemplatesFromFiles()
        {
            try
            {
                string folderPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, TemplatesFolderPath);

                if (System.IO.Directory.Exists(folderPath))
                {
                    var templates = new List<MassEmailTemplatesData>();
                    int id = 1;

                    // Get all HTML files in the templates directory
                    var templateFiles = System.IO.Directory.GetFiles(folderPath, "*.html", System.IO.SearchOption.AllDirectories);

                    foreach (var file in templateFiles)
                    {
                        try
                        {
                            // Use filename as subject (without extension)
                            string fileName = Path.GetFileNameWithoutExtension(file);

                            // Read the HTML content
                            string body = File.ReadAllText(file);

                            // Get category from subfolder if exists
                            string category = "MassEmail";
                            string directory = Path.GetDirectoryName(file);
                            if (directory != folderPath)
                            {
                                // Get the subfolder name as category
                                string relativePath = directory.Substring(folderPath.Length).TrimStart('\\', '/');
                                string[] pathParts = relativePath.Split('\\', '/');
                                if (pathParts.Length > 0)
                                {
                                    category = pathParts[0];
                                }
                            }

                            templates.Add(new MassEmailTemplatesData
                            {
                                Id = id++,
                                Name = fileName,
                                Subject = fileName,
                                Body = body,
                                IsActive = true,
                                Category = category
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error loading template file {file}: {ex.Message}");
                        }
                    }

                    return templates.Count > 0 ? templates : GetDefaultTemplates();
                }

                // Fall back to default templates if directory doesn't exist
                return GetDefaultTemplates();
            }
            catch (Exception ex)
            {
                // Log exception
                Console.WriteLine($"Error loading mass email templates: {ex.Message}");
                return GetDefaultTemplates();
            }
        }

        private static List<MassEmailTemplatesData> GetDefaultTemplates()
        {

            return new List<MassEmailTemplatesData>();
        }

    }
}