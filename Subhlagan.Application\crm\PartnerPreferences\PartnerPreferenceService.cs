﻿using Subhlagan.Core;
using Subhlagan.Core.Caching;
using Subhlagan.Core.Domain;
using Subhlagan.Infrastructure;
using Subhlagan.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Subhlagan.Application.PartnerPreferences
{
    public partial class PartnerPreferenceService : IPartnerPreferenceService
    {
        #region Fields

        private readonly EnhancedEntityRepository<PartnerPreference> _partnerPreferenceRepository;
        private readonly IStaticCacheManager _staticCacheManager;

        #endregion

        #region Ctor

        public PartnerPreferenceService( EnhancedEntityRepository<PartnerPreference> partnerPreferenceRepository, IStaticCacheManager staticCacheManager)
        {
            _partnerPreferenceRepository = partnerPreferenceRepository;
            _staticCacheManager = staticCacheManager;

        }

        #endregion

        #region Methods

        public virtual async Task InsertPartnerPreferenceAsync(PartnerPreference preference)
        {
            if (preference == null)
                throw new ArgumentNullException(nameof(preference));

            await _partnerPreferenceRepository.InsertAsync(preference);
        }

        public virtual async Task UpdatePartnerPreferenceAsync(PartnerPreference preference)
        {
            if (preference == null)
                throw new ArgumentNullException(nameof(preference));

            await _partnerPreferenceRepository.UpdateAsync(preference);
        }

        public virtual async Task DeletePartnerPreferenceAsync(PartnerPreference preference)
        {
            if (preference == null)
                throw new ArgumentNullException(nameof(preference));

            await _partnerPreferenceRepository.DeleteAsync(preference);
        }

        public virtual async Task<PartnerPreference> GetPartnerPreferenceByIdAsync(int preferenceId)
        {
            return await _partnerPreferenceRepository.GetByIdAsync(preferenceId, cache => default);
        }

        public virtual async Task<IPagedList<PartnerPreference>> GetAllPartnerPreferencesAsync(
            int? fromAge = null, int? toAge = null, bool? published = null,
            int pageIndex = 0, int pageSize = int.MaxValue, bool getOnlyTotalCount = false)
        {
            var preferences = await _partnerPreferenceRepository.GetAllPagedAsync(query =>
            {
                if (fromAge.HasValue)
                    query = query.Where(p => p.FromAge >= fromAge.Value);

                if (toAge.HasValue)
                    query = query.Where(p => p.ToAge <= toAge.Value);

                return query;
            }, pageIndex, pageSize, getOnlyTotalCount);

            return preferences;
        }

        public virtual async Task<PartnerPreference> GetPartnerPreferenceByProfileIdAsync(int profileId, bool getFromDatabase = false)
        {
            if (profileId <= 0)
                return null;

            if (getFromDatabase)
                return await _partnerPreferenceRepository.Table
                    .Where(p => p.ProfileId == profileId)
                    .SingleOrDefaultAsync();

            var cacheKey = _staticCacheManager.PrepareKeyForDefaultCache(PageBaaSDefaults.PartnerPreferenceByProfileIdCacheKey, profileId);

            return await _staticCacheManager.GetAsync(cacheKey, async () =>
            {
                return await _partnerPreferenceRepository.Table
                    .Where(p => p.ProfileId == profileId)
                    .SingleOrDefaultAsync();
            });
        }


        #endregion
    }

}
