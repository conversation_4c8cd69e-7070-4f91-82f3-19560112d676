﻿using System.Threading.Tasks;
using Subhlagan.Core.Domain.Common;
using Subhlagan.Application.Caching;
using Subhlagan.Application.Customers;

namespace Subhlagan.Application.Common.Caching
{
    /// <summary>
    /// Represents a address cache event consumer
    /// </summary>
    public partial class AddressCacheEventConsumer : CacheEventConsumer<Address>
    {
        /// <summary>
        /// Clear cache data
        /// </summary>
        /// <param name="entity">Entity</param>
        /// <returns>A task that represents the asynchronous operation</returns>
        protected override async Task ClearCacheAsync(Address entity)
        {
            await RemoveByPrefixAsync(KsCustomerServicesDefaults.CustomerAddressesPrefix);
        }
    }
}
