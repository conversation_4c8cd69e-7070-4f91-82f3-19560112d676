﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Subhlagan.Core.Domain;

namespace Subhlagan.Application.ActionStatuses
{
    public interface IActionStatusService
    {
        Task InsertActionStatusAsync(ActionStatus actionStatus);
        Task UpdateActionStatusAsync(ActionStatus actionStatus);
        Task DeleteActionStatusAsync(ActionStatus actionStatus);
        Task<ActionStatus> GetActionStatusByIdAsync(int actionStatusId);
        Task<IList<ActionStatus>> GetAllActionStatusesAsync(bool showHidden = false);
        Task<string> GetActionStatusNameByIdAsync(int actionStatusId);
        Task<bool> IsNoAnswerActionStatus(int actionStatusId);
        Task<ActionStatus> GetActionStatusBySystemNameAsync(string systemName);
    }
}
