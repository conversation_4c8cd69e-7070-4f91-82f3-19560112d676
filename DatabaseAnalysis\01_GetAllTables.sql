-- =====================================================================
-- Database Table Inventory Script for SubhLaganNew_v1
-- Purpose: Extract complete list of all tables with basic information
-- =====================================================================

USE SubhLaganNew_v1;

SELECT 
    SCHEMA_NAME(t.schema_id) AS SchemaName,
    t.name AS TableName,
    t.object_id AS TableObjectId,
    t.create_date AS CreatedDate,
    t.modify_date AS ModifiedDate,
    t.type_desc AS TableType,
    CASE 
        WHEN t.is_ms_shipped = 1 THEN 'System Table'
        ELSE 'User Table'
    END AS TableCategory,
    -- Get row count for each table
    ISNULL(p.rows, 0) AS RowCount,
    -- Get table size information
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS TotalSizeMB,
    CAST(ROUND(((SUM(a.used_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS UsedSizeMB,
    CAST(ROUND(((SUM(a.data_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS DataSizeMB
FROM 
    sys.tables t
    INNER JOIN sys.indexes i ON t.object_id = i.object_id
    INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
    INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
    LEFT OUTER JOIN sys.schemas s ON t.schema_id = s.schema_id
WHERE 
    t.name NOT LIKE 'dt%' 
    AND t.name NOT LIKE 'MS%'
    AND t.name NOT LIKE 'sys%'
    AND t.is_ms_shipped = 0
    AND i.object_id > 255
GROUP BY 
    t.schema_id, t.name, t.object_id, t.create_date, t.modify_date, t.type_desc, t.is_ms_shipped, p.rows
ORDER BY 
    SchemaName, TableName;

-- Also get a simple list for easy processing
SELECT 
    SCHEMA_NAME(schema_id) + '.' + name AS FullTableName,
    name AS TableName
FROM sys.tables 
WHERE is_ms_shipped = 0
ORDER BY name;